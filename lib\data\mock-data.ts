import { Student, Subject, TimePeriod, AttendanceRecord } from "@/lib/types/scanner"

// Enhanced mock student data with Philippine names and Grade 7-12 structure
export const mockStudents: Student[] = [
  // Grade 7 Students
  {
    id: "STU001",
    name: "<PERSON>",
    email: "<EMAIL>",
    course: "Junior High School",
    year: "1st Year",
    section: "Grade 7-A",
    grade: "7",
    status: "Active",
    photo: "https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU001_2025"
  },
  {
    id: "STU002",
    name: "<PERSON>",
    email: "<EMAIL>",
    course: "Junior High School",
    year: "1st Year",
    section: "Grade 7-B",
    grade: "7",
    status: "Active",
    photo: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU002_2025"
  },
  {
    id: "STU003",
    name: "<PERSON>",
    email: "<EMAIL>",
    course: "Junior High School",
    year: "1st Year",
    section: "Grade 7-A",
    grade: "7",
    status: "Active",
    photo: "https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU003_2025"
  },
  // Grade 8 Students
  {
    id: "STU004",
    name: "Jose Miguel Rodriguez",
    email: "<EMAIL>",
    course: "Junior High School",
    year: "2nd Year",
    section: "Grade 8-A",
    grade: "8",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU004_2025"
  },
  {
    id: "STU005",
    name: "Princess Mae Garcia",
    email: "<EMAIL>",
    course: "Junior High School",
    year: "2nd Year",
    section: "Grade 8-B",
    grade: "8",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU005_2025"
  },
  // Grade 9 Students
  {
    id: "STU006",
    name: "Mark Anthony Villanueva",
    email: "<EMAIL>",
    course: "Junior High School",
    year: "3rd Year",
    section: "Grade 9-A",
    grade: "9",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU006_2025"
  },
  {
    id: "STU007",
    name: "Angelica Mae Torres",
    email: "<EMAIL>",
    course: "Junior High School",
    year: "3rd Year",
    section: "Grade 9-B",
    grade: "9",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU007_2025"
  },
  // Grade 10 Students
  {
    id: "STU008",
    name: "Christian Paul Mendoza",
    email: "<EMAIL>",
    course: "Junior High School",
    year: "4th Year",
    section: "Grade 10-A",
    grade: "10",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU008_2025"
  },
  {
    id: "STU009",
    name: "Kimberly Rose Flores",
    email: "<EMAIL>",
    course: "Junior High School",
    year: "4th Year",
    section: "Grade 10-B",
    grade: "10",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU009_2025"
  },
  // Grade 11 Students (Senior High School)
  {
    id: "STU010",
    name: "John Michael Cruz",
    email: "<EMAIL>",
    course: "Information and Communications Technology",
    year: "1st Year Senior High",
    section: "ICT 11-A",
    grade: "11",
    status: "Active",
    photo: "https://images.unsplash.com/photo-*************-f4e0f30006d5?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU010_2025"
  },
  {
    id: "STU011",
    name: "Mary Grace Aquino",
    email: "<EMAIL>",
    course: "Accountancy, Business and Management",
    year: "1st Year Senior High",
    section: "ABM 11-A",
    grade: "11",
    status: "Active",
    photo: "https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU011_2025"
  },
  // Grade 12 Students (Senior High School)
  {
    id: "STU012",
    name: "Ryan James Bautista",
    email: "<EMAIL>",
    course: "Information and Communications Technology",
    year: "2nd Year Senior High",
    section: "ICT 12-A",
    grade: "12",
    status: "Active",
    photo: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU012_2025"
  },
  {
    id: "STU013",
    name: "Sarah Jane Morales",
    email: "<EMAIL>",
    course: "Humanities and Social Sciences",
    year: "2nd Year Senior High",
    section: "HUMSS 12-A",
    grade: "12",
    status: "Active",
    photo: "https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_STU013_2025"
  }
]

// Mock subjects
export const mockSubjects: Subject[] = [
  {
    id: "SUBJ001",
    name: "Programming Fundamentals",
    code: "IT101",
    instructor: "Prof. Martinez",
    schedule: [
      { day: "Monday", startTime: "08:00", endTime: "10:00" },
      { day: "Wednesday", startTime: "08:00", endTime: "10:00" },
      { day: "Friday", startTime: "08:00", endTime: "10:00" }
    ]
  },
  {
    id: "SUBJ002",
    name: "Database Management",
    code: "IT201",
    instructor: "Prof. Rodriguez",
    schedule: [
      { day: "Tuesday", startTime: "10:00", endTime: "12:00" },
      { day: "Thursday", startTime: "10:00", endTime: "12:00" }
    ]
  },
  {
    id: "SUBJ003",
    name: "Web Development",
    code: "IT301",
    instructor: "Prof. Santos",
    schedule: [
      { day: "Monday", startTime: "13:00", endTime: "15:00" },
      { day: "Wednesday", startTime: "13:00", endTime: "15:00" }
    ]
  },
  {
    id: "SUBJ004",
    name: "Data Structures",
    code: "CS201",
    instructor: "Prof. Reyes",
    schedule: [
      { day: "Tuesday", startTime: "08:00", endTime: "10:00" },
      { day: "Thursday", startTime: "08:00", endTime: "10:00" }
    ]
  },
  {
    id: "SUBJ005",
    name: "Software Engineering",
    code: "CS301",
    instructor: "Prof. Cruz",
    schedule: [
      { day: "Monday", startTime: "15:00", endTime: "17:00" },
      { day: "Friday", startTime: "15:00", endTime: "17:00" }
    ]
  }
]

// Mock time periods
export const mockTimePeriods: TimePeriod[] = [
  {
    id: "PERIOD001",
    name: "1st Period",
    startTime: "08:00",
    endTime: "10:00",
    type: "morning"
  },
  {
    id: "PERIOD002",
    name: "2nd Period",
    startTime: "10:00",
    endTime: "12:00",
    type: "morning"
  },
  {
    id: "PERIOD003",
    name: "3rd Period",
    startTime: "13:00",
    endTime: "15:00",
    type: "afternoon"
  },
  {
    id: "PERIOD004",
    name: "4th Period",
    startTime: "15:00",
    endTime: "17:00",
    type: "afternoon"
  },
  {
    id: "PERIOD005",
    name: "Evening Class",
    startTime: "18:00",
    endTime: "20:00",
    type: "evening"
  }
]

// Enhanced mock attendance records with realistic patterns
export const mockAttendanceRecords: AttendanceRecord[] = [
  // Today's attendance records
  {
    id: "ATT001",
    studentId: "STU001",
    studentName: "Maria Cristina Santos",
    course: "Junior High School",
    checkIn: "7:45 AM",
    checkOut: "4:30 PM",
    date: new Date().toISOString().split('T')[0],
    status: "Present",
    type: "gate",
    timestamp: new Date(new Date().setHours(7, 45, 0))
  },
  {
    id: "ATT002",
    studentId: "STU002",
    studentName: "Juan Carlos Dela Cruz",
    course: "Junior High School",
    checkIn: "7:50 AM",
    checkOut: "4:25 PM",
    date: new Date().toISOString().split('T')[0],
    status: "Present",
    type: "gate",
    timestamp: new Date(new Date().setHours(7, 50, 0))
  },
  {
    id: "ATT003",
    studentId: "STU003",
    studentName: "Ana Marie Reyes",
    course: "Junior High School",
    checkIn: "8:15 AM",
    checkOut: "4:35 PM",
    date: new Date().toISOString().split('T')[0],
    status: "Late",
    type: "gate",
    timestamp: new Date(new Date().setHours(8, 15, 0))
  },
  {
    id: "ATT004",
    studentId: "STU004",
    studentName: "Jose Miguel Rodriguez",
    course: "Junior High School",
    date: new Date().toISOString().split('T')[0],
    status: "Absent",
    type: "subject",
    subject: "Mathematics",
    period: "1st Period",
    timestamp: new Date(new Date().setHours(8, 0, 0))
  },
  {
    id: "ATT005",
    studentId: "STU005",
    studentName: "Princess Mae Garcia",
    course: "Junior High School",
    checkIn: "7:55 AM",
    checkOut: "4:20 PM",
    date: new Date().toISOString().split('T')[0],
    status: "Present",
    type: "gate",
    timestamp: new Date(new Date().setHours(7, 55, 0))
  },
  {
    id: "ATT006",
    studentId: "STU010",
    studentName: "John Michael Cruz",
    course: "Information and Communications Technology",
    checkIn: "7:40 AM",
    checkOut: "5:00 PM",
    date: new Date().toISOString().split('T')[0],
    status: "Present",
    type: "gate",
    timestamp: new Date(new Date().setHours(7, 40, 0))
  },
  {
    id: "ATT007",
    studentId: "STU012",
    studentName: "Ryan James Bautista",
    course: "Information and Communications Technology",
    checkIn: "8:10 AM",
    checkOut: "5:05 PM",
    date: new Date().toISOString().split('T')[0],
    status: "Late",
    type: "gate",
    timestamp: new Date(new Date().setHours(8, 10, 0))
  }
]

// Dashboard statistics data
export const mockDashboardStats = {
  totalStudents: 1234,
  presentToday: 1105,
  lateToday: 23,
  absentToday: 106,
  attendanceRate: 89.5,
  weeklyTrend: [
    { day: 'Mon', present: 1150, late: 15, absent: 69 },
    { day: 'Tue', present: 1120, late: 28, absent: 86 },
    { day: 'Wed', present: 1105, late: 23, absent: 106 },
    { day: 'Thu', present: 1140, late: 18, absent: 76 },
    { day: 'Fri', present: 1095, late: 35, absent: 104 }
  ],
  gradeBreakdown: [
    { grade: '7', total: 180, present: 165, late: 3, absent: 12 },
    { grade: '8', total: 175, present: 158, late: 4, absent: 13 },
    { grade: '9', total: 170, present: 155, late: 2, absent: 13 },
    { grade: '10', total: 165, present: 148, late: 5, absent: 12 },
    { grade: '11', total: 272, present: 245, late: 6, absent: 21 },
    { grade: '12', total: 272, present: 234, late: 3, absent: 35 }
  ]
}

// Recent activity feed
export const mockRecentActivity = [
  {
    id: "ACT001",
    type: "scan",
    studentName: "Maria Cristina Santos",
    action: "Check In",
    time: "2 minutes ago",
    status: "success"
  },
  {
    id: "ACT002",
    type: "scan",
    studentName: "Juan Carlos Dela Cruz",
    action: "Check Out",
    time: "5 minutes ago",
    status: "success"
  },
  {
    id: "ACT003",
    type: "alert",
    studentName: "Jose Miguel Rodriguez",
    action: "Marked Absent",
    time: "15 minutes ago",
    status: "warning"
  },
  {
    id: "ACT004",
    type: "scan",
    studentName: "Princess Mae Garcia",
    action: "Late Arrival",
    time: "25 minutes ago",
    status: "warning"
  }
]

// Helper functions
export function findStudentById(id: string): Student | undefined {
  return mockStudents.find(student => student.id === id)
}

export function findStudentByQRCode(qrCode: string): Student | undefined {
  return mockStudents.find(student => student.qrCode === qrCode)
}

export function findSubjectById(id: string): Subject | undefined {
  return mockSubjects.find(subject => subject.id === id)
}

export function findPeriodById(id: string): TimePeriod | undefined {
  return mockTimePeriods.find(period => period.id === id)
}

export function getStudentAttendanceRecords(studentId: string): AttendanceRecord[] {
  return mockAttendanceRecords.filter(record => record.studentId === studentId)
}

export function getTodayAttendanceRecord(studentId: string): AttendanceRecord | undefined {
  const today = new Date().toISOString().split('T')[0]
  return mockAttendanceRecords.find(record => 
    record.studentId === studentId && record.date === today
  )
}
