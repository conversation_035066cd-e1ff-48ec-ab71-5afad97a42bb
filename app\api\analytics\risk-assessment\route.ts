import { NextRequest, NextResponse } from "next/server"
import { 
  mockRiskAssessments, 
  getStudentRiskAssessment,
  getRiskAssessmentsByLevel 
} from "@/lib/data/analytics-mock-data"
import { RiskAssessmentEngine, RecommendationEngine } from "@/lib/utils/ai-insights"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const riskLevel = searchParams.get('riskLevel')
    const includeInterventions = searchParams.get('includeInterventions') === 'true'
    
    if (studentId) {
      // Get specific student risk assessment
      const assessment = getStudentRiskAssessment(studentId)
      
      if (!assessment) {
        return NextResponse.json({
          success: false,
          error: "Student risk assessment not found"
        }, { status: 404 })
      }
      
      return NextResponse.json({
        success: true,
        data: assessment,
        message: "Student risk assessment retrieved successfully"
      })
    }
    
    if (riskLevel) {
      // Get students by risk level
      const assessments = getRiskAssessmentsByLevel(riskLevel)
      
      return NextResponse.json({
        success: true,
        data: assessments,
        message: `${riskLevel} risk students retrieved successfully`
      })
    }
    
    // Get all risk assessments with summary
    const allAssessments = mockRiskAssessments
    
    // Calculate summary statistics
    const summary = {
      totalStudents: allAssessments.length,
      riskDistribution: {
        low: allAssessments.filter(a => a.riskLevel === 'low').length,
        medium: allAssessments.filter(a => a.riskLevel === 'medium').length,
        high: allAssessments.filter(a => a.riskLevel === 'high').length,
        critical: allAssessments.filter(a => a.riskLevel === 'critical').length
      },
      averageRiskScore: allAssessments.reduce((sum, a) => sum + a.riskScore, 0) / allAssessments.length,
      averageDropoutProbability: allAssessments.reduce((sum, a) => sum + a.dropoutProbability, 0) / allAssessments.length,
      activeInterventions: allAssessments.reduce((sum, a) => 
        sum + a.interventions.filter(i => i.status === 'in_progress' || i.status === 'pending').length, 0
      ),
      completedInterventions: allAssessments.reduce((sum, a) => 
        sum + a.interventions.filter(i => i.status === 'completed').length, 0
      )
    }
    
    // Risk factors analysis
    const riskFactorAnalysis = allAssessments.reduce((acc, assessment) => {
      assessment.riskFactors.forEach(factor => {
        if (!acc[factor.type]) {
          acc[factor.type] = { count: 0, totalImpact: 0, severityDistribution: { low: 0, medium: 0, high: 0, critical: 0 } }
        }
        acc[factor.type].count++
        acc[factor.type].totalImpact += factor.impact
        acc[factor.type].severityDistribution[factor.severity]++
      })
      return acc
    }, {} as Record<string, any>)
    
    // Parent engagement analysis
    const parentEngagementStats = {
      averageEngagementScore: allAssessments.reduce((sum, a) => sum + a.parentEngagement.engagementScore, 0) / allAssessments.length,
      averageResponseRate: allAssessments.reduce((sum, a) => sum + a.parentEngagement.responseRate, 0) / allAssessments.length,
      averageMeetingAttendance: allAssessments.reduce((sum, a) => sum + a.parentEngagement.meetingAttendance, 0) / allAssessments.length,
      communicationMethodDistribution: allAssessments.reduce((acc, a) => {
        acc[a.parentEngagement.preferredMethod] = (acc[a.parentEngagement.preferredMethod] || 0) + 1
        return acc
      }, {} as Record<string, number>)
    }
    
    const responseData = {
      summary,
      assessments: includeInterventions ? allAssessments : allAssessments.map(a => ({
        ...a,
        interventions: a.interventions.length // Just count, not full details
      })),
      riskFactorAnalysis,
      parentEngagementStats,
      lastUpdated: new Date().toISOString()
    }
    
    return NextResponse.json({
      success: true,
      data: responseData,
      message: "Risk assessments retrieved successfully"
    })
    
  } catch (error) {
    console.error("Risk assessment retrieval error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to retrieve risk assessments"
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { studentId, forceRecalculation } = body
    
    if (!studentId) {
      return NextResponse.json({
        success: false,
        error: "Student ID is required"
      }, { status: 400 })
    }
    
    // In a real implementation, this would:
    // 1. Fetch student data and attendance records
    // 2. Calculate risk assessment using AI algorithms
    // 3. Generate intervention recommendations
    // 4. Save to database
    
    // For now, return mock calculation
    const existingAssessment = getStudentRiskAssessment(studentId)
    
    if (existingAssessment && !forceRecalculation) {
      return NextResponse.json({
        success: true,
        data: existingAssessment,
        message: "Existing risk assessment returned"
      })
    }
    
    // Simulate AI calculation
    const mockCalculatedAssessment = {
      studentId,
      riskScore: Math.floor(Math.random() * 100),
      riskLevel: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
      dropoutProbability: Math.random(),
      riskFactors: [],
      interventions: [],
      parentEngagement: {
        communicationFrequency: Math.random() * 5,
        responseRate: Math.random(),
        meetingAttendance: Math.random(),
        lastContact: new Date().toISOString(),
        preferredMethod: 'email' as any,
        engagementScore: Math.floor(Math.random() * 100)
      },
      lastAssessment: new Date().toISOString(),
      nextReview: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    }
    
    return NextResponse.json({
      success: true,
      data: mockCalculatedAssessment,
      message: "Risk assessment calculated successfully"
    })
    
  } catch (error) {
    console.error("Risk assessment calculation error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to calculate risk assessment"
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { studentId, interventions, notes } = body
    
    if (!studentId) {
      return NextResponse.json({
        success: false,
        error: "Student ID is required"
      }, { status: 400 })
    }
    
    // In a real implementation, this would update the risk assessment
    // with new interventions or notes
    
    return NextResponse.json({
      success: true,
      message: "Risk assessment updated successfully"
    })
    
  } catch (error) {
    console.error("Risk assessment update error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to update risk assessment"
    }, { status: 500 })
  }
}
