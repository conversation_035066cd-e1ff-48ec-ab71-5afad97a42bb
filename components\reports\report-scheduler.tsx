"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ReportSchedule, ReportType } from "@/lib/types/reports"
import { mockScheduledReports } from "@/lib/data/reports-mock-data"
import { 
  Clock, 
  Calendar, 
  Mail, 
  Play, 
  Pause, 
  Trash2, 
  <PERSON>, 
  MoreH<PERSON><PERSON><PERSON>,
  Plus,
  Settings
} from "lucide-react"
import { format } from "date-fns"
import { toast } from "sonner"

interface ReportSchedulerProps {
  className?: string
}

export function ReportScheduler({ className }: ReportSchedulerProps) {
  const [schedules, setSchedules] = useState<ReportSchedule[]>(mockScheduledReports)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingSchedule, setEditingSchedule] = useState<ReportSchedule | null>(null)

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    reportType: "SF2" as ReportType,
    frequency: "daily" as ReportSchedule['frequency'],
    time: "08:00",
    dayOfWeek: 1,
    dayOfMonth: 1,
    timezone: "Asia/Manila",
    recipients: [""],
    isActive: true
  })

  const handleCreateSchedule = () => {
    const newSchedule: ReportSchedule = {
      id: `SCH${Date.now()}`,
      reportConfigId: `CFG${Date.now()}`,
      name: formData.name,
      frequency: formData.frequency,
      time: formData.time,
      timezone: formData.timezone,
      isActive: formData.isActive,
      nextRun: calculateNextRun(formData.frequency, formData.time, formData.dayOfWeek, formData.dayOfMonth),
      recipients: formData.recipients.filter(email => email.trim() !== ""),
      createdBy: "current-user",
      createdAt: new Date().toISOString(),
      ...(formData.frequency === 'weekly' && { dayOfWeek: formData.dayOfWeek }),
      ...(formData.frequency === 'monthly' && { dayOfMonth: formData.dayOfMonth })
    }

    setSchedules([...schedules, newSchedule])
    setShowCreateForm(false)
    resetForm()
    toast.success("Report schedule created successfully")
  }

  const handleToggleSchedule = (id: string) => {
    setSchedules(schedules.map(schedule => 
      schedule.id === id 
        ? { ...schedule, isActive: !schedule.isActive }
        : schedule
    ))
    toast.success("Schedule status updated")
  }

  const handleDeleteSchedule = (id: string) => {
    setSchedules(schedules.filter(schedule => schedule.id !== id))
    toast.success("Schedule deleted successfully")
  }

  const handleRunNow = (id: string) => {
    const schedule = schedules.find(s => s.id === id)
    if (schedule) {
      toast.success(`Running ${schedule.name} now...`)
    }
  }

  const resetForm = () => {
    setFormData({
      name: "",
      reportType: "SF2",
      frequency: "daily",
      time: "08:00",
      dayOfWeek: 1,
      dayOfMonth: 1,
      timezone: "Asia/Manila",
      recipients: [""],
      isActive: true
    })
    setEditingSchedule(null)
  }

  const calculateNextRun = (
    frequency: ReportSchedule['frequency'], 
    time: string, 
    dayOfWeek?: number, 
    dayOfMonth?: number
  ): string => {
    const now = new Date()
    const [hours, minutes] = time.split(':').map(Number)
    
    let nextRun = new Date()
    nextRun.setHours(hours, minutes, 0, 0)
    
    switch (frequency) {
      case 'daily':
        if (nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 1)
        }
        break
      case 'weekly':
        const currentDay = nextRun.getDay()
        const daysUntilTarget = ((dayOfWeek || 1) - currentDay + 7) % 7
        if (daysUntilTarget === 0 && nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 7)
        } else {
          nextRun.setDate(nextRun.getDate() + daysUntilTarget)
        }
        break
      case 'monthly':
        nextRun.setDate(dayOfMonth || 1)
        if (nextRun <= now) {
          nextRun.setMonth(nextRun.getMonth() + 1)
        }
        break
      case 'quarterly':
        // Set to first day of next quarter
        const currentQuarter = Math.floor(nextRun.getMonth() / 3)
        nextRun.setMonth((currentQuarter + 1) * 3, 1)
        break
      case 'annually':
        nextRun.setMonth(0, 1) // January 1st
        if (nextRun <= now) {
          nextRun.setFullYear(nextRun.getFullYear() + 1)
        }
        break
    }
    
    return nextRun.toISOString()
  }

  const addRecipient = () => {
    setFormData({
      ...formData,
      recipients: [...formData.recipients, ""]
    })
  }

  const updateRecipient = (index: number, email: string) => {
    const newRecipients = [...formData.recipients]
    newRecipients[index] = email
    setFormData({
      ...formData,
      recipients: newRecipients
    })
  }

  const removeRecipient = (index: number) => {
    setFormData({
      ...formData,
      recipients: formData.recipients.filter((_, i) => i !== index)
    })
  }

  const getFrequencyLabel = (frequency: ReportSchedule['frequency']) => {
    const labels = {
      daily: "Daily",
      weekly: "Weekly", 
      monthly: "Monthly",
      quarterly: "Quarterly",
      annually: "Annually"
    }
    return labels[frequency]
  }

  const getStatusBadge = (schedule: ReportSchedule) => {
    if (!schedule.isActive) {
      return <Badge variant="secondary">Inactive</Badge>
    }
    
    const nextRun = new Date(schedule.nextRun)
    const now = new Date()
    const isOverdue = nextRun < now
    
    return (
      <Badge variant={isOverdue ? "destructive" : "default"}>
        {isOverdue ? "Overdue" : "Active"}
      </Badge>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Report Scheduler</h2>
          <p className="text-muted-foreground">
            Automate report generation and delivery
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          New Schedule
        </Button>
      </div>

      {/* Create/Edit Form */}
      {(showCreateForm || editingSchedule) && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingSchedule ? "Edit Schedule" : "Create New Schedule"}
            </CardTitle>
            <CardDescription>
              Set up automated report generation and delivery
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="schedule-name">Schedule Name</Label>
                <Input
                  id="schedule-name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="Daily SF2 Reports"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="report-type">Report Type</Label>
                <Select 
                  value={formData.reportType} 
                  onValueChange={(value) => setFormData({...formData, reportType: value as ReportType})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SF2">SF2 Daily Attendance</SelectItem>
                    <SelectItem value="SF4">SF4 Monthly Movement</SelectItem>
                    <SelectItem value="DAILY">Daily Summary</SelectItem>
                    <SelectItem value="WEEKLY">Weekly Summary</SelectItem>
                    <SelectItem value="MONTHLY">Monthly Summary</SelectItem>
                    <SelectItem value="CUSTOM">Custom Report</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Frequency Settings */}
            <div className="space-y-4">
              <Label>Schedule Frequency</Label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="frequency">Frequency</Label>
                  <Select 
                    value={formData.frequency} 
                    onValueChange={(value) => setFormData({...formData, frequency: value as ReportSchedule['frequency']})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="quarterly">Quarterly</SelectItem>
                      <SelectItem value="annually">Annually</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="time">Time</Label>
                  <Input
                    id="time"
                    type="time"
                    value={formData.time}
                    onChange={(e) => setFormData({...formData, time: e.target.value})}
                  />
                </div>
                
                {formData.frequency === 'weekly' && (
                  <div className="space-y-2">
                    <Label htmlFor="day-of-week">Day of Week</Label>
                    <Select 
                      value={formData.dayOfWeek.toString()} 
                      onValueChange={(value) => setFormData({...formData, dayOfWeek: parseInt(value)})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">Sunday</SelectItem>
                        <SelectItem value="1">Monday</SelectItem>
                        <SelectItem value="2">Tuesday</SelectItem>
                        <SelectItem value="3">Wednesday</SelectItem>
                        <SelectItem value="4">Thursday</SelectItem>
                        <SelectItem value="5">Friday</SelectItem>
                        <SelectItem value="6">Saturday</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                
                {formData.frequency === 'monthly' && (
                  <div className="space-y-2">
                    <Label htmlFor="day-of-month">Day of Month</Label>
                    <Input
                      id="day-of-month"
                      type="number"
                      min="1"
                      max="31"
                      value={formData.dayOfMonth}
                      onChange={(e) => setFormData({...formData, dayOfMonth: parseInt(e.target.value)})}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Recipients */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Email Recipients</Label>
                <Button variant="outline" size="sm" onClick={addRecipient}>
                  <Plus className="h-3 w-3 mr-1" />
                  Add Recipient
                </Button>
              </div>
              
              <div className="space-y-2">
                {formData.recipients.map((email, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Input
                      type="email"
                      value={email}
                      onChange={(e) => updateRecipient(index, e.target.value)}
                      placeholder="<EMAIL>"
                      className="flex-1"
                    />
                    {formData.recipients.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeRecipient(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Active Status */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is-active"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({...formData, isActive: checked as boolean})}
              />
              <Label htmlFor="is-active">
                Activate schedule immediately
              </Label>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              <Button onClick={handleCreateSchedule}>
                {editingSchedule ? "Update Schedule" : "Create Schedule"}
              </Button>
              <Button variant="outline" onClick={() => {
                setShowCreateForm(false)
                resetForm()
              }}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Schedules List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Scheduled Reports
          </CardTitle>
          <CardDescription>
            Manage your automated report schedules
          </CardDescription>
        </CardHeader>
        <CardContent>
          {schedules.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No scheduled reports</p>
              <p className="text-sm">Create your first automated report schedule</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Schedule Name</TableHead>
                    <TableHead>Frequency</TableHead>
                    <TableHead>Next Run</TableHead>
                    <TableHead>Recipients</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {schedules.map((schedule) => (
                    <TableRow key={schedule.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{schedule.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {schedule.time} ({schedule.timezone})
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {getFrequencyLabel(schedule.frequency)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {format(new Date(schedule.nextRun), "MMM dd, yyyy")}
                          <div className="text-xs text-muted-foreground">
                            {format(new Date(schedule.nextRun), "HH:mm")}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          <span className="text-sm">{schedule.recipients.length}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(schedule)}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRunNow(schedule.id)}
                            disabled={!schedule.isActive}
                          >
                            <Play className="h-4 w-4" />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleSchedule(schedule.id)}
                          >
                            {schedule.isActive ? (
                              <Pause className="h-4 w-4" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                          </Button>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => {
                                setEditingSchedule(schedule)
                                setFormData({
                                  name: schedule.name,
                                  reportType: "SF2", // Would need to get from reportConfigId
                                  frequency: schedule.frequency,
                                  time: schedule.time,
                                  dayOfWeek: schedule.dayOfWeek || 1,
                                  dayOfMonth: schedule.dayOfMonth || 1,
                                  timezone: schedule.timezone,
                                  recipients: schedule.recipients,
                                  isActive: schedule.isActive
                                })
                              }}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDeleteSchedule(schedule.id)}
                                className="text-destructive"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
