import { NextRequest, NextResponse } from "next/server"
import { findStudentById, findSubjectById, findPeriodById } from "@/lib/data/mock-data"
import { AttendanceResponse, AttendanceRecord, AttendanceAction } from "@/lib/types/scanner"

// In-memory storage for demo purposes
// In production, this would be stored in a database
let attendanceRecords: AttendanceRecord[] = []

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      studentId, 
      action, 
      subject, 
      period, 
      reason,
      offline = false,
      originalTimestamp 
    } = body

    if (!studentId || !action) {
      return NextResponse.json({
        success: false,
        error: "studentId and action are required"
      } as AttendanceResponse, { status: 400 })
    }

    // Validate student exists
    const student = findStudentById(studentId)
    if (!student) {
      return NextResponse.json({
        success: false,
        error: "Student not found"
      } as AttendanceResponse, { status: 404 })
    }

    // Validate subject if provided
    if (subject) {
      const subjectData = findSubjectById(subject)
      if (!subjectData) {
        return NextResponse.json({
          success: false,
          error: "Subject not found"
        } as AttendanceResponse, { status: 404 })
      }
    }

    // Validate period if provided
    if (period) {
      const periodData = findPeriodById(period)
      if (!periodData) {
        return NextResponse.json({
          success: false,
          error: "Period not found"
        } as AttendanceResponse, { status: 404 })
      }
    }

    // Create attendance record
    const timestamp = originalTimestamp ? new Date(originalTimestamp) : new Date()
    const attendanceRecord: AttendanceRecord = {
      id: `ATT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      studentId: student.id,
      studentName: student.name,
      course: student.course,
      date: timestamp.toISOString().split('T')[0],
      status: mapActionToStatus(action),
      type: determineType(action, subject),
      timestamp,
      subject,
      period
    }

    // Set check-in/check-out times for gate actions
    if (action === 'check-in') {
      attendanceRecord.checkIn = timestamp.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } else if (action === 'check-out') {
      attendanceRecord.checkOut = timestamp.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    }

    // Store the record
    attendanceRecords.push(attendanceRecord)

    // Log for debugging
    console.log(`Attendance recorded: ${student.name} - ${action}${offline ? ' (offline)' : ''}`)

    return NextResponse.json({
      success: true,
      data: attendanceRecord,
      message: `Attendance recorded successfully${offline ? ' (synced from offline)' : ''}`
    } as AttendanceResponse)

  } catch (error) {
    console.error("Attendance recording error:", error)
    return NextResponse.json({
      success: false,
      error: "Internal server error"
    } as AttendanceResponse, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const date = searchParams.get('date')

    let filteredRecords = attendanceRecords

    if (studentId) {
      filteredRecords = filteredRecords.filter(record => record.studentId === studentId)
    }

    if (date) {
      filteredRecords = filteredRecords.filter(record => record.date === date)
    }

    return NextResponse.json({
      success: true,
      data: filteredRecords,
      message: "Attendance records retrieved successfully"
    })

  } catch (error) {
    console.error("Attendance retrieval error:", error)
    return NextResponse.json({
      success: false,
      error: "Internal server error"
    }, { status: 500 })
  }
}

// Helper functions
function mapActionToStatus(action: AttendanceAction): 'Present' | 'Late' | 'Absent' {
  switch (action) {
    case 'present':
    case 'check-in':
    case 'check-out':
      return 'Present'
    case 'late':
      return 'Late'
    case 'absent':
      return 'Absent'
    default:
      return 'Present'
  }
}

function determineType(action: AttendanceAction, subject?: string): 'gate' | 'subject' {
  if (action === 'check-in' || action === 'check-out') {
    return 'gate'
  }
  return subject ? 'subject' : 'gate'
}
