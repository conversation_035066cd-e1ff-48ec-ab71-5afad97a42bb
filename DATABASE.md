# QRSAMS Database Schema Documentation

## Overview

QRSAMS (QR Code Student Attendance Management System) uses a comprehensive SQLite database with Prisma ORM, specifically designed for the Philippine Grade 7-12 education system with DepEd compliance.

## Database Architecture

### Core Models

#### 1. Student Model
**Purpose**: Core student information with DepEd compliance
- **Primary Key**: `id` (CUID)
- **Unique Fields**: `studentId` (DepEd format: 12-digit number)
- **Key Features**:
  - Complete student demographics
  - Guardian and emergency contact information
  - Address with Philippine location focus (Tanauan, Leyte)
  - QR code integration
  - Status tracking (Active, Inactive, Transferred, Graduated, Dropped)

#### 2. Teacher Model
**Purpose**: Faculty and staff information management
- **Primary Key**: `id` (CUID)
- **Unique Fields**: `employeeId`, `email`
- **Key Features**:
  - Professional role classification
  - Subject and grade level assignments (JSON format)
  - Employment status tracking
  - Contact information

#### 3. Subject Model
**Purpose**: Academic subjects and schedules
- **Primary Key**: `id` (CUID)
- **Unique Fields**: `subjectCode`
- **Key Features**:
  - Grade-level specific subjects
  - Teacher assignments
  - Schedule information (JSON format)
  - Credit system

#### 4. Attendance Model
**Purpose**: Core attendance tracking with multiple types
- **Primary Key**: `id` (CUID)
- **Unique Constraint**: `[studentId, date, attendanceType, subjectId]`
- **Key Features**:
  - Gate and subject-specific attendance
  - Time in/out tracking
  - Manual entry support
  - Scanner device tracking
  - Location information

### Analytics and AI Models

#### 5. AttendancePattern Model
**Purpose**: AI-powered attendance pattern analysis
- **Key Features**:
  - Weekly and monthly attendance rates
  - Risk score calculation (0-100)
  - AI predictions and insights (JSON format)
  - Consecutive absence tracking

#### 6. StudentRiskAssessment Model
**Purpose**: Comprehensive student risk evaluation
- **Key Features**:
  - Dropout probability calculation
  - Risk factor analysis (JSON format)
  - Parent engagement metrics
  - Intervention recommendations

#### 7. SMSLog Model
**Purpose**: SMS communication tracking
- **Key Features**:
  - Message delivery status
  - Cost tracking
  - Provider integration
  - Message type classification

### System Models

#### 8. SystemSettings Model
**Purpose**: Application configuration management
- **Key Features**:
  - Key-value configuration storage
  - Category-based organization
  - Description support

#### 9. AuditLog Model
**Purpose**: System activity tracking
- **Key Features**:
  - User action logging
  - Entity change tracking
  - IP address and user agent logging
  - JSON-based old/new value storage

#### 10. NotificationQueue Model
**Purpose**: Queued notification management
- **Key Features**:
  - Multi-channel delivery (SMS, email, push)
  - Priority-based queuing
  - Retry mechanism
  - Scheduling support

## Enums

### Student-Related
- `Gender`: MALE, FEMALE
- `GradeLevel`: GRADE_7 through GRADE_12
- `StudentStatus`: ACTIVE, INACTIVE, TRANSFERRED, GRADUATED, DROPPED
- `GuardianRelationship`: FATHER, MOTHER, GUARDIAN, GRANDPARENT, SIBLING, AUNT, UNCLE, OTHER

### Teacher-Related
- `TeacherRole`: TEACHER, DEPARTMENT_HEAD, GUIDANCE_COUNSELOR, ADMIN, PRINCIPAL, VICE_PRINCIPAL
- `EmployeeStatus`: ACTIVE, INACTIVE, RESIGNED, TERMINATED, RETIRED

### Attendance-Related
- `AttendanceType`: GATE, SUBJECT, EVENT, ASSEMBLY
- `AttendanceStatus`: PRESENT, ABSENT, LATE, EXCUSED, HALF_DAY

### Analytics-Related
- `RiskLevel`: LOW, MEDIUM, HIGH, CRITICAL

### Communication-Related
- `SMSStatus`: PENDING, SENT, DELIVERED, FAILED, EXPIRED
- `MessageType`: ATTENDANCE_ALERT, LATE_ARRIVAL, RISK_WARNING, GENERAL, EMERGENCY, PARENT_MEETING, ACADEMIC_UPDATE
- `NotificationPriority`: LOW, NORMAL, HIGH, URGENT
- `NotificationStatus`: PENDING, SCHEDULED, SENT, DELIVERED, FAILED, CANCELLED

## Database Operations

### Setup Commands
```bash
# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate

# Seed database with sample data
npm run db:seed

# Open Prisma Studio
npm run db:studio

# Reset database (development only)
npm run db:reset
```

### Key Relationships

1. **Student → Attendance**: One-to-many (cascade delete)
2. **Student → AttendancePattern**: One-to-one (cascade delete)
3. **Student → StudentRiskAssessment**: One-to-many (cascade delete)
4. **Student → SMSLog**: One-to-many (optional)
5. **Teacher → Subject**: One-to-many (optional)
6. **Teacher → Attendance**: One-to-many (scanner tracking)
7. **Subject → Attendance**: One-to-many (optional)

### Performance Optimizations

#### Indexes
- **Students**: `[gradeLevel, section]`, `[status]`, `[guardianPhone]`, `[municipality, province]`
- **Teachers**: `[role]`, `[status]`
- **Subjects**: `[gradeLevel]`, `[teacherId]`
- **Attendance**: `[date]`, `[studentId, date]`, `[status]`, `[attendanceType]`
- **SMSLog**: `[status]`, `[relatedStudentId]`, `[messageType]`, `[sentAt]`
- **NotificationQueue**: `[status]`, `[scheduledFor]`, `[recipientId, recipientType]`
- **AuditLog**: `[userId]`, `[action]`, `[entityType]`, `[createdAt]`

## Sample Data

The seed file creates:
- **300 students** across all grade levels (50 per grade)
- **10 teachers** including principal and guidance counselor
- **48 subjects** covering all grade levels
- **1,883 attendance records** for the last 30 days
- **50 attendance patterns** with AI analytics
- **20 SMS logs** with various message types
- **System settings** for school configuration

## Philippine Education Context

### DepEd Compliance Features
- 12-digit student ID format
- Grade 7-12 structure (K-12 system)
- Senior High School tracks (STEM, HUMSS, ABM, GAS, TVL)
- Philippine address format with barangay support
- Local phone number format (09XXXXXXXXX)

### Tanauan, Leyte Specific
- Default municipality: Tanauan
- Default province: Leyte
- 27 barangays included in seed data
- ZIP code: 6502

## API Integration

### Database Services
- `StudentService`: CRUD operations for students
- `TeacherService`: CRUD operations for teachers
- `AttendanceService`: Attendance recording and querying
- `SubjectService`: Subject management
- `SystemSettingsService`: Configuration management

### Type Safety
- Full TypeScript integration
- Prisma-generated types
- Legacy compatibility layer
- Form validation types

## Security Considerations

1. **Data Protection**: Student data is protected with proper relationships and cascade deletes
2. **Audit Trail**: All system actions are logged in AuditLog
3. **Access Control**: Teacher roles and permissions are defined
4. **Data Integrity**: Unique constraints and foreign key relationships
5. **Privacy**: Personal information is properly structured and protected

## Backup and Maintenance

### Regular Tasks
1. **Daily**: Automated attendance pattern analysis
2. **Weekly**: Risk assessment updates
3. **Monthly**: Database optimization and cleanup
4. **Quarterly**: Full database backup and archival

### Migration Strategy
- Use Prisma migrations for schema changes
- Maintain backward compatibility
- Test migrations on development data
- Document all schema changes

## Troubleshooting

### Common Issues
1. **Migration Failures**: Check schema syntax and relationships
2. **Seed Failures**: Verify data constraints and foreign keys
3. **Performance Issues**: Review indexes and query patterns
4. **Type Errors**: Ensure Prisma client is regenerated after schema changes

### Monitoring
- Track database size and growth
- Monitor query performance
- Review attendance pattern accuracy
- Validate SMS delivery rates
