import bcrypt from 'bcrypt'
import { PasswordValidation } from './types'

// Password security configuration
const SALT_ROUNDS = 12
const MIN_PASSWORD_LENGTH = 8
const MAX_PASSWORD_LENGTH = 128

// Password complexity requirements
const PASSWORD_REQUIREMENTS = {
  minLength: MIN_PASSWORD_LENGTH,
  maxLength: MAX_PASSWORD_LENGTH,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  minSpecialChars: 1,
  preventCommonPasswords: true,
  preventUserInfoInPassword: true
}

// Common weak passwords to reject
const COMMON_PASSWORDS = [
  'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
  'admin', 'letmein', 'welcome', 'monkey', '1234567890', 'password1',
  'admin123', 'root', 'toor', 'pass', 'test', 'guest', 'user', 'demo'
]

/**
 * Hash a password using bcrypt
 */
export async function hashPassword(password: string): Promise<string> {
  if (!password || password.length === 0) {
    throw new Error('Password cannot be empty')
  }
  
  if (password.length > MAX_PASSWORD_LENGTH) {
    throw new Error(`Password cannot exceed ${MAX_PASSWORD_LENGTH} characters`)
  }
  
  return await bcrypt.hash(password, SALT_ROUNDS)
}

/**
 * Verify a password against its hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  if (!password || !hash) {
    return false
  }
  
  try {
    return await bcrypt.compare(password, hash)
  } catch (error) {
    console.error('Password verification error:', error)
    return false
  }
}

/**
 * Validate password strength and complexity
 */
export function validatePassword(
  password: string, 
  userInfo?: { 
    username?: string
    email?: string
    firstName?: string
    lastName?: string
  }
): PasswordValidation {
  const errors: string[] = []
  let score = 0
  
  // Check length
  if (!password || password.length < PASSWORD_REQUIREMENTS.minLength) {
    errors.push(`Password must be at least ${PASSWORD_REQUIREMENTS.minLength} characters long`)
  } else {
    score += 1
  }
  
  if (password.length > PASSWORD_REQUIREMENTS.maxLength) {
    errors.push(`Password cannot exceed ${PASSWORD_REQUIREMENTS.maxLength} characters`)
  }
  
  // Check for uppercase letters
  if (PASSWORD_REQUIREMENTS.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  } else if (/[A-Z]/.test(password)) {
    score += 1
  }
  
  // Check for lowercase letters
  if (PASSWORD_REQUIREMENTS.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  } else if (/[a-z]/.test(password)) {
    score += 1
  }
  
  // Check for numbers
  if (PASSWORD_REQUIREMENTS.requireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  } else if (/\d/.test(password)) {
    score += 1
  }
  
  // Check for special characters
  const specialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/g
  const specialCharCount = (password.match(specialChars) || []).length
  
  if (PASSWORD_REQUIREMENTS.requireSpecialChars && specialCharCount === 0) {
    errors.push('Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)')
  } else if (specialCharCount >= PASSWORD_REQUIREMENTS.minSpecialChars) {
    score += 1
  }
  
  // Check against common passwords
  if (PASSWORD_REQUIREMENTS.preventCommonPasswords) {
    const lowerPassword = password.toLowerCase()
    if (COMMON_PASSWORDS.includes(lowerPassword)) {
      errors.push('Password is too common. Please choose a more secure password')
    }
  }
  
  // Check if password contains user information
  if (PASSWORD_REQUIREMENTS.preventUserInfoInPassword && userInfo) {
    const lowerPassword = password.toLowerCase()
    const userFields = [
      userInfo.username?.toLowerCase(),
      userInfo.email?.toLowerCase().split('@')[0],
      userInfo.firstName?.toLowerCase(),
      userInfo.lastName?.toLowerCase()
    ].filter(Boolean)
    
    for (const field of userFields) {
      if (field && field.length > 2 && lowerPassword.includes(field)) {
        errors.push('Password cannot contain your personal information')
        break
      }
    }
  }
  
  // Additional scoring for length and complexity
  if (password.length >= 12) score += 1
  if (password.length >= 16) score += 1
  if (specialCharCount >= 2) score += 1
  if (/[A-Z].*[A-Z]/.test(password)) score += 1 // Multiple uppercase
  if (/\d.*\d/.test(password)) score += 1 // Multiple numbers
  
  // Determine strength
  let strength: 'weak' | 'medium' | 'strong'
  if (score <= 3) {
    strength = 'weak'
  } else if (score <= 6) {
    strength = 'medium'
  } else {
    strength = 'strong'
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    strength,
    score: Math.min(score, 10) // Cap at 10
  }
}

/**
 * Generate a secure temporary password
 */
export function generateTemporaryPassword(length: number = 12): string {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const lowercase = 'abcdefghijklmnopqrstuvwxyz'
  const numbers = '0123456789'
  const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?'
  
  const allChars = uppercase + lowercase + numbers + specialChars
  
  let password = ''
  
  // Ensure at least one character from each required category
  password += uppercase[Math.floor(Math.random() * uppercase.length)]
  password += lowercase[Math.floor(Math.random() * lowercase.length)]
  password += numbers[Math.floor(Math.random() * numbers.length)]
  password += specialChars[Math.floor(Math.random() * specialChars.length)]
  
  // Fill the rest randomly
  for (let i = password.length; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)]
  }
  
  // Shuffle the password to avoid predictable patterns
  return password.split('').sort(() => Math.random() - 0.5).join('')
}

/**
 * Check if password needs to be changed based on age
 */
export function shouldChangePassword(passwordChangedAt: Date, maxAgeInDays: number = 90): boolean {
  const now = new Date()
  const ageInDays = (now.getTime() - passwordChangedAt.getTime()) / (1000 * 60 * 60 * 24)
  return ageInDays > maxAgeInDays
}

/**
 * Generate password reset token
 */
export function generateResetToken(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let token = ''
  for (let i = 0; i < 32; i++) {
    token += chars[Math.floor(Math.random() * chars.length)]
  }
  return token
}

/**
 * Sanitize password for logging (replace with asterisks)
 */
export function sanitizePasswordForLog(password: string): string {
  return '*'.repeat(Math.min(password.length, 8))
}
