import { prisma } from '@/lib/prisma'
import { SecurityEventType, SecuritySeverity, UserRole } from '@/lib/generated/prisma'
import { SecurityEvent, RateLimitInfo, AuditLogEntry } from './types'

// Rate limiting configuration
const RATE_LIMITS = {
  login: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxAttempts: 5,
    blockDurationMs: 30 * 60 * 1000 // 30 minutes
  },
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100
  },
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxAttempts: 3
  }
}

// Account lockout configuration
const LOCKOUT_CONFIG = {
  maxFailedAttempts: 5,
  lockoutDurationMs: 30 * 60 * 1000, // 30 minutes
  progressiveLockout: true // Increase lockout time with repeated failures
}

/**
 * Check if user account should be locked due to failed login attempts
 */
export async function checkAccountLockout(userId: string): Promise<{
  isLocked: boolean
  lockoutUntil?: Date
  failedAttempts: number
}> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      failedLoginAttempts: true,
      lockedUntil: true
    }
  })

  if (!user) {
    return { isLocked: false, failedAttempts: 0 }
  }

  const now = new Date()
  const isCurrentlyLocked = user.lockedUntil && user.lockedUntil > now

  return {
    isLocked: !!isCurrentlyLocked,
    lockoutUntil: user.lockedUntil || undefined,
    failedAttempts: user.failedLoginAttempts
  }
}

/**
 * Handle failed login attempt
 */
export async function handleFailedLogin(
  userId: string,
  ipAddress: string,
  userAgent?: string
): Promise<{ shouldLock: boolean; lockoutUntil?: Date }> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      failedLoginAttempts: true,
      lockedUntil: true
    }
  })

  if (!user) {
    return { shouldLock: false }
  }

  const newFailedAttempts = user.failedLoginAttempts + 1
  const shouldLock = newFailedAttempts >= LOCKOUT_CONFIG.maxFailedAttempts

  let lockoutUntil: Date | undefined
  if (shouldLock) {
    const lockoutDuration = LOCKOUT_CONFIG.progressiveLockout
      ? LOCKOUT_CONFIG.lockoutDurationMs * Math.pow(2, Math.floor(newFailedAttempts / LOCKOUT_CONFIG.maxFailedAttempts) - 1)
      : LOCKOUT_CONFIG.lockoutDurationMs

    lockoutUntil = new Date(Date.now() + lockoutDuration)
  }

  // Update user record
  await prisma.user.update({
    where: { id: userId },
    data: {
      failedLoginAttempts: newFailedAttempts,
      lockedUntil: lockoutUntil
    }
  })

  // Log security event
  await logSecurityEvent({
    userId,
    eventType: shouldLock ? SecurityEventType.ACCOUNT_LOCKED : SecurityEventType.LOGIN_FAILURE,
    severity: shouldLock ? SecuritySeverity.HIGH : SecuritySeverity.MEDIUM,
    description: shouldLock 
      ? `Account locked after ${newFailedAttempts} failed login attempts`
      : `Failed login attempt (${newFailedAttempts}/${LOCKOUT_CONFIG.maxFailedAttempts})`,
    ipAddress,
    userAgent
  })

  return { shouldLock, lockoutUntil }
}

/**
 * Handle successful login
 */
export async function handleSuccessfulLogin(
  userId: string,
  ipAddress: string,
  userAgent?: string
): Promise<void> {
  // Reset failed login attempts
  await prisma.user.update({
    where: { id: userId },
    data: {
      failedLoginAttempts: 0,
      lockedUntil: null,
      lastLoginAt: new Date(),
      lastLoginIp: ipAddress
    }
  })

  // Log successful login
  await logSecurityEvent({
    userId,
    eventType: SecurityEventType.LOGIN_SUCCESS,
    severity: SecuritySeverity.LOW,
    description: 'Successful login',
    ipAddress,
    userAgent
  })
}

/**
 * Log security event
 */
export async function logSecurityEvent(event: {
  userId?: string
  eventType: SecurityEventType
  severity: SecuritySeverity
  description: string
  ipAddress?: string
  userAgent?: string
  additionalData?: Record<string, any>
}): Promise<void> {
  try {
    await prisma.securityEvent.create({
      data: {
        userId: event.userId,
        eventType: event.eventType,
        severity: event.severity,
        description: event.description,
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        additionalData: event.additionalData ? JSON.stringify(event.additionalData) : null
      }
    })
  } catch (error) {
    console.error('Failed to log security event:', error)
  }
}

/**
 * Log audit trail entry
 */
export async function logAuditEntry(entry: {
  userId?: string
  action: string
  entityType: string
  entityId?: string
  oldValues?: Record<string, any>
  newValues?: Record<string, any>
  ipAddress?: string
  userAgent?: string
}): Promise<void> {
  try {
    await prisma.auditLog.create({
      data: {
        userId: entry.userId,
        action: entry.action,
        entityType: entry.entityType,
        entityId: entry.entityId,
        oldValues: entry.oldValues ? JSON.stringify(entry.oldValues) : null,
        newValues: entry.newValues ? JSON.stringify(entry.newValues) : null,
        ipAddress: entry.ipAddress,
        userAgent: entry.userAgent
      }
    })
  } catch (error) {
    console.error('Failed to log audit entry:', error)
  }
}

/**
 * Check rate limit for IP address
 */
export async function checkRateLimit(
  key: string,
  limit: number,
  windowMs: number
): Promise<RateLimitInfo> {
  // In a production environment, you would use Redis or similar
  // For now, we'll use a simple in-memory store
  const now = Date.now()
  const windowStart = now - windowMs

  // This is a simplified implementation
  // In production, use a proper rate limiting solution like Redis
  const rateLimitKey = `rate_limit:${key}`
  
  // For demonstration, we'll return a basic rate limit info
  // In production, implement proper rate limiting with Redis
  return {
    limit,
    remaining: limit - 1,
    resetTime: new Date(now + windowMs),
    blocked: false
  }
}

/**
 * Validate user permissions for an action
 */
export function hasPermission(
  userRole: UserRole,
  userPermissions: string[] | undefined,
  requiredPermission: string
): boolean {
  // Admin has all permissions
  if (userRole === UserRole.ADMIN) {
    return true
  }

  // Principal has most permissions except system administration
  if (userRole === UserRole.PRINCIPAL) {
    const restrictedPermissions = ['system.admin', 'user.create', 'user.delete']
    return !restrictedPermissions.includes(requiredPermission)
  }

  // Check specific permissions
  if (userPermissions && userPermissions.includes(requiredPermission)) {
    return true
  }

  // Role-based permissions
  const rolePermissions: Record<UserRole, string[]> = {
    [UserRole.ADMIN]: ['*'], // All permissions
    [UserRole.PRINCIPAL]: [
      'students.read', 'students.write', 'students.delete',
      'attendance.read', 'attendance.write',
      'reports.read', 'reports.generate',
      'analytics.read', 'analytics.export',
      'teachers.read', 'teachers.write'
    ],
    [UserRole.TEACHER]: [
      'students.read',
      'attendance.read', 'attendance.write',
      'reports.read',
      'scanner.use'
    ],
    [UserRole.SCANNER]: [
      'scanner.use',
      'attendance.write',
      'students.read.basic'
    ]
  }

  const permissions = rolePermissions[userRole] || []
  return permissions.includes('*') || permissions.includes(requiredPermission)
}

/**
 * Get client IP address from request
 */
export function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  return realIP || remoteAddr || 'unknown'
}

/**
 * Get user agent from request
 */
export function getUserAgent(request: Request): string {
  return request.headers.get('user-agent') || 'unknown'
}

/**
 * Sanitize sensitive data for logging
 */
export function sanitizeForLog(data: any): any {
  if (typeof data !== 'object' || data === null) {
    return data
  }

  const sensitiveFields = ['password', 'passwordHash', 'token', 'secret', 'key']
  const sanitized = { ...data }

  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]'
    }
  }

  return sanitized
}
