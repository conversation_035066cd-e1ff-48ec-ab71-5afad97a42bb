import { NextRequest, NextResponse } from 'next/server'
import { auth, signOut } from '@/lib/auth/config'
import { prisma } from '@/lib/prisma'
import { logSecurityEvent, getClientIP, getUserAgent } from '@/lib/auth/security'
import { SecurityEventType, SecuritySeverity } from '@/lib/generated/prisma'
import { ApiResponse } from '@/lib/auth/types'

export async function POST(request: NextRequest) {
  try {
    // Get current session
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    const userId = session.user.id
    const ipAddress = getClientIP(request)
    const userAgent = getUserAgent(request)

    // Update any active login history records
    await prisma.loginHistory.updateMany({
      where: {
        userId: userId,
        logoutAt: null,
        success: true
      },
      data: {
        logoutAt: new Date(),
        logoutReason: 'manual'
      }
    })

    // Clear any session tokens
    await prisma.user.update({
      where: { id: userId },
      data: {
        sessionToken: null,
        sessionExpiresAt: null
      }
    })

    // Log security event
    await logSecurityEvent({
      userId,
      eventType: SecurityEventType.LOGOUT,
      severity: SecuritySeverity.LOW,
      description: 'User logged out manually',
      ipAddress,
      userAgent
    })

    // Sign out using NextAuth
    await signOut()

    return NextResponse.json({
      success: true,
      message: 'Logged out successfully'
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Logout error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  // Handle GET request for logout (for compatibility)
  return POST(request)
}
