"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { 
  UserCheck, 
  UserX, 
  UserMinus, 
  GraduationCap,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  Calendar
} from "lucide-react"
import { Student, getFullName } from "@/lib/types/student"
import { toast } from "sonner"
import { cn } from "@/lib/utils"

interface StudentStatusManagerProps {
  student: Student
  onStatusChanged?: (student: Student, newStatus: Student['status'], reason?: string) => void
  trigger?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

interface StatusTransition {
  from: Student['status']
  to: Student['status']
  allowed: boolean
  requiresReason: boolean
  warning?: string
}

export function StudentStatusManager({
  student,
  onStatusChanged,
  trigger,
  open,
  onOpenChange
}: StudentStatusManagerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedStatus, setSelectedStatus] = useState<Student['status']>(student.status)
  const [reason, setReason] = useState('')
  const [isUpdating, setIsUpdating] = useState(false)

  const handleOpenChange = (newOpen: boolean) => {
    if (onOpenChange) {
      onOpenChange(newOpen)
    } else {
      setIsOpen(newOpen)
    }
    
    if (!newOpen) {
      // Reset state when dialog closes
      setSelectedStatus(student.status)
      setReason('')
      setIsUpdating(false)
    }
  }

  // Define valid status transitions
  const statusTransitions: StatusTransition[] = [
    { from: 'Active', to: 'Inactive', allowed: true, requiresReason: true, warning: 'Student will be marked as temporarily inactive' },
    { from: 'Active', to: 'Transferred', allowed: true, requiresReason: true, warning: 'Student will be removed from active enrollment' },
    { from: 'Active', to: 'Graduated', allowed: true, requiresReason: false, warning: 'Student will be marked as graduated' },
    { from: 'Inactive', to: 'Active', allowed: true, requiresReason: true, warning: 'Student will be reactivated' },
    { from: 'Inactive', to: 'Transferred', allowed: true, requiresReason: true, warning: 'Student will be marked as transferred' },
    { from: 'Transferred', to: 'Active', allowed: true, requiresReason: true, warning: 'Student will be re-enrolled' },
    { from: 'Graduated', to: 'Active', allowed: false, requiresReason: false, warning: 'Cannot reactivate graduated students' }
  ]

  const getValidTransitions = () => {
    return statusTransitions.filter(t => t.from === student.status && t.allowed)
  }

  const getCurrentTransition = () => {
    return statusTransitions.find(t => t.from === student.status && t.to === selectedStatus)
  }

  const getStatusIcon = (status: Student['status']) => {
    switch (status) {
      case 'Active':
        return <UserCheck className="h-4 w-4 text-green-600" />
      case 'Inactive':
        return <UserX className="h-4 w-4 text-yellow-600" />
      case 'Transferred':
        return <UserMinus className="h-4 w-4 text-blue-600" />
      case 'Graduated':
        return <GraduationCap className="h-4 w-4 text-purple-600" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: Student['status']) => {
    switch (status) {
      case 'Active':
        return 'bg-green-500 text-white'
      case 'Inactive':
        return 'bg-yellow-500 text-white'
      case 'Transferred':
        return 'bg-blue-500 text-white'
      case 'Graduated':
        return 'bg-purple-500 text-white'
      default:
        return 'bg-gray-500 text-white'
    }
  }

  const getStatusDescription = (status: Student['status']) => {
    switch (status) {
      case 'Active':
        return 'Student is currently enrolled and attending classes'
      case 'Inactive':
        return 'Student is temporarily not attending (e.g., medical leave, suspension)'
      case 'Transferred':
        return 'Student has transferred to another school or program'
      case 'Graduated':
        return 'Student has completed their studies and graduated'
      default:
        return 'Unknown status'
    }
  }

  const handleStatusUpdate = async () => {
    const transition = getCurrentTransition()
    
    if (!transition?.allowed) {
      toast.error('Invalid status transition')
      return
    }

    if (transition.requiresReason && !reason.trim()) {
      toast.error('Please provide a reason for this status change')
      return
    }

    setIsUpdating(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const updatedStudent: Student = {
        ...student,
        status: selectedStatus,
        lastUpdated: new Date().toISOString()
      }

      if (onStatusChanged) {
        onStatusChanged(updatedStudent, selectedStatus, reason.trim() || undefined)
      }

      toast.success(`Student status updated to ${selectedStatus}`)
      handleOpenChange(false)
    } catch (error) {
      toast.error('Failed to update student status')
    } finally {
      setIsUpdating(false)
    }
  }

  const dialogOpen = open !== undefined ? open : isOpen
  const fullName = getFullName(student)
  const currentTransition = getCurrentTransition()
  const validTransitions = getValidTransitions()

  return (
    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      
      {!trigger && (
        <DialogTrigger asChild>
          <Button variant="outline" size="sm">
            {getStatusIcon(student.status)}
            <span className="ml-2">Manage Status</span>
          </Button>
        </DialogTrigger>
      )}

      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Manage Student Status
          </DialogTitle>
          <DialogDescription>
            Update the enrollment status for {fullName}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Status */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Current Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                {getStatusIcon(student.status)}
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(student.status)}>
                      {student.status}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      Since {new Date(student.lastUpdated).toLocaleDateString()}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {getStatusDescription(student.status)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Status Change */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Change Status</CardTitle>
              <CardDescription>
                Select a new status for the student
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>New Status</Label>
                <Select value={selectedStatus} onValueChange={(value: Student['status']) => setSelectedStatus(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={student.status} disabled>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(student.status)}
                        {student.status} (Current)
                      </div>
                    </SelectItem>
                    {validTransitions.map(transition => (
                      <SelectItem key={transition.to} value={transition.to}>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(transition.to)}
                          {transition.to}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Transition Warning */}
              {currentTransition && selectedStatus !== student.status && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {currentTransition.warning}
                  </AlertDescription>
                </Alert>
              )}

              {/* Reason Input */}
              {currentTransition?.requiresReason && selectedStatus !== student.status && (
                <div className="space-y-2">
                  <Label>Reason for Status Change *</Label>
                  <Textarea
                    placeholder="Please provide a reason for this status change..."
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    This reason will be recorded in the student's history
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Status History Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Status History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {/* Current status entry */}
                <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                  {getStatusIcon(student.status)}
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{student.status}</span>
                      <Badge variant="outline" className="text-xs">Current</Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Since {new Date(student.lastUpdated).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                {/* Preview of new status entry */}
                {selectedStatus !== student.status && currentTransition?.allowed && (
                  <div className="flex items-center gap-3 p-3 border-2 border-dashed border-primary/50 rounded-lg">
                    {getStatusIcon(selectedStatus)}
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{selectedStatus}</span>
                        <Badge variant="outline" className="text-xs">Pending</Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Will be effective {new Date().toLocaleDateString()}
                      </p>
                      {reason && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Reason: {reason}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => handleOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleStatusUpdate}
              disabled={
                selectedStatus === student.status || 
                !currentTransition?.allowed ||
                (currentTransition?.requiresReason && !reason.trim()) ||
                isUpdating
              }
            >
              {isUpdating ? (
                <>
                  <Clock className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Update Status
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
