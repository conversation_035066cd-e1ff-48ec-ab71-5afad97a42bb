import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/security'
import { getSecurityDashboard, analyzeSecurityEvents } from '@/lib/auth/monitoring'
import { cleanupExpiredSessions, getSessionStatistics } from '@/lib/auth/session'
import { prisma } from '@/lib/prisma'
import { ApiResponse } from '@/lib/auth/types'

// GET /api/auth/security - Get security dashboard data
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions - only admins can view security data
    if (!hasPermission(session.user.role, session.user.permissions, 'system.security')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'dashboard'

    switch (type) {
      case 'dashboard':
        const dashboardData = await getSecurityDashboard()
        return NextResponse.json({
          success: true,
          data: dashboardData
        } as ApiResponse, { status: 200 })

      case 'events':
        const page = parseInt(searchParams.get('page') || '1')
        const limit = parseInt(searchParams.get('limit') || '20')
        const severity = searchParams.get('severity')
        const userId = searchParams.get('userId')
        const skip = (page - 1) * limit

        const where: any = {}
        if (severity) where.severity = severity
        if (userId) where.userId = userId

        const [events, total] = await Promise.all([
          prisma.securityEvent.findMany({
            where,
            include: {
              user: {
                select: {
                  username: true,
                  email: true,
                  role: true
                }
              }
            },
            orderBy: { createdAt: 'desc' },
            skip,
            take: limit
          }),
          prisma.securityEvent.count({ where })
        ])

        return NextResponse.json({
          success: true,
          data: events,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        } as ApiResponse, { status: 200 })

      case 'login-history':
        const loginPage = parseInt(searchParams.get('page') || '1')
        const loginLimit = parseInt(searchParams.get('limit') || '20')
        const loginUserId = searchParams.get('userId')
        const success = searchParams.get('success')
        const loginSkip = (loginPage - 1) * loginLimit

        const loginWhere: any = {}
        if (loginUserId) loginWhere.userId = loginUserId
        if (success !== null) loginWhere.success = success === 'true'

        const [loginHistory, loginTotal] = await Promise.all([
          prisma.loginHistory.findMany({
            where: loginWhere,
            include: {
              user: {
                select: {
                  username: true,
                  email: true,
                  role: true
                }
              }
            },
            orderBy: { loginAt: 'desc' },
            skip: loginSkip,
            take: loginLimit
          }),
          prisma.loginHistory.count({ where: loginWhere })
        ])

        return NextResponse.json({
          success: true,
          data: loginHistory,
          pagination: {
            page: loginPage,
            limit: loginLimit,
            total: loginTotal,
            totalPages: Math.ceil(loginTotal / loginLimit)
          }
        } as ApiResponse, { status: 200 })

      case 'sessions':
        const sessionStats = await getSessionStatistics()
        return NextResponse.json({
          success: true,
          data: sessionStats
        } as ApiResponse, { status: 200 })

      case 'analysis':
        const analysisData = await analyzeSecurityEvents()
        return NextResponse.json({
          success: true,
          data: analysisData
        } as ApiResponse, { status: 200 })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid type parameter'
        } as ApiResponse, { status: 400 })
    }

  } catch (error) {
    console.error('Security API error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// POST /api/auth/security - Perform security actions
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions - only admins can perform security actions
    if (!hasPermission(session.user.role, session.user.permissions, 'system.security')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const body = await request.json()
    const { action, ...params } = body

    switch (action) {
      case 'cleanup_sessions':
        const cleanedCount = await cleanupExpiredSessions()
        return NextResponse.json({
          success: true,
          message: `Cleaned up ${cleanedCount} expired sessions`,
          data: { cleanedCount }
        } as ApiResponse, { status: 200 })

      case 'lock_user':
        const { userId, reason } = params
        if (!userId) {
          return NextResponse.json({
            success: false,
            error: 'User ID is required'
          } as ApiResponse, { status: 400 })
        }

        await prisma.user.update({
          where: { id: userId },
          data: {
            isActive: false,
            lockedUntil: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
          }
        })

        return NextResponse.json({
          success: true,
          message: 'User account locked successfully'
        } as ApiResponse, { status: 200 })

      case 'unlock_user':
        const { userId: unlockUserId } = params
        if (!unlockUserId) {
          return NextResponse.json({
            success: false,
            error: 'User ID is required'
          } as ApiResponse, { status: 400 })
        }

        await prisma.user.update({
          where: { id: unlockUserId },
          data: {
            isActive: true,
            lockedUntil: null,
            failedLoginAttempts: 0
          }
        })

        return NextResponse.json({
          success: true,
          message: 'User account unlocked successfully'
        } as ApiResponse, { status: 200 })

      case 'resolve_event':
        const { eventId, resolution } = params
        if (!eventId) {
          return NextResponse.json({
            success: false,
            error: 'Event ID is required'
          } as ApiResponse, { status: 400 })
        }

        await prisma.securityEvent.update({
          where: { id: eventId },
          data: {
            resolved: true,
            resolvedAt: new Date(),
            resolvedBy: session.user.id,
            resolution: resolution || 'Resolved by administrator'
          }
        })

        return NextResponse.json({
          success: true,
          message: 'Security event resolved successfully'
        } as ApiResponse, { status: 200 })

      case 'bulk_resolve_events':
        const { eventIds, bulkResolution } = params
        if (!eventIds || !Array.isArray(eventIds)) {
          return NextResponse.json({
            success: false,
            error: 'Event IDs array is required'
          } as ApiResponse, { status: 400 })
        }

        const updateResult = await prisma.securityEvent.updateMany({
          where: {
            id: { in: eventIds }
          },
          data: {
            resolved: true,
            resolvedAt: new Date(),
            resolvedBy: session.user.id,
            resolution: bulkResolution || 'Bulk resolved by administrator'
          }
        })

        return NextResponse.json({
          success: true,
          message: `Resolved ${updateResult.count} security events`,
          data: { resolvedCount: updateResult.count }
        } as ApiResponse, { status: 200 })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action'
        } as ApiResponse, { status: 400 })
    }

  } catch (error) {
    console.error('Security action error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
