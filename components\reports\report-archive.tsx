"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { GeneratedReport, ReportArchive } from "@/lib/types/reports"
import { mockGeneratedReports } from "@/lib/data/reports-mock-data"
import { 
  Archive, 
  Search, 
  Download, 
  Trash2, 
  RotateCcw, 
  MoreHorizontal,
  Calendar,
  FileText,
  Clock,
  HardDrive
} from "lucide-react"
import { format } from "date-fns"
import { toast } from "sonner"

interface ReportArchiveProps {
  className?: string
}

// Mock archived reports
const mockArchivedReports: (GeneratedReport & { archiveInfo: ReportArchive })[] = [
  {
    ...mockGeneratedReports[0],
    id: "ARC001",
    status: "ARCHIVED",
    archiveInfo: {
      id: "ARCH001",
      reportId: "RPT001",
      archivedAt: "2024-12-01T10:00:00Z",
      archivedBy: "admin",
      reason: "Automatic archival after 30 days",
      retentionPeriod: 365,
      autoDelete: true
    }
  },
  {
    ...mockGeneratedReports[1],
    id: "ARC002", 
    status: "ARCHIVED",
    archiveInfo: {
      id: "ARCH002",
      reportId: "RPT002",
      archivedAt: "2024-11-15T15:30:00Z",
      archivedBy: "principal",
      reason: "Manual archival for compliance",
      retentionPeriod: 2555, // 7 years
      autoDelete: false
    }
  }
]

export function ReportArchive({ className }: ReportArchiveProps) {
  const [archivedReports, setArchivedReports] = useState(mockArchivedReports)
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState<"date" | "name" | "type">("date")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")

  // Filter and sort archived reports
  const filteredReports = archivedReports
    .filter(report => 
      report.config.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.config.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      let comparison = 0
      switch (sortBy) {
        case "date":
          comparison = new Date(a.archiveInfo.archivedAt).getTime() - new Date(b.archiveInfo.archivedAt).getTime()
          break
        case "name":
          comparison = a.config.name.localeCompare(b.config.name)
          break
        case "type":
          comparison = a.config.type.localeCompare(b.config.type)
          break
      }
      return sortOrder === "desc" ? -comparison : comparison
    })

  const handleRestore = (reportId: string) => {
    setArchivedReports(archivedReports.filter(report => report.id !== reportId))
    toast.success("Report restored successfully", {
      description: "The report has been moved back to the active reports list"
    })
  }

  const handlePermanentDelete = (reportId: string) => {
    setArchivedReports(archivedReports.filter(report => report.id !== reportId))
    toast.success("Report permanently deleted", {
      description: "This action cannot be undone"
    })
  }

  const handleDownload = (reportId: string) => {
    toast.success("Downloading archived report")
  }

  const calculateDaysUntilDeletion = (archiveInfo: ReportArchive): number => {
    if (!archiveInfo.autoDelete) return -1
    
    const archivedDate = new Date(archiveInfo.archivedAt)
    const deletionDate = new Date(archivedDate.getTime() + (archiveInfo.retentionPeriod * 24 * 60 * 60 * 1000))
    const now = new Date()
    const daysLeft = Math.ceil((deletionDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000))
    
    return Math.max(0, daysLeft)
  }

  const getRetentionBadge = (archiveInfo: ReportArchive) => {
    if (!archiveInfo.autoDelete) {
      return <Badge variant="secondary">Permanent</Badge>
    }
    
    const daysLeft = calculateDaysUntilDeletion(archiveInfo)
    
    if (daysLeft === 0) {
      return <Badge variant="destructive">Expires Today</Badge>
    } else if (daysLeft <= 7) {
      return <Badge variant="destructive">Expires in {daysLeft} days</Badge>
    } else if (daysLeft <= 30) {
      return <Badge variant="secondary">Expires in {daysLeft} days</Badge>
    } else {
      return <Badge variant="outline">{daysLeft} days left</Badge>
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getTotalArchiveSize = () => {
    const totalBytes = archivedReports.reduce((sum, report) => sum + (report.fileSize || 0), 0)
    return formatFileSize(totalBytes)
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Archive className="h-6 w-6" />
            Report Archive
          </h2>
          <p className="text-muted-foreground">
            Manage archived reports and retention policies
          </p>
        </div>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <FileText className="h-4 w-4" />
            {archivedReports.length} reports
          </div>
          <div className="flex items-center gap-1">
            <HardDrive className="h-4 w-4" />
            {getTotalArchiveSize()}
          </div>
        </div>
      </div>

      {/* Archive Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Archived</p>
                <p className="text-2xl font-bold">{archivedReports.length}</p>
              </div>
              <Archive className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Expiring Soon</p>
                <p className="text-2xl font-bold text-orange-600">
                  {archivedReports.filter(r => {
                    const days = calculateDaysUntilDeletion(r.archiveInfo)
                    return days >= 0 && days <= 30
                  }).length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Permanent</p>
                <p className="text-2xl font-bold text-blue-600">
                  {archivedReports.filter(r => !r.archiveInfo.autoDelete).length}
                </p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Storage Used</p>
                <p className="text-2xl font-bold">{getTotalArchiveSize()}</p>
              </div>
              <HardDrive className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search archived reports..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [field, order] = value.split('-')
              setSortBy(field as typeof sortBy)
              setSortOrder(order as typeof sortOrder)
            }}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date-desc">Newest First</SelectItem>
                <SelectItem value="date-asc">Oldest First</SelectItem>
                <SelectItem value="name-asc">Name A-Z</SelectItem>
                <SelectItem value="name-desc">Name Z-A</SelectItem>
                <SelectItem value="type-asc">Type A-Z</SelectItem>
                <SelectItem value="type-desc">Type Z-A</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Archived Reports Table */}
      <Card>
        <CardHeader>
          <CardTitle>Archived Reports</CardTitle>
          <CardDescription>
            View and manage your archived reports
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredReports.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Archive className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No archived reports found</p>
              <p className="text-sm">Reports will appear here when archived</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Report</TableHead>
                    <TableHead>Archived</TableHead>
                    <TableHead>Retention</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Reason</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredReports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{report.config.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {report.config.description}
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {report.config.type}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {format(new Date(report.archiveInfo.archivedAt), "MMM dd, yyyy")}
                          <div className="text-xs text-muted-foreground">
                            by {report.archiveInfo.archivedBy}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getRetentionBadge(report.archiveInfo)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {report.fileSize ? formatFileSize(report.fileSize) : '-'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-muted-foreground max-w-48 truncate">
                          {report.archiveInfo.reason}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDownload(report.id)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleRestore(report.id)}>
                                <RotateCcw className="h-4 w-4 mr-2" />
                                Restore
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handlePermanentDelete(report.id)}
                                className="text-destructive"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete Permanently
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
