"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { 
  AlertTriangle, 
  TrendingDown, 
  TrendingUp, 
  Clock, 
  Users, 
  Phone,
  Mail,
  Calendar,
  Target,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react"
import { StudentRiskAssessment, RiskFactor, InterventionRecommendation } from "@/lib/types/analytics"

interface RiskAssessmentCardProps {
  assessment: StudentRiskAssessment
  onInterventionUpdate?: (interventionId: string, status: string) => void
  onReassess?: (studentId: string) => void
}

export function RiskAssessmentCard({ 
  assessment, 
  onInterventionUpdate, 
  onReassess 
}: RiskAssessmentCardProps) {
  const [activeTab, setActiveTab] = useState("overview")

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'bg-green-500'
      case 'medium': return 'bg-yellow-500'
      case 'high': return 'bg-orange-500'
      case 'critical': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getRiskLevelBadgeVariant = (level: string) => {
    switch (level) {
      case 'low': return 'default'
      case 'medium': return 'secondary'
      case 'high': return 'destructive'
      case 'critical': return 'destructive'
      default: return 'outline'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'low': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'medium': return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case 'high': return <AlertTriangle className="h-4 w-4 text-orange-500" />
      case 'critical': return <XCircle className="h-4 w-4 text-red-500" />
      default: return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getInterventionStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'in_progress': return <Clock className="h-4 w-4 text-blue-500" />
      case 'pending': return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case 'cancelled': return <XCircle className="h-4 w-4 text-red-500" />
      default: return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Risk Assessment
              <Badge variant={getRiskLevelBadgeVariant(assessment.riskLevel)}>
                {assessment.riskLevel.toUpperCase()}
              </Badge>
            </CardTitle>
            <CardDescription>
              Student ID: {assessment.studentId} • Last updated: {formatDate(assessment.lastAssessment)}
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => onReassess?.(assessment.studentId)}
          >
            Reassess
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="factors">Risk Factors</TabsTrigger>
            <TabsTrigger value="interventions">Interventions</TabsTrigger>
            <TabsTrigger value="engagement">Parent Engagement</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Risk Score</span>
                  <span className="text-2xl font-bold">{assessment.riskScore}/100</span>
                </div>
                <Progress value={assessment.riskScore} className="h-2" />
                <div className={`h-2 rounded-full ${getRiskLevelColor(assessment.riskLevel)}`} />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Dropout Probability</span>
                  <span className="text-2xl font-bold">
                    {Math.round(assessment.dropoutProbability * 100)}%
                  </span>
                </div>
                <Progress value={assessment.dropoutProbability * 100} className="h-2" />
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 pt-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-500">
                  {assessment.riskFactors.length}
                </div>
                <div className="text-sm text-muted-foreground">Risk Factors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-500">
                  {assessment.interventions.filter(i => i.status === 'in_progress' || i.status === 'pending').length}
                </div>
                <div className="text-sm text-muted-foreground">Active Interventions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-500">
                  {assessment.parentEngagement.engagementScore}
                </div>
                <div className="text-sm text-muted-foreground">Parent Engagement</div>
              </div>
            </div>

            <div className="pt-4 border-t">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Next Review:</span>
                <span className="font-medium">{formatDate(assessment.nextReview)}</span>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="factors" className="space-y-4">
            {assessment.riskFactors.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                <p>No risk factors identified</p>
              </div>
            ) : (
              <div className="space-y-3">
                {assessment.riskFactors.map((factor, index) => (
                  <Card key={index} className="p-4">
                    <div className="flex items-start gap-3">
                      {getSeverityIcon(factor.severity)}
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium capitalize">{factor.type.replace('_', ' ')}</h4>
                          <Badge variant="outline" className="text-xs">
                            {factor.severity}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {factor.description}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>Impact: {factor.impact}%</span>
                          <span className="flex items-center gap-1">
                            {factor.trend === 'improving' ? (
                              <TrendingUp className="h-3 w-3 text-green-500" />
                            ) : factor.trend === 'declining' ? (
                              <TrendingDown className="h-3 w-3 text-red-500" />
                            ) : null}
                            {factor.trend}
                          </span>
                          <span>Since: {formatDate(factor.firstDetected)}</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="interventions" className="space-y-4">
            {assessment.interventions.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Target className="h-12 w-12 mx-auto mb-4" />
                <p>No interventions scheduled</p>
              </div>
            ) : (
              <div className="space-y-3">
                {assessment.interventions.map((intervention) => (
                  <Card key={intervention.id} className="p-4">
                    <div className="flex items-start gap-3">
                      {getInterventionStatusIcon(intervention.status)}
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{intervention.title}</h4>
                          <div className="flex items-center gap-2">
                            <Badge 
                              variant={intervention.priority === 'urgent' ? 'destructive' : 'outline'}
                              className="text-xs"
                            >
                              {intervention.priority}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {intervention.status.replace('_', ' ')}
                            </Badge>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {intervention.description}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground mb-3">
                          <span>Impact: {intervention.estimatedImpact}%</span>
                          <span>Timeframe: {intervention.timeframe}</span>
                          <span>Due: {formatDate(intervention.dueDate)}</span>
                        </div>
                        {intervention.status === 'pending' && (
                          <div className="flex gap-2">
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => onInterventionUpdate?.(intervention.id, 'in_progress')}
                            >
                              Start
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => onInterventionUpdate?.(intervention.id, 'completed')}
                            >
                              Mark Complete
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="engagement" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Card className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Mail className="h-4 w-4" />
                  <span className="font-medium">Communication</span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Frequency (per month)</span>
                    <span>{assessment.parentEngagement.communicationFrequency}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Response Rate</span>
                    <span>{Math.round(assessment.parentEngagement.responseRate * 100)}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Preferred Method</span>
                    <span className="capitalize">{assessment.parentEngagement.preferredMethod}</span>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Users className="h-4 w-4" />
                  <span className="font-medium">Engagement</span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Meeting Attendance</span>
                    <span>{Math.round(assessment.parentEngagement.meetingAttendance * 100)}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Engagement Score</span>
                    <span>{assessment.parentEngagement.engagementScore}/100</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Last Contact</span>
                    <span>{formatDate(assessment.parentEngagement.lastContact)}</span>
                  </div>
                </div>
              </Card>
            </div>

            <div className="pt-4">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Overall Engagement Score</span>
                <span className="text-2xl font-bold">{assessment.parentEngagement.engagementScore}/100</span>
              </div>
              <Progress value={assessment.parentEngagement.engagementScore} className="h-2" />
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
