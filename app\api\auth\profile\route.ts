import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { prisma } from '@/lib/prisma'
import { hashPassword, validatePassword, verifyPassword } from '@/lib/auth/password'
import { logAuditEntry, getClientIP, getUserAgent } from '@/lib/auth/security'
import { ApiResponse, UpdateUserRequest, PasswordChangeRequest, AuthUser } from '@/lib/auth/types'

// GET /api/auth/profile - Get current user profile
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        username: true,
        email: true,
        firstName: true,
        lastName: true,
        middleName: true,
        role: true,
        permissions: true,
        isActive: true,
        lastLoginAt: true,
        mustChangePassword: true,
        twoFactorEnabled: true,
        phoneNumber: true,
        department: true,
        position: true,
        passwordChangedAt: true,
        createdAt: true,
        updatedAt: true
      }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      } as ApiResponse, { status: 404 })
    }

    // Parse permissions
    let permissions: string[] = []
    if (user.permissions) {
      try {
        permissions = JSON.parse(user.permissions)
      } catch (error) {
        console.error('Failed to parse user permissions:', error)
      }
    }

    const authUser: AuthUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      middleName: user.middleName,
      role: user.role,
      permissions,
      isActive: user.isActive,
      lastLoginAt: user.lastLoginAt,
      mustChangePassword: user.mustChangePassword,
      twoFactorEnabled: user.twoFactorEnabled,
      phoneNumber: user.phoneNumber,
      department: user.department,
      position: user.position
    }

    return NextResponse.json({
      success: true,
      data: authUser
    } as ApiResponse<AuthUser>, { status: 200 })

  } catch (error) {
    console.error('Get profile error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// PUT /api/auth/profile - Update user profile
export async function PUT(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    const body = await request.json()
    const updateData: UpdateUserRequest = body

    // Get current user data for audit log
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!currentUser) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      } as ApiResponse, { status: 404 })
    }

    // Validate email uniqueness if being updated
    if (updateData.email && updateData.email !== currentUser.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email: updateData.email }
      })

      if (existingUser) {
        return NextResponse.json({
          success: false,
          error: 'Email address is already in use'
        } as ApiResponse, { status: 400 })
      }
    }

    // Prepare update data
    const updateFields: any = {}
    
    if (updateData.firstName) updateFields.firstName = updateData.firstName
    if (updateData.lastName) updateFields.lastName = updateData.lastName
    if (updateData.middleName !== undefined) updateFields.middleName = updateData.middleName
    if (updateData.email) updateFields.email = updateData.email
    if (updateData.phoneNumber !== undefined) updateFields.phoneNumber = updateData.phoneNumber
    if (updateData.department !== undefined) updateFields.department = updateData.department
    if (updateData.position !== undefined) updateFields.position = updateData.position

    // Only allow role/permission changes for admins
    if (session.user.role === 'ADMIN') {
      if (updateData.role) updateFields.role = updateData.role
      if (updateData.permissions) updateFields.permissions = JSON.stringify(updateData.permissions)
      if (updateData.isActive !== undefined) updateFields.isActive = updateData.isActive
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: updateFields
    })

    // Log audit entry
    await logAuditEntry({
      userId: session.user.id,
      action: 'UPDATE_PROFILE',
      entityType: 'User',
      entityId: session.user.id,
      oldValues: {
        firstName: currentUser.firstName,
        lastName: currentUser.lastName,
        middleName: currentUser.middleName,
        email: currentUser.email,
        phoneNumber: currentUser.phoneNumber,
        department: currentUser.department,
        position: currentUser.position
      },
      newValues: updateFields,
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request)
    })

    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully'
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Update profile error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// POST /api/auth/profile/change-password - Change user password
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    const body = await request.json()
    const { currentPassword, newPassword, confirmPassword }: PasswordChangeRequest = body

    // Validate input
    if (!currentPassword || !newPassword || !confirmPassword) {
      return NextResponse.json({
        success: false,
        error: 'All password fields are required'
      } as ApiResponse, { status: 400 })
    }

    if (newPassword !== confirmPassword) {
      return NextResponse.json({
        success: false,
        error: 'New passwords do not match'
      } as ApiResponse, { status: 400 })
    }

    // Get current user
    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      } as ApiResponse, { status: 404 })
    }

    // Verify current password
    const isValidCurrentPassword = await verifyPassword(currentPassword, user.passwordHash)
    if (!isValidCurrentPassword) {
      return NextResponse.json({
        success: false,
        error: 'Current password is incorrect'
      } as ApiResponse, { status: 400 })
    }

    // Validate new password
    const passwordValidation = validatePassword(newPassword, {
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName
    })

    if (!passwordValidation.isValid) {
      return NextResponse.json({
        success: false,
        error: 'Password does not meet security requirements',
        data: passwordValidation.errors
      } as ApiResponse, { status: 400 })
    }

    // Hash new password
    const newPasswordHash = await hashPassword(newPassword)

    // Update password
    await prisma.user.update({
      where: { id: session.user.id },
      data: {
        passwordHash: newPasswordHash,
        passwordChangedAt: new Date(),
        mustChangePassword: false
      }
    })

    // Log audit entry
    await logAuditEntry({
      userId: session.user.id,
      action: 'CHANGE_PASSWORD',
      entityType: 'User',
      entityId: session.user.id,
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request)
    })

    return NextResponse.json({
      success: true,
      message: 'Password changed successfully'
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Change password error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
