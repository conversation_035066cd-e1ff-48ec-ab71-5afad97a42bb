// Analytics and AI-powered insights types for QRSAMS

export interface AttendancePattern {
  studentId: string
  patterns: {
    weeklyTrend: number[] // 7 days
    monthlyTrend: number[] // 30 days
    seasonalTrend: number[] // 12 months
    timeOfDayPattern: number[] // 24 hours
    subjectPattern: Record<string, number>
  }
  riskFactors: RiskFactor[]
  lastUpdated: string
}

export interface RiskFactor {
  type: 'attendance' | 'punctuality' | 'engagement' | 'academic' | 'behavioral'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  impact: number // 0-100
  trend: 'improving' | 'stable' | 'declining'
  firstDetected: string
  lastOccurrence: string
}

export interface StudentRiskAssessment {
  studentId: string
  riskScore: number // 0-100 (100 = highest risk)
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  dropoutProbability: number // 0-1
  riskFactors: RiskFactor[]
  interventions: InterventionRecommendation[]
  parentEngagement: ParentEngagementMetrics
  lastAssessment: string
  nextReview: string
}

export interface InterventionRecommendation {
  id: string
  type: 'counseling' | 'academic_support' | 'parent_meeting' | 'peer_mentoring' | 'schedule_adjustment'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  title: string
  description: string
  estimatedImpact: number // 0-100
  timeframe: string
  assignedTo?: string
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  createdAt: string
  dueDate: string
}

export interface ParentEngagementMetrics {
  communicationFrequency: number // messages per month
  responseRate: number // 0-1
  meetingAttendance: number // 0-1
  lastContact: string
  preferredMethod: 'sms' | 'email' | 'call' | 'in_person'
  engagementScore: number // 0-100
}

export interface AnalyticsKPI {
  id: string
  name: string
  value: number
  unit: string
  trend: {
    direction: 'up' | 'down' | 'stable'
    percentage: number
    period: string
  }
  target?: number
  status: 'good' | 'warning' | 'critical'
  description: string
}

export interface AttendanceHeatmapData {
  date: string
  hour: number
  value: number // attendance count
  rate: number // attendance rate 0-1
  grade?: string
  section?: string
}

export interface TrendAnalysis {
  period: 'daily' | 'weekly' | 'monthly' | 'yearly'
  data: Array<{
    date: string
    value: number
    prediction?: number
    confidence?: number
  }>
  trend: 'increasing' | 'decreasing' | 'stable' | 'volatile'
  seasonality: boolean
  forecast: Array<{
    date: string
    predicted: number
    confidence: number
  }>
}

export interface AIInsight {
  id: string
  type: 'pattern' | 'anomaly' | 'prediction' | 'recommendation'
  severity: 'info' | 'warning' | 'critical'
  title: string
  description: string
  confidence: number // 0-1
  affectedStudents?: string[]
  affectedGrades?: string[]
  actionRequired: boolean
  suggestedActions: string[]
  createdAt: string
  expiresAt?: string
}

export interface PredictiveModel {
  id: string
  name: string
  type: 'dropout_prediction' | 'attendance_forecast' | 'risk_assessment'
  accuracy: number // 0-1
  lastTrained: string
  features: string[]
  predictions: Array<{
    studentId?: string
    date: string
    prediction: number
    confidence: number
  }>
}

export interface AnalyticsFilter {
  dateRange: {
    start: string
    end: string
  }
  grades: string[]
  sections: string[]
  courses: string[]
  subjects: string[]
  riskLevels: string[]
  attendanceThreshold?: {
    min: number
    max: number
  }
}

export interface DashboardWidget {
  id: string
  type: 'kpi' | 'chart' | 'table' | 'alert' | 'insight'
  title: string
  size: 'small' | 'medium' | 'large'
  position: { x: number; y: number }
  config: Record<string, any>
  refreshInterval?: number // seconds
  lastUpdated: string
}

export interface AnalyticsReport {
  id: string
  title: string
  type: 'attendance_summary' | 'risk_assessment' | 'trend_analysis' | 'intervention_tracking'
  generatedAt: string
  generatedBy: string
  period: {
    start: string
    end: string
  }
  filters: AnalyticsFilter
  data: any
  insights: AIInsight[]
  recommendations: string[]
  exportFormats: ('pdf' | 'excel' | 'csv')[]
}

export interface AlertConfiguration {
  id: string
  name: string
  type: 'attendance_drop' | 'risk_increase' | 'pattern_change' | 'anomaly_detected'
  conditions: {
    threshold: number
    operator: 'greater_than' | 'less_than' | 'equals'
    period: string
  }
  recipients: string[]
  channels: ('email' | 'sms' | 'dashboard')[]
  active: boolean
  lastTriggered?: string
}

// Utility types for analytics calculations
export interface AttendanceMetrics {
  totalStudents: number
  presentCount: number
  lateCount: number
  absentCount: number
  attendanceRate: number
  punctualityRate: number
  averageArrivalTime: string
  peakHours: string[]
}

export interface GradeComparison {
  grade: string
  metrics: AttendanceMetrics
  trend: TrendAnalysis
  riskDistribution: Record<string, number>
}

export interface SubjectAnalysis {
  subjectId: string
  subjectName: string
  metrics: AttendanceMetrics
  popularityScore: number
  difficultyIndicator: number
  teacherEffectiveness: number
}

// Export configuration for analytics
export interface ExportConfig {
  format: 'pdf' | 'excel' | 'csv' | 'json'
  includeCharts: boolean
  includeRawData: boolean
  includeInsights: boolean
  dateRange: {
    start: string
    end: string
  }
  filters: AnalyticsFilter
}
