"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { 
  Download, 
  Upload, 
  QrCode, 
  Trash2, 
  FileText, 
  Users, 
  CheckCircle,
  AlertTriangle,
  X
} from "lucide-react"
import { Student } from "@/lib/types/student"
import { toast } from "sonner"

interface BulkActionsProps {
  selectedStudents: string[]
  students: Student[]
  onClearSelection: () => void
  onStudentsUpdated?: (students: Student[]) => void
  className?: string
}

export function BulkActions({
  selectedStudents,
  students,
  onClearSelection,
  onStudentsUpdated,
  className
}: BulkActionsProps) {
  const [isExporting, setIsExporting] = useState(false)
  const [isGeneratingQR, setIsGeneratingQR] = useState(false)
  const [showStatusDialog, setShowStatusDialog] = useState(false)
  const [newStatus, setNewStatus] = useState<string>('')

  const selectedStudentData = students.filter(s => selectedStudents.includes(s.id))

  const handleExportCSV = async () => {
    setIsExporting(true)
    try {
      // Simulate CSV export
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Create CSV content
      const headers = ['ID', 'Name', 'Email', 'Grade', 'Section', 'Course', 'Year', 'Status', 'Guardian Name', 'Guardian Phone']
      const csvContent = [
        headers.join(','),
        ...selectedStudentData.map(student => [
          student.id,
          `"${student.firstName} ${student.middleName || ''} ${student.lastName}".trim()`,
          student.email,
          student.grade,
          student.section || '',
          `"${student.course}"`,
          `"${student.year}"`,
          student.status,
          `"${student.guardian.name}"`,
          student.guardian.phone
        ].join(','))
      ].join('\n')

      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `students_export_${new Date().toISOString().split('T')[0]}.csv`
      a.click()
      window.URL.revokeObjectURL(url)

      toast.success(`Exported ${selectedStudents.length} students to CSV`)
    } catch (error) {
      toast.error('Failed to export students')
    } finally {
      setIsExporting(false)
    }
  }

  const handleGenerateQRCodes = async () => {
    setIsGeneratingQR(true)
    try {
      // Simulate QR code generation
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast.success(`Generated QR codes for ${selectedStudents.length} students`)
      
      // Here you would typically trigger the QR code generation and download
      console.log('Generating QR codes for students:', selectedStudents)
    } catch (error) {
      toast.error('Failed to generate QR codes')
    } finally {
      setIsGeneratingQR(false)
    }
  }

  const handleStatusUpdate = async () => {
    if (!newStatus) return

    try {
      // Simulate status update
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const updatedStudents = students.map(student => 
        selectedStudents.includes(student.id) 
          ? { ...student, status: newStatus as any }
          : student
      )

      onStudentsUpdated?.(updatedStudents)
      toast.success(`Updated status for ${selectedStudents.length} students`)
      setShowStatusDialog(false)
      setNewStatus('')
      onClearSelection()
    } catch (error) {
      toast.error('Failed to update student status')
    }
  }

  const handleDeleteStudents = async () => {
    try {
      // Simulate deletion
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const remainingStudents = students.filter(s => !selectedStudents.includes(s.id))
      onStudentsUpdated?.(remainingStudents)
      toast.success(`Deleted ${selectedStudents.length} students`)
      onClearSelection()
    } catch (error) {
      toast.error('Failed to delete students')
    }
  }

  const handleExportGuardianContacts = async () => {
    try {
      // Create guardian contacts CSV
      const headers = ['Student ID', 'Student Name', 'Guardian Name', 'Relationship', 'Phone', 'Email', 'Address']
      const csvContent = [
        headers.join(','),
        ...selectedStudentData.map(student => [
          student.id,
          `"${student.firstName} ${student.middleName || ''} ${student.lastName}".trim()`,
          `"${student.guardian.name}"`,
          student.guardian.relationship,
          student.guardian.phone,
          student.guardian.email || '',
          `"${student.guardian.address || ''}"`
        ].join(','))
      ].join('\n')

      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `guardian_contacts_${new Date().toISOString().split('T')[0]}.csv`
      a.click()
      window.URL.revokeObjectURL(url)

      toast.success(`Exported guardian contacts for ${selectedStudents.length} students`)
    } catch (error) {
      toast.error('Failed to export guardian contacts')
    }
  }

  if (selectedStudents.length === 0) {
    return null
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Bulk Actions
            <Badge variant="secondary">{selectedStudents.length} selected</Badge>
          </div>
          <Button variant="ghost" size="sm" onClick={onClearSelection}>
            <X className="h-4 w-4" />
          </Button>
        </CardTitle>
        <CardDescription>
          Perform actions on {selectedStudents.length} selected student{selectedStudents.length !== 1 ? 's' : ''}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2">
          {/* Export Actions */}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleExportCSV}
            disabled={isExporting}
          >
            <Download className="h-4 w-4 mr-2" />
            {isExporting ? 'Exporting...' : 'Export CSV'}
          </Button>

          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleExportGuardianContacts}
          >
            <FileText className="h-4 w-4 mr-2" />
            Export Contacts
          </Button>

          {/* QR Code Generation */}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleGenerateQRCodes}
            disabled={isGeneratingQR}
          >
            <QrCode className="h-4 w-4 mr-2" />
            {isGeneratingQR ? 'Generating...' : 'Generate QR Codes'}
          </Button>

          {/* Status Update */}
          <Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <CheckCircle className="h-4 w-4 mr-2" />
                Update Status
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Update Student Status</DialogTitle>
                <DialogDescription>
                  Change the status for {selectedStudents.length} selected student{selectedStudents.length !== 1 ? 's' : ''}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <Select value={newStatus} onValueChange={setNewStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select new status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Active">Active</SelectItem>
                    <SelectItem value="Inactive">Inactive</SelectItem>
                    <SelectItem value="Transferred">Transferred</SelectItem>
                    <SelectItem value="Graduated">Graduated</SelectItem>
                  </SelectContent>
                </Select>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowStatusDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleStatusUpdate} disabled={!newStatus}>
                    Update Status
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          {/* Delete Action */}
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-destructive" />
                  Delete Students
                </AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete {selectedStudents.length} student{selectedStudents.length !== 1 ? 's' : ''}? 
                  This action cannot be undone and will permanently remove all student data including:
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Personal information</li>
                    <li>Academic records</li>
                    <li>Attendance history</li>
                    <li>Guardian and emergency contacts</li>
                  </ul>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction 
                  onClick={handleDeleteStudents}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  Delete Students
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>

        {/* Selected Students Preview */}
        <div className="mt-4 pt-4 border-t">
          <h4 className="text-sm font-medium mb-2">Selected Students:</h4>
          <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
            {selectedStudentData.slice(0, 10).map(student => (
              <Badge key={student.id} variant="secondary" className="text-xs">
                {student.firstName} {student.lastName}
              </Badge>
            ))}
            {selectedStudentData.length > 10 && (
              <Badge variant="secondary" className="text-xs">
                +{selectedStudentData.length - 10} more
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
