import NextAuth from "next-auth"
import Credentials from "next-auth/providers/credentials"
import { prisma } from "@/lib/prisma"
import { verifyPassword } from "./password"
import { handleSuccessfulLogin, handleFailedLogin, checkAccountLockout, logSecurityEvent } from "./security"
import { SecurityEventType, SecuritySeverity, UserRole } from "@/lib/generated/prisma"
import { AuthUser, LoginCredentials } from "./types"

/**
 * Authenticate user with username/email and password
 */
async function authenticateUser(credentials: LoginCredentials, request?: Request): Promise<AuthUser | null> {
  try {
    const { username, password } = credentials

    if (!username || !password) {
      return null
    }

    // Find user by username or email
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: username },
          { email: username }
        ]
      },
      include: {
        teacher: true
      }
    })

    if (!user) {
      // Log failed login attempt for unknown user
      if (request) {
        await logSecurityEvent({
          eventType: SecurityEventType.LOGIN_FAILURE,
          severity: SecuritySeverity.MEDIUM,
          description: `Login attempt with unknown username: ${username}`,
          ipAddress: getClientIP(request),
          userAgent: getUserAgent(request)
        })
      }
      return null
    }

    // Check if account is active
    if (!user.isActive) {
      if (request) {
        await logSecurityEvent({
          userId: user.id,
          eventType: SecurityEventType.LOGIN_FAILURE,
          severity: SecuritySeverity.HIGH,
          description: 'Login attempt on inactive account',
          ipAddress: getClientIP(request),
          userAgent: getUserAgent(request)
        })
      }
      return null
    }

    // Check account lockout
    const lockoutStatus = await checkAccountLockout(user.id)
    if (lockoutStatus.isLocked) {
      if (request) {
        await logSecurityEvent({
          userId: user.id,
          eventType: SecurityEventType.LOGIN_FAILURE,
          severity: SecuritySeverity.HIGH,
          description: 'Login attempt on locked account',
          ipAddress: getClientIP(request),
          userAgent: getUserAgent(request)
        })
      }
      return null
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.passwordHash)

    if (!isValidPassword) {
      // Handle failed login
      if (request) {
        await handleFailedLogin(
          user.id,
          getClientIP(request),
          getUserAgent(request)
        )
      }
      return null
    }

    // Handle successful login
    if (request) {
      await handleSuccessfulLogin(
        user.id,
        getClientIP(request),
        getUserAgent(request)
      )
    }

    // Parse permissions
    let permissions: string[] = []
    if (user.permissions) {
      try {
        permissions = JSON.parse(user.permissions)
      } catch (error) {
        console.error('Failed to parse user permissions:', error)
      }
    }

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      middleName: user.middleName,
      role: user.role,
      permissions,
      isActive: user.isActive,
      lastLoginAt: user.lastLoginAt,
      mustChangePassword: user.mustChangePassword,
      twoFactorEnabled: user.twoFactorEnabled,
      phoneNumber: user.phoneNumber,
      department: user.department,
      position: user.position
    }
  } catch (error) {
    console.error('Authentication error:', error)
    return null
  }
}

// Helper functions for request handling
function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')

  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }

  return realIP || 'unknown'
}

function getUserAgent(request: Request): string {
  return request.headers.get('user-agent') || 'unknown'
}

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    Credentials({
      name: "credentials",
      credentials: {
        username: {
          label: "Username or Email",
          type: "text",
          placeholder: "Enter your username or email"
        },
        password: {
          label: "Password",
          type: "password",
          placeholder: "Enter your password"
        }
      },
      async authorize(credentials, request) {
        if (!credentials?.username || !credentials?.password) {
          return null
        }

        const user = await authenticateUser({
          username: credentials.username as string,
          password: credentials.password as string
        }, request as Request)

        if (user) {
          return {
            id: user.id,
            email: user.email,
            name: `${user.firstName} ${user.lastName}`,
            role: user.role,
            permissions: user.permissions,
            isActive: user.isActive,
            mustChangePassword: user.mustChangePassword
          }
        }

        return null
      }
    })
  ],
  pages: {
    signIn: "/login",
    error: "/login"
  },
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      // Initial sign in
      if (user) {
        token.role = user.role
        token.permissions = user.permissions
        token.isActive = user.isActive
        token.mustChangePassword = user.mustChangePassword
      }

      // Handle session updates
      if (trigger === "update" && session) {
        token.role = session.role || token.role
        token.permissions = session.permissions || token.permissions
        token.isActive = session.isActive ?? token.isActive
        token.mustChangePassword = session.mustChangePassword ?? token.mustChangePassword
      }

      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.sub!
        session.user.role = token.role as UserRole
        session.user.permissions = token.permissions as string[]
        session.user.isActive = token.isActive as boolean
        session.user.mustChangePassword = token.mustChangePassword as boolean
      }
      return session
    },
    async signIn({ user, account, profile, email, credentials }) {
      // Additional sign-in validation can be added here
      return true
    }
  },
  session: {
    strategy: "jwt",
    maxAge: 8 * 60 * 60, // 8 hours
    updateAge: 60 * 60, // 1 hour
  },
  jwt: {
    maxAge: 8 * 60 * 60, // 8 hours
  },
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      // Log successful sign in
      console.log(`User ${user.email} signed in successfully`)
    },
    async signOut({ token, session }) {
      // Log sign out
      if (token?.sub) {
        await logSecurityEvent({
          userId: token.sub,
          eventType: SecurityEventType.LOGOUT,
          severity: SecuritySeverity.LOW,
          description: 'User logged out'
        })
      }
    }
  }
})
