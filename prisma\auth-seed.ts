import { PrismaClient } from '../lib/generated/prisma'
import { 
  UserRole,
  SecurityEventType,
  SecuritySeverity
} from '../lib/generated/prisma'
import { hashPassword } from '../lib/auth/password'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting authentication system seeding...')

  // Clear existing authentication data
  console.log('🧹 Clearing existing authentication data...')
  await prisma.loginHistory.deleteMany()
  await prisma.securityEvent.deleteMany()
  await prisma.auditLog.deleteMany()
  await prisma.rolePermission.deleteMany()
  await prisma.permission.deleteMany()
  await prisma.user.deleteMany()

  console.log('✅ Existing authentication data cleared')

  // Create Permissions
  console.log('🔐 Creating permissions...')
  
  const permissions = await Promise.all([
    // Student permissions
    prisma.permission.create({
      data: {
        name: 'students.read',
        description: 'View student information',
        category: 'students',
        resource: 'student',
        action: 'read'
      }
    }),
    prisma.permission.create({
      data: {
        name: 'students.write',
        description: 'Create and update student information',
        category: 'students',
        resource: 'student',
        action: 'write'
      }
    }),
    prisma.permission.create({
      data: {
        name: 'students.delete',
        description: 'Delete student records',
        category: 'students',
        resource: 'student',
        action: 'delete'
      }
    }),
    
    // Attendance permissions
    prisma.permission.create({
      data: {
        name: 'attendance.read',
        description: 'View attendance records',
        category: 'attendance',
        resource: 'attendance',
        action: 'read'
      }
    }),
    prisma.permission.create({
      data: {
        name: 'attendance.write',
        description: 'Record attendance',
        category: 'attendance',
        resource: 'attendance',
        action: 'write'
      }
    }),
    
    // Scanner permissions
    prisma.permission.create({
      data: {
        name: 'scanner.use',
        description: 'Use QR code scanner',
        category: 'scanner',
        resource: 'scanner',
        action: 'use'
      }
    }),
    
    // Reports permissions
    prisma.permission.create({
      data: {
        name: 'reports.read',
        description: 'View reports',
        category: 'reports',
        resource: 'report',
        action: 'read'
      }
    }),
    prisma.permission.create({
      data: {
        name: 'reports.generate',
        description: 'Generate reports',
        category: 'reports',
        resource: 'report',
        action: 'generate'
      }
    }),
    
    // Analytics permissions
    prisma.permission.create({
      data: {
        name: 'analytics.read',
        description: 'View analytics',
        category: 'analytics',
        resource: 'analytics',
        action: 'read'
      }
    }),
    prisma.permission.create({
      data: {
        name: 'analytics.export',
        description: 'Export analytics data',
        category: 'analytics',
        resource: 'analytics',
        action: 'export'
      }
    }),
    
    // User management permissions
    prisma.permission.create({
      data: {
        name: 'users.read',
        description: 'View user accounts',
        category: 'users',
        resource: 'user',
        action: 'read'
      }
    }),
    prisma.permission.create({
      data: {
        name: 'users.create',
        description: 'Create user accounts',
        category: 'users',
        resource: 'user',
        action: 'create'
      }
    }),
    prisma.permission.create({
      data: {
        name: 'users.update',
        description: 'Update user accounts',
        category: 'users',
        resource: 'user',
        action: 'update'
      }
    }),
    prisma.permission.create({
      data: {
        name: 'users.delete',
        description: 'Delete user accounts',
        category: 'users',
        resource: 'user',
        action: 'delete'
      }
    }),
    prisma.permission.create({
      data: {
        name: 'users.reset_password',
        description: 'Reset user passwords',
        category: 'users',
        resource: 'user',
        action: 'reset_password'
      }
    }),
    prisma.permission.create({
      data: {
        name: 'users.unlock',
        description: 'Unlock user accounts',
        category: 'users',
        resource: 'user',
        action: 'unlock'
      }
    }),
    
    // Teacher permissions
    prisma.permission.create({
      data: {
        name: 'teachers.read',
        description: 'View teacher information',
        category: 'teachers',
        resource: 'teacher',
        action: 'read'
      }
    }),
    prisma.permission.create({
      data: {
        name: 'teachers.write',
        description: 'Create and update teacher information',
        category: 'teachers',
        resource: 'teacher',
        action: 'write'
      }
    })
  ])

  console.log(`✅ Created ${permissions.length} permissions`)

  // Create Role Permissions
  console.log('👥 Creating role permissions...')
  
  const rolePermissions = []
  
  // Admin permissions (all permissions)
  for (const permission of permissions) {
    rolePermissions.push(
      await prisma.rolePermission.create({
        data: {
          role: UserRole.ADMIN,
          permissionId: permission.id
        }
      })
    )
  }
  
  // Principal permissions (most permissions except user management)
  const principalPermissionNames = [
    'students.read', 'students.write', 'students.delete',
    'attendance.read', 'attendance.write',
    'scanner.use',
    'reports.read', 'reports.generate',
    'analytics.read', 'analytics.export',
    'teachers.read', 'teachers.write'
  ]
  
  for (const permissionName of principalPermissionNames) {
    const permission = permissions.find(p => p.name === permissionName)
    if (permission) {
      rolePermissions.push(
        await prisma.rolePermission.create({
          data: {
            role: UserRole.PRINCIPAL,
            permissionId: permission.id
          }
        })
      )
    }
  }
  
  // Teacher permissions
  const teacherPermissionNames = [
    'students.read',
    'attendance.read', 'attendance.write',
    'scanner.use',
    'reports.read'
  ]
  
  for (const permissionName of teacherPermissionNames) {
    const permission = permissions.find(p => p.name === permissionName)
    if (permission) {
      rolePermissions.push(
        await prisma.rolePermission.create({
          data: {
            role: UserRole.TEACHER,
            permissionId: permission.id
          }
        })
      )
    }
  }
  
  // Scanner permissions
  const scannerPermissionNames = ['scanner.use', 'attendance.write']
  
  for (const permissionName of scannerPermissionNames) {
    const permission = permissions.find(p => p.name === permissionName)
    if (permission) {
      rolePermissions.push(
        await prisma.rolePermission.create({
          data: {
            role: UserRole.SCANNER,
            permissionId: permission.id
          }
        })
      )
    }
  }

  console.log(`✅ Created ${rolePermissions.length} role permissions`)

  // Create Users
  console.log('👤 Creating users...')
  
  const users = await Promise.all([
    // Admin user
    prisma.user.create({
      data: {
        username: 'admin',
        email: '<EMAIL>',
        passwordHash: await hashPassword('Admin123!'),
        firstName: 'System',
        lastName: 'Administrator',
        role: UserRole.ADMIN,
        isActive: true,
        mustChangePassword: false,
        department: 'IT',
        position: 'System Administrator'
      }
    }),
    
    // Principal user
    prisma.user.create({
      data: {
        username: 'principal',
        email: '<EMAIL>',
        passwordHash: await hashPassword('Principal123!'),
        firstName: 'Maria',
        lastName: 'Santos',
        middleName: 'Cruz',
        role: UserRole.PRINCIPAL,
        isActive: true,
        mustChangePassword: false,
        phoneNumber: '+639171234567',
        department: 'Administration',
        position: 'School Principal'
      }
    }),
    
    // Teacher user
    prisma.user.create({
      data: {
        username: 'teacher1',
        email: '<EMAIL>',
        passwordHash: await hashPassword('Teacher123!'),
        firstName: 'Juan',
        lastName: 'Dela Cruz',
        middleName: 'Reyes',
        role: UserRole.TEACHER,
        isActive: true,
        mustChangePassword: false,
        phoneNumber: '+639171234568',
        department: 'Mathematics',
        position: 'Mathematics Teacher'
      }
    }),
    
    // Scanner user
    prisma.user.create({
      data: {
        username: 'scanner1',
        email: '<EMAIL>',
        passwordHash: await hashPassword('Scanner123!'),
        firstName: 'Gate',
        lastName: 'Scanner',
        role: UserRole.SCANNER,
        isActive: true,
        mustChangePassword: false,
        department: 'Security',
        position: 'Gate Scanner Operator'
      }
    })
  ])

  console.log(`✅ Created ${users.length} users`)

  console.log('🎉 Authentication system seeding completed successfully!')
  console.log(`
📊 Summary:
- Users: ${users.length}
- Permissions: ${permissions.length}
- Role Permissions: ${rolePermissions.length}

🔑 Default Login Credentials:
- Admin: admin / Admin123!
- Principal: principal / Principal123!
- Teacher: teacher1 / Teacher123!
- Scanner: scanner1 / Scanner123!
  `)
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
