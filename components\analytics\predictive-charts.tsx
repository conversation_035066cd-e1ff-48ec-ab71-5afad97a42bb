"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ReferenceLine,
  Brush
} from "recharts"
import { 
  TrendingUp, 
  TrendingDown, 
  Brain, 
  Target, 
  AlertTriangle,
  Info
} from "lucide-react"
import { TrendAnalysis, PredictiveModel } from "@/lib/types/analytics"

interface PredictiveChartsProps {
  trendData: TrendAnalysis
  models?: PredictiveModel[]
  onModelSelect?: (modelId: string) => void
}

export function PredictiveCharts({ trendData, models = [], onModelSelect }: PredictiveChartsProps) {
  const [selectedModel, setSelectedModel] = useState("attendance_forecast")
  const [confidenceThreshold, setConfidenceThreshold] = useState(0.7)
  const [showConfidenceBands, setShowConfidenceBands] = useState(true)

  // Combine historical and forecast data
  const chartData = useMemo(() => {
    const historical = trendData.data.map(item => ({
      ...item,
      type: 'historical',
      confidence: 1.0
    }))

    const forecast = trendData.forecast.map(item => ({
      date: item.date,
      value: null,
      predicted: item.predicted,
      confidence: item.confidence,
      type: 'forecast'
    }))

    return [...historical, ...forecast]
  }, [trendData])

  // Calculate trend statistics
  const trendStats = useMemo(() => {
    const values = trendData.data.map(d => d.value)
    const recent = values.slice(-7) // Last 7 days
    const previous = values.slice(-14, -7) // Previous 7 days

    const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length
    const previousAvg = previous.reduce((sum, val) => sum + val, 0) / previous.length
    const change = ((recentAvg - previousAvg) / previousAvg) * 100

    const forecastAvg = trendData.forecast.reduce((sum, f) => sum + f.predicted, 0) / trendData.forecast.length
    const forecastChange = ((forecastAvg - recentAvg) / recentAvg) * 100

    return {
      currentTrend: trendData.trend,
      weeklyChange: change,
      forecastChange,
      avgConfidence: trendData.forecast.reduce((sum, f) => sum + f.confidence, 0) / trendData.forecast.length,
      seasonality: trendData.seasonality
    }
  }, [trendData])

  // Get trend color and icon
  const getTrendIndicator = (trend: string, change: number) => {
    if (trend === 'increasing' || change > 0) {
      return {
        icon: <TrendingUp className="h-4 w-4 text-green-500" />,
        color: 'text-green-500',
        label: 'Improving'
      }
    } else if (trend === 'decreasing' || change < 0) {
      return {
        icon: <TrendingDown className="h-4 w-4 text-red-500" />,
        color: 'text-red-500',
        label: 'Declining'
      }
    } else {
      return {
        icon: <Target className="h-4 w-4 text-blue-500" />,
        color: 'text-blue-500',
        label: 'Stable'
      }
    }
  }

  const currentTrendIndicator = getTrendIndicator(trendStats.currentTrend, trendStats.weeklyChange)
  const forecastTrendIndicator = getTrendIndicator('', trendStats.forecastChange)

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      const isHistorical = data.type === 'historical'
      
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{new Date(label).toLocaleDateString()}</p>
          {isHistorical ? (
            <p className="text-blue-600">
              Actual: {(data.value * 100).toFixed(1)}%
            </p>
          ) : (
            <>
              <p className="text-orange-600">
                Predicted: {(data.predicted * 100).toFixed(1)}%
              </p>
              <p className="text-sm text-muted-foreground">
                Confidence: {(data.confidence * 100).toFixed(0)}%
              </p>
            </>
          )}
        </div>
      )
    }
    return null
  }

  // Filter forecast data by confidence threshold
  const filteredForecast = useMemo(() => {
    return chartData.map(item => ({
      ...item,
      predicted: item.confidence && item.confidence >= confidenceThreshold ? item.predicted : null
    }))
  }, [chartData, confidenceThreshold])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold flex items-center gap-2">
            <Brain className="h-6 w-6" />
            Predictive Analytics
          </h3>
          <p className="text-muted-foreground">
            AI-powered attendance forecasting and trend analysis
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedModel} onValueChange={setSelectedModel}>
            <SelectTrigger className="w-[200px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="attendance_forecast">Attendance Forecast</SelectItem>
              <SelectItem value="dropout_prediction">Dropout Prediction</SelectItem>
              <SelectItem value="risk_assessment">Risk Assessment</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Trend</CardTitle>
            {currentTrendIndicator.icon}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${currentTrendIndicator.color}`}>
              {currentTrendIndicator.label}
            </div>
            <p className="text-xs text-muted-foreground">
              {trendStats.weeklyChange > 0 ? '+' : ''}{trendStats.weeklyChange.toFixed(1)}% this week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Forecast Trend</CardTitle>
            {forecastTrendIndicator.icon}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${forecastTrendIndicator.color}`}>
              {forecastTrendIndicator.label}
            </div>
            <p className="text-xs text-muted-foreground">
              {trendStats.forecastChange > 0 ? '+' : ''}{trendStats.forecastChange.toFixed(1)}% predicted
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Model Confidence</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(trendStats.avgConfidence * 100)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Average prediction confidence
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Seasonality</CardTitle>
            <Info className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {trendStats.seasonality ? 'Yes' : 'No'}
            </div>
            <p className="text-xs text-muted-foreground">
              Seasonal patterns detected
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Chart */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Attendance Forecast</CardTitle>
              <CardDescription>
                Historical data with AI-powered predictions
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={showConfidenceBands ? "default" : "outline"}
                size="sm"
                onClick={() => setShowConfidenceBands(!showConfidenceBands)}
              >
                Confidence Bands
              </Button>
              <Select 
                value={confidenceThreshold.toString()} 
                onValueChange={(value) => setConfidenceThreshold(parseFloat(value))}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0.5">50% Confidence</SelectItem>
                  <SelectItem value="0.7">70% Confidence</SelectItem>
                  <SelectItem value="0.8">80% Confidence</SelectItem>
                  <SelectItem value="0.9">90% Confidence</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={filteredForecast}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
              />
              <YAxis 
                tickFormatter={(value) => `${(value * 100).toFixed(0)}%`}
                domain={['dataMin - 0.05', 'dataMax + 0.05']}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              
              {/* Historical data */}
              <Line
                type="monotone"
                dataKey="value"
                stroke="#3B82F6"
                strokeWidth={2}
                dot={{ fill: '#3B82F6', strokeWidth: 2, r: 3 }}
                name="Historical"
                connectNulls={false}
              />
              
              {/* Predicted data */}
              <Line
                type="monotone"
                dataKey="predicted"
                stroke="#F59E0B"
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={{ fill: '#F59E0B', strokeWidth: 2, r: 3 }}
                name="Predicted"
                connectNulls={false}
              />
              
              {/* Reference line for current date */}
              <ReferenceLine 
                x={new Date().toISOString().split('T')[0]} 
                stroke="#6B7280" 
                strokeDasharray="2 2"
                label="Today"
              />
              
              {/* Brush for zooming */}
              <Brush dataKey="date" height={30} />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Detailed Analysis */}
      <Tabs defaultValue="insights" className="space-y-4">
        <TabsList>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
          <TabsTrigger value="confidence">Confidence Analysis</TabsTrigger>
          <TabsTrigger value="scenarios">Scenarios</TabsTrigger>
        </TabsList>

        <TabsContent value="insights" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>AI-Generated Insights</CardTitle>
              <CardDescription>
                Automated analysis of attendance patterns and predictions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-3 p-4 bg-blue-50 rounded-lg">
                <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900">Pattern Recognition</h4>
                  <p className="text-sm text-blue-700">
                    The model has identified a weekly pattern with lower attendance on Fridays. 
                    This trend is expected to continue based on historical data.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-4 bg-yellow-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-900">Forecast Alert</h4>
                  <p className="text-sm text-yellow-700">
                    Predicted attendance may drop by 3-5% in the next two weeks. 
                    Consider implementing engagement strategies during this period.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-4 bg-green-50 rounded-lg">
                <TrendingUp className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-900">Positive Trend</h4>
                  <p className="text-sm text-green-700">
                    Overall attendance trend shows improvement over the past month. 
                    Current interventions appear to be effective.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="confidence" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Model Confidence Analysis</CardTitle>
              <CardDescription>
                Understanding prediction reliability and uncertainty
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={chartData.filter(d => d.type === 'forecast')}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  />
                  <YAxis tickFormatter={(value) => `${(value * 100).toFixed(0)}%`} />
                  <Tooltip />
                  <Area
                    type="monotone"
                    dataKey="confidence"
                    stroke="#8B5CF6"
                    fill="#8B5CF6"
                    fillOpacity={0.3}
                    name="Confidence Level"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scenarios" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Scenario Analysis</CardTitle>
              <CardDescription>
                What-if scenarios and their potential impact
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium text-green-700 mb-2">Best Case</h4>
                    <div className="text-2xl font-bold text-green-600">95%</div>
                    <p className="text-sm text-muted-foreground">
                      With optimal interventions
                    </p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium text-blue-700 mb-2">Expected</h4>
                    <div className="text-2xl font-bold text-blue-600">87%</div>
                    <p className="text-sm text-muted-foreground">
                      Current trajectory
                    </p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium text-red-700 mb-2">Worst Case</h4>
                    <div className="text-2xl font-bold text-red-600">78%</div>
                    <p className="text-sm text-muted-foreground">
                      Without interventions
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
