"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Calendar, Clock, TrendingUp, TrendingDown } from "lucide-react"
import { AttendanceHeatmapData } from "@/lib/types/analytics"

interface AttendanceHeatmapProps {
  data: AttendanceHeatmapData[]
  title?: string
  description?: string
  onDateRangeChange?: (range: { start: string; end: string }) => void
}

export function AttendanceHeatmap({ 
  data, 
  title = "Attendance Heatmap",
  description = "Visual representation of attendance patterns by day and time",
  onDateRangeChange 
}: AttendanceHeatmapProps) {
  const [selectedPeriod, setSelectedPeriod] = useState("30d")
  const [selectedGrade, setSelectedGrade] = useState("all")
  const [hoveredCell, setHoveredCell] = useState<AttendanceHeatmapData | null>(null)

  // Process data for heatmap
  const processedData = useMemo(() => {
    let filteredData = data

    // Filter by grade if selected
    if (selectedGrade !== "all") {
      filteredData = filteredData.filter(d => d.grade === selectedGrade)
    }

    // Filter by period
    const endDate = new Date()
    const startDate = new Date()
    
    switch (selectedPeriod) {
      case "7d":
        startDate.setDate(endDate.getDate() - 7)
        break
      case "30d":
        startDate.setDate(endDate.getDate() - 30)
        break
      case "90d":
        startDate.setDate(endDate.getDate() - 90)
        break
    }

    filteredData = filteredData.filter(d => {
      const date = new Date(d.date)
      return date >= startDate && date <= endDate
    })

    // Group by date and hour
    const grouped = filteredData.reduce((acc, item) => {
      const key = `${item.date}-${item.hour}`
      if (!acc[key]) {
        acc[key] = { ...item, count: 1 }
      } else {
        acc[key].value += item.value
        acc[key].rate = (acc[key].rate + item.rate) / 2
        acc[key].count += 1
      }
      return acc
    }, {} as Record<string, AttendanceHeatmapData & { count: number }>)

    return Object.values(grouped)
  }, [data, selectedPeriod, selectedGrade])

  // Get unique dates and hours for grid
  const dates = useMemo(() => {
    const uniqueDates = [...new Set(processedData.map(d => d.date))].sort()
    return uniqueDates.slice(-30) // Show last 30 days max for readability
  }, [processedData])

  const hours = useMemo(() => {
    return Array.from({ length: 10 }, (_, i) => i + 7) // 7 AM to 4 PM
  }, [])

  // Get color intensity based on attendance rate
  const getColorIntensity = (rate: number) => {
    if (rate >= 0.9) return "bg-green-500"
    if (rate >= 0.8) return "bg-green-400"
    if (rate >= 0.7) return "bg-yellow-400"
    if (rate >= 0.6) return "bg-orange-400"
    if (rate >= 0.5) return "bg-red-400"
    return "bg-red-500"
  }

  const getOpacity = (rate: number) => {
    return Math.max(0.1, rate)
  }

  // Find data for specific date and hour
  const getCellData = (date: string, hour: number): AttendanceHeatmapData | null => {
    return processedData.find(d => d.date === date && d.hour === hour) || null
  }

  // Calculate statistics
  const stats = useMemo(() => {
    if (processedData.length === 0) return null

    const rates = processedData.map(d => d.rate)
    const avgRate = rates.reduce((sum, rate) => sum + rate, 0) / rates.length
    const maxRate = Math.max(...rates)
    const minRate = Math.min(...rates)

    // Find peak hours
    const hourlyAvg = hours.map(hour => {
      const hourData = processedData.filter(d => d.hour === hour)
      const avgForHour = hourData.length > 0 
        ? hourData.reduce((sum, d) => sum + d.rate, 0) / hourData.length 
        : 0
      return { hour, rate: avgForHour }
    }).sort((a, b) => b.rate - a.rate)

    const peakHour = hourlyAvg[0]
    const lowHour = hourlyAvg[hourlyAvg.length - 1]

    return {
      avgRate,
      maxRate,
      minRate,
      peakHour: peakHour.hour,
      peakRate: peakHour.rate,
      lowHour: lowHour.hour,
      lowRate: lowHour.rate
    }
  }, [processedData, hours])

  const formatHour = (hour: number) => {
    return `${hour}:00`
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {title}
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-[120px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedGrade} onValueChange={setSelectedGrade}>
              <SelectTrigger className="w-[120px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Grades</SelectItem>
                <SelectItem value="7">Grade 7</SelectItem>
                <SelectItem value="8">Grade 8</SelectItem>
                <SelectItem value="9">Grade 9</SelectItem>
                <SelectItem value="10">Grade 10</SelectItem>
                <SelectItem value="11">Grade 11</SelectItem>
                <SelectItem value="12">Grade 12</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Statistics */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">
                {Math.round(stats.avgRate * 100)}%
              </div>
              <div className="text-sm text-muted-foreground">Average Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatHour(stats.peakHour)}
              </div>
              <div className="text-sm text-muted-foreground">Peak Hour</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {formatHour(stats.lowHour)}
              </div>
              <div className="text-sm text-muted-foreground">Low Hour</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {Math.round((stats.maxRate - stats.minRate) * 100)}%
              </div>
              <div className="text-sm text-muted-foreground">Variation</div>
            </div>
          </div>
        )}

        {/* Heatmap Grid */}
        <div className="overflow-x-auto">
          <div className="min-w-[800px]">
            {/* Hour labels */}
            <div className="flex mb-2">
              <div className="w-20"></div> {/* Space for date labels */}
              {hours.map(hour => (
                <div key={hour} className="flex-1 text-center text-sm font-medium text-muted-foreground">
                  {formatHour(hour)}
                </div>
              ))}
            </div>

            {/* Heatmap rows */}
            <TooltipProvider>
              <div className="space-y-1">
                {dates.map(date => (
                  <div key={date} className="flex items-center">
                    <div className="w-20 text-sm text-muted-foreground pr-2">
                      {formatDate(date)}
                    </div>
                    <div className="flex flex-1 gap-1">
                      {hours.map(hour => {
                        const cellData = getCellData(date, hour)
                        const rate = cellData?.rate || 0
                        const value = cellData?.value || 0

                        return (
                          <Tooltip key={`${date}-${hour}`}>
                            <TooltipTrigger asChild>
                              <div
                                className={`
                                  flex-1 h-8 rounded cursor-pointer border border-border/50
                                  ${getColorIntensity(rate)}
                                  hover:ring-2 hover:ring-primary/50
                                  transition-all duration-200
                                `}
                                style={{ opacity: getOpacity(rate) }}
                                onMouseEnter={() => setHoveredCell(cellData)}
                                onMouseLeave={() => setHoveredCell(null)}
                              />
                            </TooltipTrigger>
                            <TooltipContent>
                              <div className="text-sm">
                                <div className="font-medium">
                                  {formatDate(date)} at {formatHour(hour)}
                                </div>
                                <div>Attendance: {value} students</div>
                                <div>Rate: {Math.round(rate * 100)}%</div>
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        )
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </TooltipProvider>
          </div>
        </div>

        {/* Legend */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium">Attendance Rate:</span>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span className="text-xs">Low (0-50%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-orange-400 rounded"></div>
              <span className="text-xs">Medium (50-70%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-yellow-400 rounded"></div>
              <span className="text-xs">Good (70-80%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-400 rounded"></div>
              <span className="text-xs">High (80-90%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span className="text-xs">Excellent (90%+)</span>
            </div>
          </div>
        </div>

        {/* Hovered cell details */}
        {hoveredCell && (
          <div className="p-4 bg-muted rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4" />
              <span className="font-medium">
                {formatDate(hoveredCell.date)} at {formatHour(hoveredCell.hour)}
              </span>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Students Present:</span>
                <span className="ml-2 font-medium">{hoveredCell.value}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Attendance Rate:</span>
                <span className="ml-2 font-medium">{Math.round(hoveredCell.rate * 100)}%</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
