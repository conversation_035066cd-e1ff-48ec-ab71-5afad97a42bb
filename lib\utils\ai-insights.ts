// AI-powered insights and analytics utilities for QRSAMS

import { 
  StudentRiskAssessment, 
  RiskFactor, 
  AIInsight, 
  AttendancePattern,
  InterventionRecommendation,
  TrendAnalysis,
  PredictiveModel
} from "@/lib/types/analytics"
import { Student } from "@/lib/types/student"
import { AttendanceRecord } from "@/lib/types/scanner"

// Risk Assessment Algorithm
export class RiskAssessmentEngine {
  static calculateRiskScore(
    student: Student, 
    attendanceRecords: AttendanceRecord[],
    patterns: AttendancePattern
  ): number {
    let riskScore = 0
    
    // Attendance rate factor (40% weight)
    const attendanceRate = student.attendanceStats?.attendanceRate || 0
    if (attendanceRate < 0.7) riskScore += 40
    else if (attendanceRate < 0.8) riskScore += 25
    else if (attendanceRate < 0.9) riskScore += 10
    
    // Trend analysis (30% weight)
    const recentTrend = this.calculateRecentTrend(attendanceRecords)
    if (recentTrend < -0.1) riskScore += 30
    else if (recentTrend < -0.05) riskScore += 20
    else if (recentTrend < 0) riskScore += 10
    
    // Pattern irregularity (20% weight)
    const patternScore = this.analyzePatternIrregularity(patterns)
    riskScore += patternScore * 0.2
    
    // Engagement factors (10% weight)
    const engagementScore = this.calculateEngagementScore(attendanceRecords)
    riskScore += (1 - engagementScore) * 10
    
    return Math.min(100, Math.max(0, riskScore))
  }
  
  static calculateRecentTrend(records: AttendanceRecord[]): number {
    if (records.length < 10) return 0
    
    const recent = records.slice(-30) // Last 30 records
    const older = records.slice(-60, -30) // Previous 30 records
    
    const recentRate = recent.filter(r => r.status === 'present').length / recent.length
    const olderRate = older.filter(r => r.status === 'present').length / older.length
    
    return recentRate - olderRate
  }
  
  static analyzePatternIrregularity(patterns: AttendancePattern): number {
    const weeklyVariance = this.calculateVariance(patterns.patterns.weeklyTrend)
    const timeVariance = this.calculateVariance(patterns.patterns.timeOfDayPattern)
    
    return (weeklyVariance + timeVariance) / 2
  }
  
  static calculateVariance(data: number[]): number {
    const mean = data.reduce((a, b) => a + b, 0) / data.length
    const variance = data.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / data.length
    return Math.sqrt(variance) / mean // Coefficient of variation
  }
  
  static calculateEngagementScore(records: AttendanceRecord[]): number {
    const lateCount = records.filter(r => r.status === 'late').length
    const totalCount = records.length
    
    return Math.max(0, 1 - (lateCount / totalCount) * 2)
  }
  
  static identifyRiskFactors(
    student: Student,
    attendanceRecords: AttendanceRecord[],
    patterns: AttendancePattern
  ): RiskFactor[] {
    const factors: RiskFactor[] = []
    
    // Low attendance
    const attendanceRate = student.attendanceStats?.attendanceRate || 0
    if (attendanceRate < 0.8) {
      factors.push({
        type: 'attendance',
        severity: attendanceRate < 0.6 ? 'critical' : attendanceRate < 0.7 ? 'high' : 'medium',
        description: `Low attendance rate: ${(attendanceRate * 100).toFixed(1)}%`,
        impact: (1 - attendanceRate) * 100,
        trend: this.calculateRecentTrend(attendanceRecords) < 0 ? 'declining' : 'stable',
        firstDetected: new Date().toISOString(),
        lastOccurrence: new Date().toISOString()
      })
    }
    
    // Chronic lateness
    const lateRate = attendanceRecords.filter(r => r.status === 'late').length / attendanceRecords.length
    if (lateRate > 0.2) {
      factors.push({
        type: 'punctuality',
        severity: lateRate > 0.4 ? 'high' : 'medium',
        description: `Frequent tardiness: ${(lateRate * 100).toFixed(1)}% of days`,
        impact: lateRate * 50,
        trend: 'stable',
        firstDetected: new Date().toISOString(),
        lastOccurrence: new Date().toISOString()
      })
    }
    
    // Irregular patterns
    const patternIrregularity = this.analyzePatternIrregularity(patterns)
    if (patternIrregularity > 0.3) {
      factors.push({
        type: 'behavioral',
        severity: 'medium',
        description: 'Irregular attendance patterns detected',
        impact: patternIrregularity * 30,
        trend: 'stable',
        firstDetected: new Date().toISOString(),
        lastOccurrence: new Date().toISOString()
      })
    }
    
    return factors
  }
}

// Pattern Detection Engine
export class PatternDetectionEngine {
  static detectAnomalies(data: number[], threshold: number = 2): number[] {
    const mean = data.reduce((a, b) => a + b, 0) / data.length
    const stdDev = Math.sqrt(
      data.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / data.length
    )
    
    return data
      .map((value, index) => ({ value, index }))
      .filter(({ value }) => Math.abs(value - mean) > threshold * stdDev)
      .map(({ index }) => index)
  }
  
  static identifyTrends(data: Array<{ date: string; value: number }>): TrendAnalysis {
    const values = data.map(d => d.value)
    const n = values.length
    
    // Simple linear regression for trend
    const sumX = data.reduce((acc, _, i) => acc + i, 0)
    const sumY = values.reduce((acc, val) => acc + val, 0)
    const sumXY = data.reduce((acc, d, i) => acc + i * d.value, 0)
    const sumXX = data.reduce((acc, _, i) => acc + i * i, 0)
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
    
    let trend: 'increasing' | 'decreasing' | 'stable' | 'volatile'
    if (Math.abs(slope) < 0.01) trend = 'stable'
    else if (slope > 0.05) trend = 'increasing'
    else if (slope < -0.05) trend = 'decreasing'
    else trend = 'volatile'
    
    // Generate forecast (simple linear projection)
    const forecast = Array.from({ length: 7 }, (_, i) => ({
      date: new Date(Date.now() + (i + 1) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      predicted: values[values.length - 1] + slope * (i + 1),
      confidence: Math.max(0.5, 1 - Math.abs(slope) * 10)
    }))
    
    return {
      period: 'daily',
      data: data.map(d => ({ date: d.date, value: d.value })),
      trend,
      seasonality: this.detectSeasonality(values),
      forecast
    }
  }
  
  static detectSeasonality(data: number[]): boolean {
    if (data.length < 14) return false
    
    // Simple autocorrelation check for weekly patterns
    const weeklyCorrelation = this.calculateAutocorrelation(data, 7)
    return weeklyCorrelation > 0.3
  }
  
  static calculateAutocorrelation(data: number[], lag: number): number {
    if (data.length <= lag) return 0
    
    const n = data.length - lag
    const mean = data.reduce((a, b) => a + b, 0) / data.length
    
    let numerator = 0
    let denominator = 0
    
    for (let i = 0; i < n; i++) {
      numerator += (data[i] - mean) * (data[i + lag] - mean)
    }
    
    for (let i = 0; i < data.length; i++) {
      denominator += Math.pow(data[i] - mean, 2)
    }
    
    return numerator / denominator
  }
}

// Recommendation Engine
export class RecommendationEngine {
  static generateInterventions(assessment: StudentRiskAssessment): InterventionRecommendation[] {
    const interventions: InterventionRecommendation[] = []
    
    assessment.riskFactors.forEach(factor => {
      switch (factor.type) {
        case 'attendance':
          if (factor.severity === 'critical' || factor.severity === 'high') {
            interventions.push({
              id: `INT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              type: 'counseling',
              priority: factor.severity === 'critical' ? 'urgent' : 'high',
              title: 'Attendance Counseling Session',
              description: 'Schedule immediate counseling to address attendance issues and identify barriers',
              estimatedImpact: 70,
              timeframe: '1-2 weeks',
              status: 'pending',
              createdAt: new Date().toISOString(),
              dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
            })
          }
          break
          
        case 'punctuality':
          interventions.push({
            id: `INT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: 'schedule_adjustment',
            priority: 'medium',
            title: 'Schedule Review and Adjustment',
            description: 'Review student schedule and transportation options to improve punctuality',
            estimatedImpact: 50,
            timeframe: '2-3 weeks',
            status: 'pending',
            createdAt: new Date().toISOString(),
            dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString()
          })
          break
          
        case 'behavioral':
          interventions.push({
            id: `INT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: 'peer_mentoring',
            priority: 'medium',
            title: 'Peer Mentoring Program',
            description: 'Assign peer mentor to provide support and guidance',
            estimatedImpact: 60,
            timeframe: '4-6 weeks',
            status: 'pending',
            createdAt: new Date().toISOString(),
            dueDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString()
          })
          break
      }
    })
    
    // Parent engagement intervention for high-risk students
    if (assessment.riskLevel === 'high' || assessment.riskLevel === 'critical') {
      interventions.push({
        id: `INT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'parent_meeting',
        priority: 'high',
        title: 'Parent Conference',
        description: 'Schedule meeting with parents to discuss concerns and develop support plan',
        estimatedImpact: 80,
        timeframe: '1 week',
        status: 'pending',
        createdAt: new Date().toISOString(),
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      })
    }
    
    return interventions
  }
}

// AI Insights Generator
export class AIInsightsGenerator {
  static generateInsights(
    students: Student[],
    attendanceRecords: AttendanceRecord[],
    patterns: AttendancePattern[]
  ): AIInsight[] {
    const insights: AIInsight[] = []
    
    // Detect attendance drops
    const attendanceDrops = this.detectAttendanceDrops(students, attendanceRecords)
    if (attendanceDrops.length > 0) {
      insights.push({
        id: `INS_${Date.now()}_1`,
        type: 'anomaly',
        severity: 'warning',
        title: 'Attendance Drop Detected',
        description: `${attendanceDrops.length} students showing significant attendance decline`,
        confidence: 0.85,
        affectedStudents: attendanceDrops,
        actionRequired: true,
        suggestedActions: [
          'Review individual student circumstances',
          'Schedule counseling sessions',
          'Contact parents/guardians'
        ],
        createdAt: new Date().toISOString()
      })
    }
    
    // Identify high-risk periods
    const riskPeriods = this.identifyHighRiskPeriods(attendanceRecords)
    if (riskPeriods.length > 0) {
      insights.push({
        id: `INS_${Date.now()}_2`,
        type: 'pattern',
        severity: 'info',
        title: 'High-Risk Time Periods Identified',
        description: `Attendance typically drops during: ${riskPeriods.join(', ')}`,
        confidence: 0.75,
        actionRequired: false,
        suggestedActions: [
          'Plan engaging activities during these periods',
          'Increase monitoring and support',
          'Consider schedule adjustments'
        ],
        createdAt: new Date().toISOString()
      })
    }
    
    return insights
  }
  
  static detectAttendanceDrops(students: Student[], records: AttendanceRecord[]): string[] {
    return students
      .filter(student => {
        const studentRecords = records.filter(r => r.studentId === student.id)
        if (studentRecords.length < 20) return false
        
        const recent = studentRecords.slice(-10)
        const older = studentRecords.slice(-20, -10)
        
        const recentRate = recent.filter(r => r.status === 'present').length / recent.length
        const olderRate = older.filter(r => r.status === 'present').length / older.length
        
        return (olderRate - recentRate) > 0.2 // 20% drop
      })
      .map(student => student.id)
  }
  
  static identifyHighRiskPeriods(records: AttendanceRecord[]): string[] {
    const periods: string[] = []
    
    // Analyze by day of week
    const dayStats = Array.from({ length: 7 }, (_, i) => {
      const dayRecords = records.filter(r => new Date(r.timestamp).getDay() === i)
      const attendanceRate = dayRecords.filter(r => r.status === 'present').length / dayRecords.length
      return { day: i, rate: attendanceRate }
    })
    
    const avgRate = dayStats.reduce((acc, d) => acc + d.rate, 0) / dayStats.length
    const lowDays = dayStats.filter(d => d.rate < avgRate - 0.1)
    
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    periods.push(...lowDays.map(d => dayNames[d.day]))
    
    return periods
  }
}
