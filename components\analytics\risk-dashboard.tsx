"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  AlertTriangle, 
  Users, 
  TrendingUp, 
  Search,
  Filter,
  Download,
  RefreshCw
} from "lucide-react"
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from "recharts"
import { StudentRiskAssessment } from "@/lib/types/analytics"
import { RiskAssessmentCard } from "./risk-assessment-card"
import { CHART_COLORS } from "@/lib/config/analytics"

interface RiskDashboardProps {
  assessments: StudentRiskAssessment[]
  onRefresh?: () => void
  onExport?: (format: string) => void
}

export function RiskDashboard({ assessments, onRefresh, onExport }: RiskDashboardProps) {
  const [filteredAssessments, setFilteredAssessments] = useState(assessments)
  const [searchTerm, setSearchTerm] = useState("")
  const [riskLevelFilter, setRiskLevelFilter] = useState("all")
  const [selectedStudent, setSelectedStudent] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Filter assessments based on search and filters
  useEffect(() => {
    let filtered = assessments

    if (searchTerm) {
      filtered = filtered.filter(assessment => 
        assessment.studentId.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (riskLevelFilter !== "all") {
      filtered = filtered.filter(assessment => assessment.riskLevel === riskLevelFilter)
    }

    setFilteredAssessments(filtered)
  }, [assessments, searchTerm, riskLevelFilter])

  // Calculate summary statistics
  const summary = {
    total: assessments.length,
    critical: assessments.filter(a => a.riskLevel === 'critical').length,
    high: assessments.filter(a => a.riskLevel === 'high').length,
    medium: assessments.filter(a => a.riskLevel === 'medium').length,
    low: assessments.filter(a => a.riskLevel === 'low').length,
    averageRiskScore: assessments.reduce((sum, a) => sum + a.riskScore, 0) / assessments.length,
    activeInterventions: assessments.reduce((sum, a) => 
      sum + a.interventions.filter(i => i.status === 'in_progress' || i.status === 'pending').length, 0
    )
  }

  // Data for charts
  const riskDistributionData = [
    { name: 'Low Risk', value: summary.low, color: CHART_COLORS.RISK_LEVELS.low },
    { name: 'Medium Risk', value: summary.medium, color: CHART_COLORS.RISK_LEVELS.medium },
    { name: 'High Risk', value: summary.high, color: CHART_COLORS.RISK_LEVELS.high },
    { name: 'Critical Risk', value: summary.critical, color: CHART_COLORS.RISK_LEVELS.critical }
  ]

  const riskFactorData = assessments.reduce((acc, assessment) => {
    assessment.riskFactors.forEach(factor => {
      const existing = acc.find(item => item.name === factor.type)
      if (existing) {
        existing.count += 1
        existing.totalImpact += factor.impact
      } else {
        acc.push({
          name: factor.type.replace('_', ' ').toUpperCase(),
          count: 1,
          totalImpact: factor.impact,
          averageImpact: factor.impact
        })
      }
    })
    return acc
  }, [] as Array<{ name: string; count: number; totalImpact: number; averageImpact: number }>)

  // Calculate average impact for each factor type
  riskFactorData.forEach(item => {
    item.averageImpact = item.totalImpact / item.count
  })

  const handleRefresh = async () => {
    setIsLoading(true)
    await onRefresh?.()
    setIsLoading(false)
  }

  const handleInterventionUpdate = (interventionId: string, status: string) => {
    // In a real implementation, this would update the intervention status
    console.log(`Updating intervention ${interventionId} to ${status}`)
  }

  const handleReassess = (studentId: string) => {
    // In a real implementation, this would trigger a risk reassessment
    console.log(`Reassessing student ${studentId}`)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Risk Assessment Dashboard</h2>
          <p className="text-muted-foreground">
            AI-powered student risk analysis and intervention tracking
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={() => onExport?.('pdf')}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.total}</div>
            <p className="text-xs text-muted-foreground">
              Under risk assessment
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Risk Students</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">
              {summary.critical + summary.high}
            </div>
            <p className="text-xs text-muted-foreground">
              Require immediate attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Risk Score</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(summary.averageRiskScore)}
            </div>
            <p className="text-xs text-muted-foreground">
              Out of 100
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Interventions</CardTitle>
            <AlertTriangle className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-500">
              {summary.activeInterventions}
            </div>
            <p className="text-xs text-muted-foreground">
              In progress or pending
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="students">Student Details</TabsTrigger>
          <TabsTrigger value="analytics">Risk Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Risk Level Distribution</CardTitle>
                <CardDescription>Current risk assessment breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={riskDistributionData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {riskDistributionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Risk Factors Analysis</CardTitle>
                <CardDescription>Most common risk factors and their impact</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={riskFactorData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#3B82F6" name="Count" />
                    <Bar dataKey="averageImpact" fill="#EF4444" name="Avg Impact" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="students" className="space-y-4">
          {/* Filters */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by student ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={riskLevelFilter} onValueChange={setRiskLevelFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by risk level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Risk Levels</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Student List */}
          <div className="space-y-4">
            {filteredAssessments.map((assessment) => (
              <div key={assessment.studentId}>
                <div 
                  className="flex items-center justify-between p-4 border rounded-lg cursor-pointer hover:bg-muted/50"
                  onClick={() => setSelectedStudent(
                    selectedStudent === assessment.studentId ? null : assessment.studentId
                  )}
                >
                  <div className="flex items-center gap-4">
                    <div>
                      <h3 className="font-medium">Student {assessment.studentId}</h3>
                      <p className="text-sm text-muted-foreground">
                        Risk Score: {assessment.riskScore}/100 • 
                        Dropout Risk: {Math.round(assessment.dropoutProbability * 100)}%
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={
                      assessment.riskLevel === 'critical' ? 'destructive' :
                      assessment.riskLevel === 'high' ? 'destructive' :
                      assessment.riskLevel === 'medium' ? 'secondary' : 'default'
                    }>
                      {assessment.riskLevel.toUpperCase()}
                    </Badge>
                    {assessment.interventions.filter(i => i.status === 'pending').length > 0 && (
                      <Badge variant="outline">
                        {assessment.interventions.filter(i => i.status === 'pending').length} Pending
                      </Badge>
                    )}
                  </div>
                </div>
                
                {selectedStudent === assessment.studentId && (
                  <div className="mt-4">
                    <RiskAssessmentCard
                      assessment={assessment}
                      onInterventionUpdate={handleInterventionUpdate}
                      onReassess={handleReassess}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Advanced Risk Analytics</CardTitle>
              <CardDescription>Detailed analysis and trends</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
                  <p>Advanced analytics coming soon...</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
