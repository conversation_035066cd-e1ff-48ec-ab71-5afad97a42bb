"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from "recharts"
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Clock, 
  AlertTriangle,
  Award,
  Target
} from "lucide-react"
import { GradeComparison, AttendanceMetrics } from "@/lib/types/analytics"
import { CHART_COLORS } from "@/lib/config/analytics"

interface GradeComparisonChartProps {
  gradeData: GradeComparison[]
  onGradeSelect?: (grade: string) => void
}

export function GradeComparisonChart({ gradeData, onGradeSelect }: GradeComparisonChartProps) {
  const [selectedMetric, setSelectedMetric] = useState("attendanceRate")
  const [chartType, setChartType] = useState("bar")
  const [selectedGrade, setSelectedGrade] = useState<string | null>(null)

  // Prepare data for different chart types
  const chartData = useMemo(() => {
    return gradeData.map(grade => ({
      grade: `Grade ${grade.grade}`,
      gradeNumber: grade.grade,
      attendanceRate: grade.metrics.attendanceRate * 100,
      punctualityRate: grade.metrics.punctualityRate * 100,
      totalStudents: grade.metrics.totalStudents,
      presentCount: grade.metrics.presentCount,
      lateCount: grade.metrics.lateCount,
      absentCount: grade.metrics.absentCount,
      riskHigh: grade.riskDistribution.high + grade.riskDistribution.critical,
      riskMedium: grade.riskDistribution.medium,
      riskLow: grade.riskDistribution.low
    }))
  }, [gradeData])

  // Radar chart data for comprehensive comparison
  const radarData = useMemo(() => {
    return gradeData.map(grade => ({
      grade: `Grade ${grade.grade}`,
      attendance: grade.metrics.attendanceRate * 100,
      punctuality: grade.metrics.punctualityRate * 100,
      engagement: 100 - ((grade.riskDistribution.high + grade.riskDistribution.critical) / grade.metrics.totalStudents * 100),
      performance: Math.random() * 20 + 80 // Mock performance score
    }))
  }, [gradeData])

  // Calculate overall statistics
  const overallStats = useMemo(() => {
    const totalStudents = gradeData.reduce((sum, grade) => sum + grade.metrics.totalStudents, 0)
    const totalPresent = gradeData.reduce((sum, grade) => sum + grade.metrics.presentCount, 0)
    const totalLate = gradeData.reduce((sum, grade) => sum + grade.metrics.lateCount, 0)
    const totalAbsent = gradeData.reduce((sum, grade) => sum + grade.metrics.absentCount, 0)

    const overallAttendanceRate = totalStudents > 0 ? (totalPresent + totalLate) / totalStudents : 0
    const overallPunctualityRate = totalStudents > 0 ? totalPresent / totalStudents : 0

    // Find best and worst performing grades
    const sortedByAttendance = [...gradeData].sort((a, b) => b.metrics.attendanceRate - a.metrics.attendanceRate)
    const bestGrade = sortedByAttendance[0]
    const worstGrade = sortedByAttendance[sortedByAttendance.length - 1]

    return {
      totalStudents,
      overallAttendanceRate,
      overallPunctualityRate,
      bestGrade: bestGrade?.grade,
      worstGrade: worstGrade?.grade,
      bestRate: bestGrade?.metrics.attendanceRate * 100,
      worstRate: worstGrade?.metrics.attendanceRate * 100
    }
  }, [gradeData])

  // Get metric label and color
  const getMetricInfo = (metric: string) => {
    switch (metric) {
      case 'attendanceRate':
        return { label: 'Attendance Rate (%)', color: '#3B82F6' }
      case 'punctualityRate':
        return { label: 'Punctuality Rate (%)', color: '#10B981' }
      case 'totalStudents':
        return { label: 'Total Students', color: '#8B5CF6' }
      case 'riskHigh':
        return { label: 'High Risk Students', color: '#EF4444' }
      default:
        return { label: 'Attendance Rate (%)', color: '#3B82F6' }
    }
  }

  const metricInfo = getMetricInfo(selectedMetric)

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium mb-2">{label}</p>
          <div className="space-y-1">
            <p className="text-sm">
              <span className="text-muted-foreground">Students:</span>
              <span className="ml-2 font-medium">{data.totalStudents}</span>
            </p>
            <p className="text-sm">
              <span className="text-muted-foreground">Attendance:</span>
              <span className="ml-2 font-medium">{data.attendanceRate.toFixed(1)}%</span>
            </p>
            <p className="text-sm">
              <span className="text-muted-foreground">Punctuality:</span>
              <span className="ml-2 font-medium">{data.punctualityRate.toFixed(1)}%</span>
            </p>
            <p className="text-sm">
              <span className="text-muted-foreground">High Risk:</span>
              <span className="ml-2 font-medium">{data.riskHigh}</span>
            </p>
          </div>
        </div>
      )
    }
    return null
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold flex items-center gap-2">
            <Award className="h-6 w-6" />
            Grade Level Comparison
          </h3>
          <p className="text-muted-foreground">
            Comparative analysis across different grade levels
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedMetric} onValueChange={setSelectedMetric}>
            <SelectTrigger className="w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="attendanceRate">Attendance Rate</SelectItem>
              <SelectItem value="punctualityRate">Punctuality Rate</SelectItem>
              <SelectItem value="totalStudents">Total Students</SelectItem>
              <SelectItem value="riskHigh">High Risk Students</SelectItem>
            </SelectContent>
          </Select>
          <Select value={chartType} onValueChange={setChartType}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="bar">Bar Chart</SelectItem>
              <SelectItem value="line">Line Chart</SelectItem>
              <SelectItem value="radar">Radar Chart</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallStats.totalStudents}</div>
            <p className="text-xs text-muted-foreground">
              Across all grades
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overall Attendance</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(overallStats.overallAttendanceRate * 100).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              School-wide average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Best Performing</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              Grade {overallStats.bestGrade}
            </div>
            <p className="text-xs text-muted-foreground">
              {overallStats.bestRate?.toFixed(1)}% attendance
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Needs Attention</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              Grade {overallStats.worstGrade}
            </div>
            <p className="text-xs text-muted-foreground">
              {overallStats.worstRate?.toFixed(1)}% attendance
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Chart */}
      <Card>
        <CardHeader>
          <CardTitle>{metricInfo.label} by Grade Level</CardTitle>
          <CardDescription>
            Comparative view of {metricInfo.label.toLowerCase()} across all grades
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            {chartType === 'bar' && (
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="grade" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey={selectedMetric} 
                  fill={metricInfo.color}
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            )}
            
            {chartType === 'line' && (
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="grade" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Line 
                  type="monotone" 
                  dataKey={selectedMetric} 
                  stroke={metricInfo.color}
                  strokeWidth={3}
                  dot={{ fill: metricInfo.color, strokeWidth: 2, r: 6 }}
                />
              </LineChart>
            )}
            
            {chartType === 'radar' && (
              <RadarChart data={radarData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="grade" />
                <PolarRadiusAxis angle={90} domain={[0, 100]} />
                <Radar
                  name="Attendance"
                  dataKey="attendance"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.1}
                />
                <Radar
                  name="Punctuality"
                  dataKey="punctuality"
                  stroke="#10B981"
                  fill="#10B981"
                  fillOpacity={0.1}
                />
                <Radar
                  name="Engagement"
                  dataKey="engagement"
                  stroke="#8B5CF6"
                  fill="#8B5CF6"
                  fillOpacity={0.1}
                />
                <Legend />
                <Tooltip />
              </RadarChart>
            )}
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Detailed Analysis */}
      <Tabs defaultValue="breakdown" className="space-y-4">
        <TabsList>
          <TabsTrigger value="breakdown">Detailed Breakdown</TabsTrigger>
          <TabsTrigger value="trends">Grade Trends</TabsTrigger>
          <TabsTrigger value="risks">Risk Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="breakdown" className="space-y-4">
          <div className="grid gap-4">
            {gradeData.map((grade) => (
              <Card key={grade.grade} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader 
                  className="pb-3"
                  onClick={() => {
                    setSelectedGrade(selectedGrade === grade.grade ? null : grade.grade)
                    onGradeSelect?.(grade.grade)
                  }}
                >
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Grade {grade.grade}</CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {grade.metrics.totalStudents} students
                      </Badge>
                      <Badge 
                        variant={grade.metrics.attendanceRate >= 0.9 ? "default" : 
                                grade.metrics.attendanceRate >= 0.8 ? "secondary" : "destructive"}
                      >
                        {(grade.metrics.attendanceRate * 100).toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                
                {selectedGrade === grade.grade && (
                  <CardContent className="pt-0">
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {grade.metrics.presentCount}
                        </div>
                        <div className="text-sm text-green-700">Present Today</div>
                      </div>
                      <div className="text-center p-3 bg-yellow-50 rounded-lg">
                        <div className="text-2xl font-bold text-yellow-600">
                          {grade.metrics.lateCount}
                        </div>
                        <div className="text-sm text-yellow-700">Late Today</div>
                      </div>
                      <div className="text-center p-3 bg-red-50 rounded-lg">
                        <div className="text-2xl font-bold text-red-600">
                          {grade.metrics.absentCount}
                        </div>
                        <div className="text-sm text-red-700">Absent Today</div>
                      </div>
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {(grade.metrics.punctualityRate * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-blue-700">Punctuality</div>
                      </div>
                    </div>
                    
                    <div className="mt-4 pt-4 border-t">
                      <h4 className="font-medium mb-2">Risk Distribution</h4>
                      <div className="flex gap-2">
                        <Badge variant="default" className="bg-green-500">
                          Low: {grade.riskDistribution.low}
                        </Badge>
                        <Badge variant="secondary">
                          Medium: {grade.riskDistribution.medium}
                        </Badge>
                        <Badge variant="destructive">
                          High: {grade.riskDistribution.high}
                        </Badge>
                        <Badge variant="destructive" className="bg-red-700">
                          Critical: {grade.riskDistribution.critical}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Grade-Level Trends</CardTitle>
              <CardDescription>
                Historical performance trends by grade
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4" />
                  <p>Historical trend analysis coming soon...</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="risks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Risk Analysis by Grade</CardTitle>
              <CardDescription>
                Detailed risk assessment across grade levels
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="grade" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="riskLow" stackId="risk" fill={CHART_COLORS.RISK_LEVELS.low} name="Low Risk" />
                  <Bar dataKey="riskMedium" stackId="risk" fill={CHART_COLORS.RISK_LEVELS.medium} name="Medium Risk" />
                  <Bar dataKey="riskHigh" stackId="risk" fill={CHART_COLORS.RISK_LEVELS.high} name="High Risk" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
