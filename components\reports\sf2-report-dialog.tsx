"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { SF2ReportGenerator } from "./sf2-report-generator"
import { SF2ReportPreview } from "./sf2-report-preview"
import { ReportConfig, SF2Report, ExportFormat } from "@/lib/types/reports"
import { ArrowLeft, FileText, Eye } from "lucide-react"
import { toast } from "sonner"

interface SF2ReportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  config: ReportConfig
}

export function SF2ReportDialog({ open, onOpenChange, config }: SF2ReportDialogProps) {
  const [activeTab, setActiveTab] = useState("generate")
  const [generatedReport, setGeneratedReport] = useState<SF2Report | null>(null)

  const handleGenerateReport = (report: SF2Report) => {
    setGeneratedReport(report)
    setActiveTab("preview")
    toast.success("SF2 report generated successfully", {
      description: "You can now preview, download, or print the report"
    })
  }

  const handlePreviewReport = (report: SF2Report) => {
    setGeneratedReport(report)
    setActiveTab("preview")
  }

  const handleDownload = (format: ExportFormat) => {
    toast.success(`Downloading SF2 report in ${format} format`, {
      description: "The download will start shortly"
    })
  }

  const handlePrint = () => {
    window.print()
    toast.success("Print dialog opened", {
      description: "Please select your printer and print settings"
    })
  }

  const handleBackToGenerate = () => {
    setActiveTab("generate")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            SF2 Daily Attendance Report Generator
          </DialogTitle>
          <DialogDescription>
            Generate official DepEd School Form 2 - Daily Attendance Report of Learners
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Generate Report
            </TabsTrigger>
            <TabsTrigger 
              value="preview" 
              disabled={!generatedReport}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              Preview Report
            </TabsTrigger>
          </TabsList>

          <TabsContent value="generate" className="mt-6">
            <SF2ReportGenerator
              config={config}
              onGenerate={handleGenerateReport}
              onPreview={handlePreviewReport}
            />
          </TabsContent>

          <TabsContent value="preview" className="mt-6">
            {generatedReport ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Button 
                    variant="outline" 
                    onClick={handleBackToGenerate}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Back to Generator
                  </Button>
                </div>
                
                <SF2ReportPreview
                  report={generatedReport}
                  onDownload={handleDownload}
                  onPrint={handlePrint}
                />
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No report generated yet</p>
                <p className="text-sm">Generate a report first to see the preview</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
