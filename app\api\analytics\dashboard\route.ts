import { NextRequest, NextResponse } from "next/server"
import { 
  mockAnalyticsKPIs, 
  mockAIInsights, 
  mockHeatmapData, 
  mockTrendAnalysis,
  mockGradeComparisons,
  getHeatmapDataByDateRange 
} from "@/lib/data/analytics-mock-data"
import { AnalyticsFilter } from "@/lib/types/analytics"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'
    const grades = searchParams.get('grades')?.split(',') || []
    const sections = searchParams.get('sections')?.split(',') || []
    
    // Parse date range
    const endDate = new Date().toISOString().split('T')[0]
    const startDate = new Date()
    
    switch (timeRange) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(startDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(startDate.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1)
        break
      default:
        startDate.setDate(startDate.getDate() - 30)
    }
    
    const startDateStr = startDate.toISOString().split('T')[0]
    
    // Filter data based on parameters
    let filteredKPIs = mockAnalyticsKPIs
    let filteredInsights = mockAIInsights
    let filteredHeatmapData = getHeatmapDataByDateRange(startDateStr, endDate)
    let filteredGradeComparisons = mockGradeComparisons
    
    // Apply grade filter
    if (grades.length > 0) {
      filteredInsights = filteredInsights.filter(insight => 
        !insight.affectedGrades || insight.affectedGrades.some(grade => grades.includes(grade))
      )
      filteredGradeComparisons = filteredGradeComparisons.filter(comparison => 
        grades.includes(comparison.grade)
      )
    }
    
    // Calculate summary statistics
    const totalStudents = filteredGradeComparisons.reduce((sum, grade) => sum + grade.metrics.totalStudents, 0)
    const totalPresent = filteredGradeComparisons.reduce((sum, grade) => sum + grade.metrics.presentCount, 0)
    const totalLate = filteredGradeComparisons.reduce((sum, grade) => sum + grade.metrics.lateCount, 0)
    const totalAbsent = filteredGradeComparisons.reduce((sum, grade) => sum + grade.metrics.absentCount, 0)
    
    const overallAttendanceRate = totalStudents > 0 ? (totalPresent + totalLate) / totalStudents : 0
    const punctualityRate = totalStudents > 0 ? totalPresent / totalStudents : 0
    
    // Risk distribution
    const riskDistribution = filteredGradeComparisons.reduce((acc, grade) => {
      acc.low += grade.riskDistribution.low
      acc.medium += grade.riskDistribution.medium
      acc.high += grade.riskDistribution.high
      acc.critical += grade.riskDistribution.critical
      return acc
    }, { low: 0, medium: 0, high: 0, critical: 0 })
    
    // Recent alerts (high severity insights)
    const recentAlerts = filteredInsights
      .filter(insight => insight.severity === 'warning' || insight.severity === 'critical')
      .slice(0, 5)
    
    // Attendance trends for charts
    const attendanceTrends = {
      daily: mockTrendAnalysis.data.slice(-parseInt(timeRange.replace('d', '')) || -30),
      forecast: mockTrendAnalysis.forecast
    }
    
    // Peak attendance hours
    const hourlyData = filteredHeatmapData.reduce((acc, data) => {
      if (!acc[data.hour]) acc[data.hour] = { total: 0, count: 0 }
      acc[data.hour].total += data.value
      acc[data.hour].count += 1
      return acc
    }, {} as Record<number, { total: number; count: number }>)
    
    const peakHours = Object.entries(hourlyData)
      .map(([hour, data]) => ({
        hour: parseInt(hour),
        average: data.total / data.count
      }))
      .sort((a, b) => b.average - a.average)
      .slice(0, 3)
      .map(item => `${item.hour}:00`)
    
    const dashboardData = {
      summary: {
        totalStudents,
        overallAttendanceRate: Math.round(overallAttendanceRate * 1000) / 10, // Round to 1 decimal
        punctualityRate: Math.round(punctualityRate * 1000) / 10,
        atRiskStudents: riskDistribution.high + riskDistribution.critical,
        peakHours
      },
      kpis: filteredKPIs,
      insights: filteredInsights,
      alerts: recentAlerts,
      trends: attendanceTrends,
      riskDistribution,
      gradeComparisons: filteredGradeComparisons,
      heatmapData: filteredHeatmapData.slice(0, 1000), // Limit for performance
      lastUpdated: new Date().toISOString()
    }
    
    return NextResponse.json({
      success: true,
      data: dashboardData,
      message: "Dashboard data retrieved successfully"
    })
    
  } catch (error) {
    console.error("Dashboard data retrieval error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to retrieve dashboard data"
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { filters, customKPIs, widgetConfig } = body
    
    // Handle custom dashboard configuration
    // In a real implementation, this would save user preferences
    
    return NextResponse.json({
      success: true,
      message: "Dashboard configuration updated successfully"
    })
    
  } catch (error) {
    console.error("Dashboard configuration error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to update dashboard configuration"
    }, { status: 500 })
  }
}
