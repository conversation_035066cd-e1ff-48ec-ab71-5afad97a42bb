import { 
  GeneratedReport, 
  ReportTemplate, 
  ReportSchedule, 
  SchoolInfo, 
  SF2Report, 
  SF4Report,
  ReportAnalytics,
  TeacherInfo
} from '@/lib/types/reports'

// School information for Tanauan School of Arts and Trade
export const schoolInfo: SchoolInfo = {
  schoolId: "301216",
  schoolName: "Tanauan School of Arts and Trade",
  address: "Tanauan City, Batangas, Philippines",
  district: "Tanauan City District",
  division: "Schools Division of Batangas",
  region: "Region IV-A (CALABARZON)",
  principalName: "Dr. <PERSON>",
  schoolYear: "2024-2025",
  semester: "1st Semester",
  quarter: "2nd Quarter"
}

// Mock teachers data
export const mockTeachers: TeacherInfo[] = [
  {
    id: "TCH001",
    name: "Prof. <PERSON>",
    position: "Subject Teacher - Mathematics",
  },
  {
    id: "TCH002", 
    name: "Mrs. <PERSON>",
    position: "Subject Teacher - English",
  },
  {
    id: "TCH003",
    name: "Mr. <PERSON>",
    position: "Subject Teacher - Science",
  },
  {
    id: "TCH004",
    name: "Ms. Princess <PERSON>",
    position: "Subject Teacher - Filipino",
  }
]

// Mock generated reports
export const mockGeneratedReports: GeneratedReport[] = [
  {
    id: "RPT001",
    config: {
      id: "CFG001",
      name: "Daily Attendance Report - Grade 7A",
      type: "SF2",
      description: "SF2 Daily Attendance Report for Grade 7-A",
      dateRange: {
        startDate: "2025-01-02",
        endDate: "2025-01-02"
      },
      filters: {
        grades: ["7"],
        sections: ["Grade 7-A"]
      },
      settings: {
        includePhotos: false,
        includeSignatures: true,
        pageOrientation: "portrait",
        fontSize: "medium"
      },
      createdBy: "admin",
      createdAt: "2025-01-02T08:00:00Z",
      lastModified: "2025-01-02T08:00:00Z"
    },
    status: "READY",
    generatedAt: "2025-01-02T08:15:00Z",
    fileSize: 245760,
    downloadCount: 12,
    metadata: {
      totalStudents: 35,
      totalRecords: 35,
      dateRange: {
        startDate: "2025-01-02",
        endDate: "2025-01-02"
      },
      statistics: {
        presentCount: 32,
        lateCount: 2,
        absentCount: 1,
        attendanceRate: 94.3
      },
      schoolInfo
    }
  },
  {
    id: "RPT002",
    config: {
      id: "CFG002",
      name: "Monthly Learner's Movement - December 2024",
      type: "SF4",
      description: "SF4 Monthly Learner's Movement Report for December 2024",
      dateRange: {
        startDate: "2024-12-01",
        endDate: "2024-12-31"
      },
      filters: {
        grades: ["7", "8", "9", "10", "11", "12"]
      },
      settings: {
        includeSignatures: true,
        pageOrientation: "landscape",
        fontSize: "small"
      },
      createdBy: "principal",
      createdAt: "2025-01-01T09:00:00Z",
      lastModified: "2025-01-01T09:00:00Z"
    },
    status: "READY",
    generatedAt: "2025-01-01T09:30:00Z",
    fileSize: 512000,
    downloadCount: 8,
    metadata: {
      totalStudents: 1234,
      totalRecords: 24680,
      dateRange: {
        startDate: "2024-12-01",
        endDate: "2024-12-31"
      },
      statistics: {
        presentCount: 22145,
        lateCount: 1235,
        absentCount: 1300,
        attendanceRate: 89.7
      },
      schoolInfo
    }
  },
  {
    id: "RPT003",
    config: {
      id: "CFG003",
      name: "Weekly Attendance Summary",
      type: "WEEKLY",
      description: "Weekly attendance summary for all grades",
      dateRange: {
        startDate: "2024-12-30",
        endDate: "2025-01-03"
      },
      filters: {},
      settings: {
        showStatistics: true,
        groupBy: "grade",
        pageOrientation: "landscape"
      },
      createdBy: "admin",
      createdAt: "2025-01-03T16:00:00Z",
      lastModified: "2025-01-03T16:00:00Z"
    },
    status: "GENERATING",
    generatedAt: "2025-01-03T16:15:00Z",
    downloadCount: 0,
    metadata: {
      totalStudents: 1234,
      totalRecords: 6170,
      dateRange: {
        startDate: "2024-12-30",
        endDate: "2025-01-03"
      },
      statistics: {
        presentCount: 5540,
        lateCount: 310,
        absentCount: 320,
        attendanceRate: 89.8
      },
      schoolInfo
    }
  }
]

// Mock report templates
export const mockReportTemplates: ReportTemplate[] = [
  {
    id: "TPL001",
    name: "SF2 Daily Attendance",
    type: "SF2",
    description: "Standard SF2 Daily Attendance Report template",
    config: {
      type: "SF2",
      settings: {
        includeSignatures: true,
        pageOrientation: "portrait",
        fontSize: "medium",
        includeHeader: true,
        includeFooter: true
      }
    },
    isDefault: true,
    isPublic: true,
    createdBy: "system",
    createdAt: "2024-01-01T00:00:00Z"
  },
  {
    id: "TPL002",
    name: "SF4 Monthly Movement",
    type: "SF4",
    description: "Standard SF4 Monthly Learner's Movement Report template",
    config: {
      type: "SF4",
      settings: {
        includeSignatures: true,
        pageOrientation: "landscape",
        fontSize: "small",
        includeHeader: true,
        includeFooter: true
      }
    },
    isDefault: true,
    isPublic: true,
    createdBy: "system",
    createdAt: "2024-01-01T00:00:00Z"
  },
  {
    id: "TPL003",
    name: "Chronic Absenteeism Report",
    type: "CUSTOM",
    description: "Custom report for identifying students with chronic absenteeism",
    config: {
      type: "CUSTOM",
      filters: {
        attendanceStatus: ["Absent"]
      },
      settings: {
        showStatistics: true,
        groupBy: "grade",
        sortBy: "attendance"
      }
    },
    isDefault: false,
    isPublic: true,
    createdBy: "admin",
    createdAt: "2024-06-15T10:00:00Z"
  }
]

// Mock scheduled reports
export const mockScheduledReports: ReportSchedule[] = [
  {
    id: "SCH001",
    reportConfigId: "CFG001",
    name: "Daily SF2 Reports - All Grades",
    frequency: "daily",
    time: "07:00",
    timezone: "Asia/Manila",
    isActive: true,
    nextRun: "2025-01-03T07:00:00Z",
    recipients: ["<EMAIL>", "<EMAIL>"],
    createdBy: "admin",
    createdAt: "2024-08-01T00:00:00Z"
  },
  {
    id: "SCH002",
    reportConfigId: "CFG002",
    name: "Monthly SF4 Reports",
    frequency: "monthly",
    dayOfMonth: 1,
    time: "09:00",
    timezone: "Asia/Manila",
    isActive: true,
    lastRun: "2025-01-01T09:00:00Z",
    nextRun: "2025-02-01T09:00:00Z",
    recipients: ["<EMAIL>", "<EMAIL>"],
    createdBy: "principal",
    createdAt: "2024-08-01T00:00:00Z"
  }
]

// Mock report analytics
export const mockReportAnalytics: ReportAnalytics[] = [
  {
    reportId: "RPT001",
    views: 45,
    downloads: 12,
    exports: {
      PDF: 8,
      EXCEL: 3,
      CSV: 1,
      PRINT: 0
    },
    lastAccessed: "2025-01-02T14:30:00Z",
    popularFilters: {
      "grades": 15,
      "sections": 12,
      "dateRange": 45
    },
    averageGenerationTime: 15.5
  },
  {
    reportId: "RPT002",
    views: 23,
    downloads: 8,
    exports: {
      PDF: 6,
      EXCEL: 2,
      CSV: 0,
      PRINT: 0
    },
    lastAccessed: "2025-01-01T16:45:00Z",
    popularFilters: {
      "grades": 8,
      "dateRange": 23
    },
    averageGenerationTime: 45.2
  }
]

// Helper functions
export function getReportById(id: string): GeneratedReport | undefined {
  return mockGeneratedReports.find(report => report.id === id)
}

export function getTemplateById(id: string): ReportTemplate | undefined {
  return mockReportTemplates.find(template => template.id === id)
}

export function getScheduleById(id: string): ReportSchedule | undefined {
  return mockScheduledReports.find(schedule => schedule.id === id)
}

export function getReportsByType(type: string): GeneratedReport[] {
  return mockGeneratedReports.filter(report => report.config.type === type)
}

export function getReportsByStatus(status: string): GeneratedReport[] {
  return mockGeneratedReports.filter(report => report.status === status)
}

export function getTeacherById(id: string): TeacherInfo | undefined {
  return mockTeachers.find(teacher => teacher.id === id)
}
