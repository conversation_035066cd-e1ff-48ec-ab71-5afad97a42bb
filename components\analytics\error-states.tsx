"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  AlertTriangle, 
  RefreshCw, 
  Wifi, 
  Database, 
  Clock, 
  Shield,
  Bug,
  FileX,
  TrendingDown,
  Brain,
  Settings,
  HelpCircle
} from "lucide-react"

interface ErrorStateProps {
  title?: string
  description?: string
  action?: () => void
  actionLabel?: string
  showRetry?: boolean
}

// Generic Error State
export function ErrorState({ 
  title = "Something went wrong", 
  description = "An unexpected error occurred. Please try again.", 
  action, 
  actionLabel = "Try Again",
  showRetry = true 
}: ErrorStateProps) {
  return (
    <Card>
      <CardContent className="flex items-center justify-center py-12">
        <div className="text-center space-y-4 max-w-sm">
          <AlertTriangle className="h-16 w-16 mx-auto text-red-500" />
          <div>
            <h3 className="text-lg font-semibold text-red-900">{title}</h3>
            <p className="text-sm text-red-700 mt-2">{description}</p>
          </div>
          {showRetry && (
            <Button onClick={action} variant="outline" className="mt-4">
              <RefreshCw className="h-4 w-4 mr-2" />
              {actionLabel}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Network Error State
export function NetworkErrorState({ onRetry }: { onRetry?: () => void }) {
  return (
    <Card className="border-red-200">
      <CardContent className="flex items-center justify-center py-12">
        <div className="text-center space-y-4 max-w-sm">
          <div className="relative">
            <Wifi className="h-16 w-16 mx-auto text-red-500" />
            <div className="absolute -bottom-1 -right-1">
              <div className="h-6 w-6 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">!</span>
              </div>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-red-900">Connection Error</h3>
            <p className="text-sm text-red-700 mt-2">
              Unable to connect to the server. Please check your internet connection and try again.
            </p>
          </div>
          <div className="space-y-2">
            <Button onClick={onRetry} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Connection
            </Button>
            <p className="text-xs text-muted-foreground">
              If the problem persists, contact your system administrator
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Data Loading Error
export function DataLoadingError({ 
  dataType = "analytics data", 
  onRetry, 
  errorCode 
}: { 
  dataType?: string; 
  onRetry?: () => void; 
  errorCode?: string; 
}) {
  return (
    <Alert className="border-red-200 bg-red-50">
      <Database className="h-4 w-4 text-red-600" />
      <AlertTitle className="text-red-800">Failed to Load {dataType}</AlertTitle>
      <AlertDescription className="text-red-700">
        <div className="space-y-2">
          <p>There was an error loading the {dataType}. This might be due to:</p>
          <ul className="list-disc list-inside text-sm space-y-1">
            <li>Temporary server issues</li>
            <li>Network connectivity problems</li>
            <li>Data processing delays</li>
          </ul>
          {errorCode && (
            <Badge variant="outline" className="text-red-700 border-red-300">
              Error Code: {errorCode}
            </Badge>
          )}
          <div className="flex gap-2 mt-3">
            <Button size="sm" variant="outline" onClick={onRetry}>
              <RefreshCw className="h-3 w-3 mr-1" />
              Retry
            </Button>
            <Button size="sm" variant="ghost">
              <HelpCircle className="h-3 w-3 mr-1" />
              Get Help
            </Button>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  )
}

// Chart Error State
export function ChartErrorState({ 
  chartType = "chart", 
  onRetry 
}: { 
  chartType?: string; 
  onRetry?: () => void; 
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-800">
          <TrendingDown className="h-5 w-5" />
          Chart Error
        </CardTitle>
        <CardDescription className="text-red-600">
          Unable to render {chartType}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8">
          <FileX className="h-12 w-12 mx-auto mb-4 text-red-400" />
          <p className="text-sm text-red-700 mb-4">
            The {chartType} could not be displayed due to a rendering error.
          </p>
          <Button size="sm" variant="outline" onClick={onRetry}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Reload Chart
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

// AI Processing Error
export function AIProcessingError({ 
  onRetry, 
  onFallback 
}: { 
  onRetry?: () => void; 
  onFallback?: () => void; 
}) {
  return (
    <Card className="border-orange-200 bg-orange-50">
      <CardContent className="py-8">
        <div className="text-center space-y-4">
          <div className="relative">
            <Brain className="h-16 w-16 mx-auto text-orange-500" />
            <div className="absolute -top-1 -right-1">
              <AlertTriangle className="h-6 w-6 text-red-500" />
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-orange-900">AI Processing Error</h3>
            <p className="text-sm text-orange-700 mt-2">
              The AI analysis could not be completed. This might be due to high server load or model unavailability.
            </p>
          </div>
          <div className="flex gap-2 justify-center">
            <Button size="sm" variant="outline" onClick={onRetry}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Analysis
            </Button>
            {onFallback && (
              <Button size="sm" variant="ghost" onClick={onFallback}>
                View Basic Analytics
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Permission Error
export function PermissionError({ 
  resource = "this data", 
  onRequestAccess 
}: { 
  resource?: string; 
  onRequestAccess?: () => void; 
}) {
  return (
    <Card className="border-yellow-200 bg-yellow-50">
      <CardContent className="flex items-center justify-center py-12">
        <div className="text-center space-y-4 max-w-sm">
          <Shield className="h-16 w-16 mx-auto text-yellow-600" />
          <div>
            <h3 className="text-lg font-semibold text-yellow-900">Access Restricted</h3>
            <p className="text-sm text-yellow-700 mt-2">
              You don't have permission to view {resource}. Contact your administrator to request access.
            </p>
          </div>
          {onRequestAccess && (
            <Button size="sm" variant="outline" onClick={onRequestAccess}>
              Request Access
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Timeout Error
export function TimeoutError({ 
  operation = "operation", 
  onRetry 
}: { 
  operation?: string; 
  onRetry?: () => void; 
}) {
  return (
    <Alert className="border-orange-200 bg-orange-50">
      <Clock className="h-4 w-4 text-orange-600" />
      <AlertTitle className="text-orange-800">Request Timeout</AlertTitle>
      <AlertDescription className="text-orange-700">
        <div className="space-y-2">
          <p>The {operation} took too long to complete and was cancelled.</p>
          <p className="text-sm">This might be due to:</p>
          <ul className="list-disc list-inside text-sm space-y-1">
            <li>Large amount of data being processed</li>
            <li>Server overload</li>
            <li>Network latency issues</li>
          </ul>
          <Button size="sm" variant="outline" onClick={onRetry} className="mt-3">
            <RefreshCw className="h-3 w-3 mr-1" />
            Try Again
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  )
}

// Export Error
export function ExportError({ 
  format = "PDF", 
  onRetry, 
  onChangeFormat 
}: { 
  format?: string; 
  onRetry?: () => void; 
  onChangeFormat?: () => void; 
}) {
  return (
    <Card className="border-red-200 bg-red-50">
      <CardContent className="py-8">
        <div className="text-center space-y-4">
          <FileX className="h-16 w-16 mx-auto text-red-500" />
          <div>
            <h3 className="text-lg font-semibold text-red-900">Export Failed</h3>
            <p className="text-sm text-red-700 mt-2">
              Unable to generate the {format} export. The file might be too large or the server is busy.
            </p>
          </div>
          <div className="flex gap-2 justify-center">
            <Button size="sm" variant="outline" onClick={onRetry}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Export
            </Button>
            {onChangeFormat && (
              <Button size="sm" variant="ghost" onClick={onChangeFormat}>
                Try Different Format
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Configuration Error
export function ConfigurationError({ 
  onReconfigure, 
  onUseDefaults 
}: { 
  onReconfigure?: () => void; 
  onUseDefaults?: () => void; 
}) {
  return (
    <Alert className="border-blue-200 bg-blue-50">
      <Settings className="h-4 w-4 text-blue-600" />
      <AlertTitle className="text-blue-800">Configuration Issue</AlertTitle>
      <AlertDescription className="text-blue-700">
        <div className="space-y-2">
          <p>There's an issue with the current configuration that prevents the analytics from loading properly.</p>
          <div className="flex gap-2 mt-3">
            <Button size="sm" variant="outline" onClick={onReconfigure}>
              <Settings className="h-3 w-3 mr-1" />
              Reconfigure
            </Button>
            <Button size="sm" variant="ghost" onClick={onUseDefaults}>
              Use Defaults
            </Button>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  )
}

// No Data State
export function NoDataState({ 
  title = "No Data Available", 
  description = "There's no data to display for the selected criteria.", 
  onAdjustFilters, 
  onRefresh 
}: { 
  title?: string; 
  description?: string; 
  onAdjustFilters?: () => void; 
  onRefresh?: () => void; 
}) {
  return (
    <Card>
      <CardContent className="flex items-center justify-center py-12">
        <div className="text-center space-y-4 max-w-sm">
          <Database className="h-16 w-16 mx-auto text-muted-foreground" />
          <div>
            <h3 className="text-lg font-semibold text-muted-foreground">{title}</h3>
            <p className="text-sm text-muted-foreground mt-2">{description}</p>
          </div>
          <div className="flex gap-2 justify-center">
            {onAdjustFilters && (
              <Button size="sm" variant="outline" onClick={onAdjustFilters}>
                Adjust Filters
              </Button>
            )}
            {onRefresh && (
              <Button size="sm" variant="ghost" onClick={onRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Maintenance Mode
export function MaintenanceMode({ 
  estimatedTime = "30 minutes", 
  onCheckStatus 
}: { 
  estimatedTime?: string; 
  onCheckStatus?: () => void; 
}) {
  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardContent className="flex items-center justify-center py-12">
        <div className="text-center space-y-4 max-w-sm">
          <Settings className="h-16 w-16 mx-auto text-blue-600 animate-spin" />
          <div>
            <h3 className="text-lg font-semibold text-blue-900">System Maintenance</h3>
            <p className="text-sm text-blue-700 mt-2">
              The analytics system is currently undergoing maintenance. 
              Estimated completion time: {estimatedTime}.
            </p>
          </div>
          <Button size="sm" variant="outline" onClick={onCheckStatus}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Check Status
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
