"use client"

import React, { useState } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ReportTypeSelector } from "./report-type-selector"
import { ReportFilters } from "./report-filters"
import { ReportType, ReportFilters as ReportFiltersType, ReportConfig } from "@/lib/types/reports"
import { 
  ChevronLeft, 
  ChevronRight, 
  FileText, 
  Filter, 
  Settings, 
  Eye, 
  Download,
  CheckCircle,
  Wand2
} from "lucide-react"
import { toast } from "sonner"

interface ReportWizardProps {
  onComplete: (config: ReportConfig) => void
  onCancel: () => void
  className?: string
}

type WizardStep = "type" | "filters" | "settings" | "preview"

const wizardSteps = [
  { id: "type", title: "Report Type", description: "Choose the type of report to generate", icon: FileText },
  { id: "filters", title: "Filters", description: "Set date ranges and data filters", icon: Filter },
  { id: "settings", title: "Settings", description: "Configure report options", icon: Settings },
  { id: "preview", title: "Preview", description: "Review and generate report", icon: Eye }
]

export function ReportWizard({ onComplete, onCancel, className }: ReportWizardProps) {
  const [currentStep, setCurrentStep] = useState<WizardStep>("type")
  const [selectedReportType, setSelectedReportType] = useState<ReportType>("SF2")
  const [reportFilters, setReportFilters] = useState<ReportFiltersType>({})
  const [reportSettings, setReportSettings] = useState({
    includeSignatures: true,
    includePhotos: false,
    includeRemarks: true,
    showStatistics: true,
    pageOrientation: "portrait" as "portrait" | "landscape",
    fontSize: "medium" as "small" | "medium" | "large",
    includeHeader: true,
    includeFooter: true,
    watermark: ""
  })
  const [reportName, setReportName] = useState("")
  const [reportDescription, setReportDescription] = useState("")

  const getCurrentStepIndex = () => {
    return wizardSteps.findIndex(step => step.id === currentStep)
  }

  const getProgress = () => {
    return ((getCurrentStepIndex() + 1) / wizardSteps.length) * 100
  }

  const canProceed = () => {
    switch (currentStep) {
      case "type":
        return selectedReportType !== undefined
      case "filters":
        return true // Filters are optional
      case "settings":
        return reportName.trim() !== ""
      case "preview":
        return true
      default:
        return false
    }
  }

  const handleNext = () => {
    const currentIndex = getCurrentStepIndex()
    if (currentIndex < wizardSteps.length - 1) {
      setCurrentStep(wizardSteps[currentIndex + 1].id as WizardStep)
    }
  }

  const handlePrevious = () => {
    const currentIndex = getCurrentStepIndex()
    if (currentIndex > 0) {
      setCurrentStep(wizardSteps[currentIndex - 1].id as WizardStep)
    }
  }

  const handleComplete = () => {
    const config: ReportConfig = {
      id: `wizard_${Date.now()}`,
      name: reportName,
      type: selectedReportType,
      description: reportDescription,
      dateRange: {
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0]
      },
      filters: reportFilters,
      settings: reportSettings,
      createdBy: "current-user",
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    }

    onComplete(config)
    toast.success("Report configuration completed", {
      description: "Your report is being generated"
    })
  }

  const generateDefaultName = () => {
    const today = new Date().toLocaleDateString()
    const typeNames = {
      SF2: "SF2 Daily Attendance",
      SF4: "SF4 Monthly Movement", 
      DAILY: "Daily Summary",
      WEEKLY: "Weekly Summary",
      MONTHLY: "Monthly Summary",
      ANNUAL: "Annual Summary",
      CUSTOM: "Custom Report"
    }
    return `${typeNames[selectedReportType]} - ${today}`
  }

  const generateDefaultDescription = () => {
    const descriptions = {
      SF2: "Official DepEd SF2 Daily Attendance Report of Learners",
      SF4: "Official DepEd SF4 Monthly Report on Learner's Movement",
      DAILY: "Daily attendance summary for all students",
      WEEKLY: "Weekly attendance trends and statistics",
      MONTHLY: "Monthly attendance overview and analysis",
      ANNUAL: "Annual attendance and performance report",
      CUSTOM: "Custom report with selected data and filters"
    }
    return descriptions[selectedReportType]
  }

  // Auto-generate name and description when report type changes
  React.useEffect(() => {
    if (!reportName) {
      setReportName(generateDefaultName())
    }
    if (!reportDescription) {
      setReportDescription(generateDefaultDescription())
    }
  }, [selectedReportType])

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Wizard Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Wand2 className="h-6 w-6 text-primary" />
              </div>
              <div>
                <CardTitle>Report Generation Wizard</CardTitle>
                <CardDescription>
                  Step-by-step guide to create your report
                </CardDescription>
              </div>
            </div>
            <Button variant="ghost" onClick={onCancel}>
              Cancel
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Progress Bar */}
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <span>Step {getCurrentStepIndex() + 1} of {wizardSteps.length}</span>
              <span>{Math.round(getProgress())}% Complete</span>
            </div>
            <Progress value={getProgress()} className="w-full" />
          </div>

          {/* Step Indicators */}
          <div className="flex items-center justify-between mt-6">
            {wizardSteps.map((step, index) => {
              const isActive = step.id === currentStep
              const isCompleted = index < getCurrentStepIndex()
              const Icon = step.icon

              return (
                <div key={step.id} className="flex flex-col items-center space-y-2">
                  <div className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                    ${isActive ? 'border-primary bg-primary text-primary-foreground' : 
                      isCompleted ? 'border-green-500 bg-green-500 text-white' : 
                      'border-muted-foreground/30 bg-background'}
                  `}>
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <div className="text-center">
                    <div className={`text-sm font-medium ${isActive ? 'text-primary' : isCompleted ? 'text-green-600' : 'text-muted-foreground'}`}>
                      {step.title}
                    </div>
                    <div className="text-xs text-muted-foreground hidden sm:block">
                      {step.description}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      <Card>
        <CardContent className="pt-6">
          {currentStep === "type" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">Choose Report Type</h3>
                <p className="text-muted-foreground mb-6">
                  Select the type of report you want to generate. Each type has specific features and formatting.
                </p>
              </div>
              <ReportTypeSelector
                selectedType={selectedReportType}
                onTypeSelect={setSelectedReportType}
              />
            </div>
          )}

          {currentStep === "filters" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">Configure Filters</h3>
                <p className="text-muted-foreground mb-6">
                  Set date ranges and apply filters to customize your report data.
                </p>
              </div>
              <ReportFilters
                filters={reportFilters}
                onFiltersChange={setReportFilters}
                reportType={selectedReportType}
              />
            </div>
          )}

          {currentStep === "settings" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">Report Settings</h3>
                <p className="text-muted-foreground mb-6">
                  Configure report options and provide a name and description.
                </p>
              </div>
              
              {/* Report Name and Description */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Report Name</label>
                  <input
                    type="text"
                    value={reportName}
                    onChange={(e) => setReportName(e.target.value)}
                    className="w-full px-3 py-2 border border-input rounded-md"
                    placeholder="Enter report name"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Page Orientation</label>
                  <select
                    value={reportSettings.pageOrientation}
                    onChange={(e) => setReportSettings({
                      ...reportSettings,
                      pageOrientation: e.target.value as "portrait" | "landscape"
                    })}
                    className="w-full px-3 py-2 border border-input rounded-md"
                  >
                    <option value="portrait">Portrait</option>
                    <option value="landscape">Landscape</option>
                  </select>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Description</label>
                <textarea
                  value={reportDescription}
                  onChange={(e) => setReportDescription(e.target.value)}
                  className="w-full px-3 py-2 border border-input rounded-md"
                  rows={3}
                  placeholder="Enter report description"
                />
              </div>

              {/* Report Options */}
              <div className="space-y-4">
                <h4 className="font-medium">Report Options</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={reportSettings.includeSignatures}
                        onChange={(e) => setReportSettings({
                          ...reportSettings,
                          includeSignatures: e.target.checked
                        })}
                        className="rounded"
                      />
                      <span className="text-sm">Include signature fields</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={reportSettings.includeRemarks}
                        onChange={(e) => setReportSettings({
                          ...reportSettings,
                          includeRemarks: e.target.checked
                        })}
                        className="rounded"
                      />
                      <span className="text-sm">Include remarks column</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={reportSettings.showStatistics}
                        onChange={(e) => setReportSettings({
                          ...reportSettings,
                          showStatistics: e.target.checked
                        })}
                        className="rounded"
                      />
                      <span className="text-sm">Show statistics summary</span>
                    </label>
                  </div>
                  
                  <div className="space-y-3">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={reportSettings.includeHeader}
                        onChange={(e) => setReportSettings({
                          ...reportSettings,
                          includeHeader: e.target.checked
                        })}
                        className="rounded"
                      />
                      <span className="text-sm">Include header</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={reportSettings.includeFooter}
                        onChange={(e) => setReportSettings({
                          ...reportSettings,
                          includeFooter: e.target.checked
                        })}
                        className="rounded"
                      />
                      <span className="text-sm">Include footer</span>
                    </label>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Font Size</label>
                      <select
                        value={reportSettings.fontSize}
                        onChange={(e) => setReportSettings({
                          ...reportSettings,
                          fontSize: e.target.value as "small" | "medium" | "large"
                        })}
                        className="w-full px-3 py-2 border border-input rounded-md"
                      >
                        <option value="small">Small</option>
                        <option value="medium">Medium</option>
                        <option value="large">Large</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentStep === "preview" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">Review Configuration</h3>
                <p className="text-muted-foreground mb-6">
                  Review your report configuration before generating.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Report Type</h4>
                    <Badge variant="outline" className="mt-1">{selectedReportType}</Badge>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Report Name</h4>
                    <p className="text-sm">{reportName}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Description</h4>
                    <p className="text-sm">{reportDescription}</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Filters Applied</h4>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {Object.keys(reportFilters).length === 0 ? (
                        <Badge variant="secondary">No filters</Badge>
                      ) : (
                        Object.entries(reportFilters).map(([key, value]) => (
                          <Badge key={key} variant="secondary" className="text-xs">
                            {key}: {Array.isArray(value) ? value.length : 1}
                          </Badge>
                        ))
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Settings</h4>
                    <div className="text-sm space-y-1">
                      <p>Orientation: {reportSettings.pageOrientation}</p>
                      <p>Font Size: {reportSettings.fontSize}</p>
                      <p>Signatures: {reportSettings.includeSignatures ? 'Yes' : 'No'}</p>
                      <p>Statistics: {reportSettings.showStatistics ? 'Yes' : 'No'}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={getCurrentStepIndex() === 0}
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>

        <div className="flex items-center gap-2">
          {getCurrentStepIndex() === wizardSteps.length - 1 ? (
            <Button onClick={handleComplete} disabled={!canProceed()}>
              <Download className="mr-2 h-4 w-4" />
              Generate Report
            </Button>
          ) : (
            <Button onClick={handleNext} disabled={!canProceed()}>
              Next
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
