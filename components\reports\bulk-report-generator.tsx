"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ReportType, ReportConfig } from "@/lib/types/reports"
import { DateRangePicker, DateRange } from "@/components/ui/date-range-picker"
import { 
  Layers, 
  Play, 
  Pause, 
  CheckCircle, 
  XCircle, 
  Clock,
  Download,
  FileText,
  Calendar
} from "lucide-react"
import { format } from "date-fns"
import { toast } from "sonner"

interface BulkReportGeneratorProps {
  onGenerate: (configs: ReportConfig[]) => void
  className?: string
}

interface BulkReportJob {
  id: string
  name: string
  type: ReportType
  dateRange: DateRange
  filters: {
    grades?: string[]
    sections?: string[]
  }
  status: 'pending' | 'generating' | 'completed' | 'failed'
  progress: number
  error?: string
  generatedAt?: string
}

const gradeOptions = ["7", "8", "9", "10", "11", "12"]
const sectionOptions = ["A", "B", "C", "D", "E"]

export function BulkReportGenerator({ onGenerate, className }: BulkReportGeneratorProps) {
  const [reportType, setReportType] = useState<ReportType>("SF2")
  const [dateRange, setDateRange] = useState<DateRange>({
    from: new Date(),
    to: new Date()
  })
  const [selectedGrades, setSelectedGrades] = useState<string[]>([])
  const [selectedSections, setSelectedSections] = useState<string[]>([])
  const [generateBy, setGenerateBy] = useState<"grade" | "section" | "both">("section")
  const [jobs, setJobs] = useState<BulkReportJob[]>([])
  const [isGenerating, setIsGenerating] = useState(false)

  const handleGradeChange = (grade: string, checked: boolean) => {
    if (checked) {
      setSelectedGrades([...selectedGrades, grade])
    } else {
      setSelectedGrades(selectedGrades.filter(g => g !== grade))
    }
  }

  const handleSectionChange = (section: string, checked: boolean) => {
    if (checked) {
      setSelectedSections([...selectedSections, section])
    } else {
      setSelectedSections(selectedSections.filter(s => s !== section))
    }
  }

  const generateJobList = (): BulkReportJob[] => {
    const jobs: BulkReportJob[] = []

    if (generateBy === "grade") {
      selectedGrades.forEach(grade => {
        jobs.push({
          id: `bulk_${Date.now()}_${grade}`,
          name: `${reportType} Report - Grade ${grade}`,
          type: reportType,
          dateRange,
          filters: { grades: [grade] },
          status: 'pending',
          progress: 0
        })
      })
    } else if (generateBy === "section") {
      selectedGrades.forEach(grade => {
        selectedSections.forEach(section => {
          jobs.push({
            id: `bulk_${Date.now()}_${grade}_${section}`,
            name: `${reportType} Report - Grade ${grade}-${section}`,
            type: reportType,
            dateRange,
            filters: { grades: [grade], sections: [section] },
            status: 'pending',
            progress: 0
          })
        })
      })
    } else { // both
      // Generate one report per grade
      selectedGrades.forEach(grade => {
        jobs.push({
          id: `bulk_${Date.now()}_grade_${grade}`,
          name: `${reportType} Report - Grade ${grade} (All Sections)`,
          type: reportType,
          dateRange,
          filters: { grades: [grade] },
          status: 'pending',
          progress: 0
        })
      })
      
      // Generate one report per section within each grade
      selectedGrades.forEach(grade => {
        selectedSections.forEach(section => {
          jobs.push({
            id: `bulk_${Date.now()}_section_${grade}_${section}`,
            name: `${reportType} Report - Grade ${grade}-${section}`,
            type: reportType,
            dateRange,
            filters: { grades: [grade], sections: [section] },
            status: 'pending',
            progress: 0
          })
        })
      })
    }

    return jobs
  }

  const handleStartBulkGeneration = async () => {
    if (!dateRange.from || !dateRange.to) {
      toast.error("Please select a date range")
      return
    }

    if (selectedGrades.length === 0) {
      toast.error("Please select at least one grade")
      return
    }

    if (generateBy !== "grade" && selectedSections.length === 0) {
      toast.error("Please select at least one section")
      return
    }

    const newJobs = generateJobList()
    setJobs(newJobs)
    setIsGenerating(true)

    // Simulate bulk generation process
    for (let i = 0; i < newJobs.length; i++) {
      const job = newJobs[i]
      
      // Update job status to generating
      setJobs(prevJobs => 
        prevJobs.map(j => 
          j.id === job.id 
            ? { ...j, status: 'generating' as const }
            : j
        )
      )

      // Simulate generation progress
      for (let progress = 0; progress <= 100; progress += 20) {
        await new Promise(resolve => setTimeout(resolve, 200))
        setJobs(prevJobs => 
          prevJobs.map(j => 
            j.id === job.id 
              ? { ...j, progress }
              : j
          )
        )
      }

      // Mark as completed (or failed randomly for demo)
      const isSuccess = Math.random() > 0.1 // 90% success rate
      setJobs(prevJobs => 
        prevJobs.map(j => 
          j.id === job.id 
            ? { 
                ...j, 
                status: isSuccess ? 'completed' as const : 'failed' as const,
                progress: 100,
                generatedAt: isSuccess ? new Date().toISOString() : undefined,
                error: isSuccess ? undefined : "Generation failed due to data issues"
              }
            : j
        )
      )
    }

    setIsGenerating(false)
    
    const successCount = newJobs.filter(j => Math.random() > 0.1).length
    toast.success(`Bulk generation completed`, {
      description: `${successCount} of ${newJobs.length} reports generated successfully`
    })
  }

  const handleClearJobs = () => {
    setJobs([])
  }

  const getStatusIcon = (status: BulkReportJob['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-500" />
      case 'generating':
        return <Play className="h-4 w-4 text-blue-500 animate-spin" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusBadge = (status: BulkReportJob['status']) => {
    const variants = {
      pending: "secondary",
      generating: "default", 
      completed: "default",
      failed: "destructive"
    } as const

    const colors = {
      pending: "text-gray-600",
      generating: "text-blue-600",
      completed: "text-green-600", 
      failed: "text-red-600"
    } as const

    return (
      <Badge variant={variants[status]} className={colors[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const completedJobs = jobs.filter(j => j.status === 'completed').length
  const totalJobs = jobs.length
  const overallProgress = totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Layers className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <CardTitle>Bulk Report Generator</CardTitle>
              <CardDescription>
                Generate multiple reports at once for different grades and sections
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Bulk Generation Settings</CardTitle>
          <CardDescription>
            Configure the reports you want to generate in bulk
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Report Type and Date Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Report Type</Label>
              <Select value={reportType} onValueChange={(value) => setReportType(value as ReportType)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SF2">SF2 Daily Attendance</SelectItem>
                  <SelectItem value="SF4">SF4 Monthly Movement</SelectItem>
                  <SelectItem value="DAILY">Daily Summary</SelectItem>
                  <SelectItem value="WEEKLY">Weekly Summary</SelectItem>
                  <SelectItem value="MONTHLY">Monthly Summary</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Generation Strategy</Label>
              <Select value={generateBy} onValueChange={(value) => setGenerateBy(value as typeof generateBy)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="grade">One report per grade</SelectItem>
                  <SelectItem value="section">One report per section</SelectItem>
                  <SelectItem value="both">Both grade and section reports</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Date Range */}
          <div className="space-y-2">
            <Label>Date Range</Label>
            <DateRangePicker
              value={dateRange}
              onChange={setDateRange}
            />
          </div>

          {/* Grade Selection */}
          <div className="space-y-3">
            <Label>Select Grades</Label>
            <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
              {gradeOptions.map((grade) => (
                <div key={grade} className="flex items-center space-x-2">
                  <Checkbox
                    id={`bulk-grade-${grade}`}
                    checked={selectedGrades.includes(grade)}
                    onCheckedChange={(checked) => handleGradeChange(grade, checked as boolean)}
                  />
                  <Label htmlFor={`bulk-grade-${grade}`} className="text-sm">
                    Grade {grade}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Section Selection */}
          {generateBy !== "grade" && (
            <div className="space-y-3">
              <Label>Select Sections</Label>
              <div className="grid grid-cols-5 gap-2">
                {sectionOptions.map((section) => (
                  <div key={section} className="flex items-center space-x-2">
                    <Checkbox
                      id={`bulk-section-${section}`}
                      checked={selectedSections.includes(section)}
                      onCheckedChange={(checked) => handleSectionChange(section, checked as boolean)}
                    />
                    <Label htmlFor={`bulk-section-${section}`} className="text-sm">
                      Section {section}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Preview */}
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Generation Preview</h4>
            <p className="text-sm text-muted-foreground">
              This will generate <strong>{generateJobList().length}</strong> reports
              {dateRange.from && dateRange.to && (
                <> for the period from <strong>{format(dateRange.from, "MMM dd, yyyy")}</strong> to <strong>{format(dateRange.to, "MMM dd, yyyy")}</strong></>
              )}
            </p>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <Button 
              onClick={handleStartBulkGeneration}
              disabled={isGenerating || selectedGrades.length === 0}
            >
              <Layers className="mr-2 h-4 w-4" />
              Start Bulk Generation
            </Button>
            {jobs.length > 0 && (
              <Button variant="outline" onClick={handleClearJobs} disabled={isGenerating}>
                Clear Jobs
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Generation Progress */}
      {jobs.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Generation Progress</CardTitle>
                <CardDescription>
                  {completedJobs} of {totalJobs} reports completed
                </CardDescription>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold">{Math.round(overallProgress)}%</div>
                <div className="text-sm text-muted-foreground">Overall Progress</div>
              </div>
            </div>
            <Progress value={overallProgress} className="w-full" />
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Report Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Generated</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {jobs.map((job) => (
                    <TableRow key={job.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(job.status)}
                          <div>
                            <div className="font-medium">{job.name}</div>
                            {job.error && (
                              <div className="text-xs text-red-600">{job.error}</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(job.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress value={job.progress} className="w-20" />
                          <span className="text-sm">{job.progress}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {job.generatedAt ? (
                          <div className="text-sm">
                            {format(new Date(job.generatedAt), "HH:mm:ss")}
                          </div>
                        ) : (
                          <span className="text-sm text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        {job.status === 'completed' && (
                          <Button variant="ghost" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
