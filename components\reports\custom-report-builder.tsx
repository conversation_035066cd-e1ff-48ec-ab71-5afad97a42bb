"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CustomReport, ReportQuery, ReportVisualization, QueryCondition } from "@/lib/types/reports"
import { 
  Users, 
  BarChart3, 
  Table as TableIcon, 
  PieChart, 
  TrendingUp,
  Filter,
  Plus,
  X,
  Eye,
  Save,
  Download,
  Settings
} from "lucide-react"

interface CustomReportBuilderProps {
  onGenerate: (report: CustomReport) => void
  onPreview: (report: CustomReport) => void
  onSave: (report: CustomReport) => void
  className?: string
}

// Available data sources
const dataSources = [
  { id: "students", name: "Students", description: "Student information and enrollment data" },
  { id: "attendance", name: "Attendance Records", description: "Daily attendance tracking data" },
  { id: "grades", name: "Academic Grades", description: "Student academic performance data" },
  { id: "subjects", name: "Subjects", description: "Subject and curriculum information" },
  { id: "teachers", name: "Teachers", description: "Teacher and staff information" },
  { id: "communications", name: "Guardian Communications", description: "Parent/guardian communication logs" }
]

// Available fields for each data source
const dataFields = {
  students: [
    { id: "id", name: "Student ID", type: "string" },
    { id: "name", name: "Full Name", type: "string" },
    { id: "grade", name: "Grade Level", type: "string" },
    { id: "section", name: "Section", type: "string" },
    { id: "course", name: "Course", type: "string" },
    { id: "status", name: "Status", type: "string" },
    { id: "gender", name: "Gender", type: "string" },
    { id: "enrollmentDate", name: "Enrollment Date", type: "date" }
  ],
  attendance: [
    { id: "date", name: "Date", type: "date" },
    { id: "studentId", name: "Student ID", type: "string" },
    { id: "status", name: "Attendance Status", type: "string" },
    { id: "checkIn", name: "Check In Time", type: "time" },
    { id: "checkOut", name: "Check Out Time", type: "time" },
    { id: "subject", name: "Subject", type: "string" },
    { id: "period", name: "Period", type: "string" }
  ],
  grades: [
    { id: "studentId", name: "Student ID", type: "string" },
    { id: "subject", name: "Subject", type: "string" },
    { id: "quarter", name: "Quarter", type: "string" },
    { id: "grade", name: "Grade", type: "number" },
    { id: "remarks", name: "Remarks", type: "string" }
  ],
  subjects: [
    { id: "id", name: "Subject ID", type: "string" },
    { id: "name", name: "Subject Name", type: "string" },
    { id: "code", name: "Subject Code", type: "string" },
    { id: "instructor", name: "Instructor", type: "string" }
  ],
  teachers: [
    { id: "id", name: "Teacher ID", type: "string" },
    { id: "name", name: "Full Name", type: "string" },
    { id: "position", name: "Position", type: "string" },
    { id: "department", name: "Department", type: "string" }
  ],
  communications: [
    { id: "studentId", name: "Student ID", type: "string" },
    { id: "date", name: "Communication Date", type: "date" },
    { id: "type", name: "Communication Type", type: "string" },
    { id: "subject", name: "Subject", type: "string" },
    { id: "status", name: "Status", type: "string" }
  ]
}

// Query operators
const operators = [
  { id: "equals", name: "Equals", types: ["string", "number", "date"] },
  { id: "not_equals", name: "Not Equals", types: ["string", "number", "date"] },
  { id: "contains", name: "Contains", types: ["string"] },
  { id: "greater_than", name: "Greater Than", types: ["number", "date"] },
  { id: "less_than", name: "Less Than", types: ["number", "date"] },
  { id: "between", name: "Between", types: ["number", "date"] },
  { id: "in", name: "In List", types: ["string", "number"] }
]

export function CustomReportBuilder({ 
  onGenerate, 
  onPreview, 
  onSave, 
  className 
}: CustomReportBuilderProps) {
  const [reportName, setReportName] = useState("")
  const [reportDescription, setReportDescription] = useState("")
  const [selectedDataSources, setSelectedDataSources] = useState<string[]>([])
  const [selectedFields, setSelectedFields] = useState<string[]>([])
  const [conditions, setConditions] = useState<QueryCondition[]>([])
  const [groupBy, setGroupBy] = useState<string[]>([])
  const [orderBy, setOrderBy] = useState<string[]>([])
  const [visualization, setVisualization] = useState<ReportVisualization>({
    type: "table"
  })
  const [limit, setLimit] = useState<number>(100)

  const handleDataSourceChange = (sourceId: string, checked: boolean) => {
    if (checked) {
      setSelectedDataSources([...selectedDataSources, sourceId])
    } else {
      setSelectedDataSources(selectedDataSources.filter(id => id !== sourceId))
      // Remove fields from deselected data source
      const sourceFields = dataFields[sourceId as keyof typeof dataFields]?.map(f => `${sourceId}.${f.id}`) || []
      setSelectedFields(selectedFields.filter(field => !sourceFields.includes(field)))
    }
  }

  const handleFieldChange = (fieldId: string, checked: boolean) => {
    if (checked) {
      setSelectedFields([...selectedFields, fieldId])
    } else {
      setSelectedFields(selectedFields.filter(id => id !== fieldId))
    }
  }

  const addCondition = () => {
    setConditions([...conditions, {
      field: "",
      operator: "equals",
      value: ""
    }])
  }

  const updateCondition = (index: number, updates: Partial<QueryCondition>) => {
    const newConditions = [...conditions]
    newConditions[index] = { ...newConditions[index], ...updates }
    setConditions(newConditions)
  }

  const removeCondition = (index: number) => {
    setConditions(conditions.filter((_, i) => i !== index))
  }

  const getAvailableFields = () => {
    const fields: Array<{ id: string, name: string, type: string, source: string }> = []
    selectedDataSources.forEach(sourceId => {
      const sourceFields = dataFields[sourceId as keyof typeof dataFields] || []
      sourceFields.forEach(field => {
        fields.push({
          ...field,
          id: `${sourceId}.${field.id}`,
          source: sourceId
        })
      })
    })
    return fields
  }

  const handleGenerateReport = () => {
    const query: ReportQuery = {
      tables: selectedDataSources,
      fields: selectedFields,
      conditions,
      groupBy: groupBy.length > 0 ? groupBy : undefined,
      orderBy: orderBy.length > 0 ? orderBy : undefined,
      limit
    }

    const report: CustomReport = {
      id: `custom_${Date.now()}`,
      name: reportName,
      description: reportDescription,
      query,
      visualization,
      data: [], // Would be populated by actual query execution
      generatedAt: new Date().toISOString()
    }

    onGenerate(report)
  }

  const handlePreviewReport = () => {
    // Similar to generate but for preview
    const query: ReportQuery = {
      tables: selectedDataSources,
      fields: selectedFields,
      conditions,
      groupBy: groupBy.length > 0 ? groupBy : undefined,
      orderBy: orderBy.length > 0 ? orderBy : undefined,
      limit: Math.min(limit, 10) // Limit preview to 10 rows
    }

    const report: CustomReport = {
      id: `preview_${Date.now()}`,
      name: reportName || "Preview Report",
      description: reportDescription,
      query,
      visualization,
      data: [], // Would be populated by actual query execution
      generatedAt: new Date().toISOString()
    }

    onPreview(report)
  }

  const handleSaveReport = () => {
    const query: ReportQuery = {
      tables: selectedDataSources,
      fields: selectedFields,
      conditions,
      groupBy: groupBy.length > 0 ? groupBy : undefined,
      orderBy: orderBy.length > 0 ? orderBy : undefined,
      limit
    }

    const report: CustomReport = {
      id: `saved_${Date.now()}`,
      name: reportName,
      description: reportDescription,
      query,
      visualization,
      data: [],
      generatedAt: new Date().toISOString()
    }

    onSave(report)
  }

  const availableFields = getAvailableFields()

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Report Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Custom Report Builder
          </CardTitle>
          <CardDescription>
            Create flexible reports with custom data sources and visualizations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="report-name">Report Name</Label>
              <Input
                id="report-name"
                value={reportName}
                onChange={(e) => setReportName(e.target.value)}
                placeholder="Enter report name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="report-limit">Row Limit</Label>
              <Input
                id="report-limit"
                type="number"
                value={limit}
                onChange={(e) => setLimit(parseInt(e.target.value) || 100)}
                placeholder="100"
                min="1"
                max="10000"
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="report-description">Description</Label>
            <Textarea
              id="report-description"
              value={reportDescription}
              onChange={(e) => setReportDescription(e.target.value)}
              placeholder="Describe what this report shows"
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      {/* Report Configuration Tabs */}
      <Tabs defaultValue="data" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="data">Data Sources</TabsTrigger>
          <TabsTrigger value="fields">Fields</TabsTrigger>
          <TabsTrigger value="filters">Filters</TabsTrigger>
          <TabsTrigger value="visualization">Visualization</TabsTrigger>
        </TabsList>

        <TabsContent value="data" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Select Data Sources</CardTitle>
              <CardDescription>
                Choose the data sources you want to include in your report
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {dataSources.map((source) => (
                  <div key={source.id} className="flex items-start space-x-3 p-4 border rounded-lg">
                    <Checkbox
                      id={source.id}
                      checked={selectedDataSources.includes(source.id)}
                      onCheckedChange={(checked) => 
                        handleDataSourceChange(source.id, checked as boolean)
                      }
                    />
                    <div className="space-y-1">
                      <Label htmlFor={source.id} className="text-sm font-medium">
                        {source.name}
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        {source.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fields" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Select Fields</CardTitle>
              <CardDescription>
                Choose the specific fields you want to include in your report
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedDataSources.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <TableIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Please select data sources first</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {selectedDataSources.map((sourceId) => {
                    const source = dataSources.find(s => s.id === sourceId)
                    const fields = dataFields[sourceId as keyof typeof dataFields] || []
                    
                    return (
                      <div key={sourceId} className="space-y-3">
                        <h4 className="font-medium text-sm">{source?.name}</h4>
                        <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
                          {fields.map((field) => {
                            const fieldId = `${sourceId}.${field.id}`
                            return (
                              <div key={fieldId} className="flex items-center space-x-2">
                                <Checkbox
                                  id={fieldId}
                                  checked={selectedFields.includes(fieldId)}
                                  onCheckedChange={(checked) => 
                                    handleFieldChange(fieldId, checked as boolean)
                                  }
                                />
                                <Label htmlFor={fieldId} className="text-sm">
                                  {field.name}
                                  <Badge variant="outline" className="ml-1 text-xs">
                                    {field.type}
                                  </Badge>
                                </Label>
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="filters" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Filters & Conditions</CardTitle>
                  <CardDescription>
                    Add conditions to filter your data
                  </CardDescription>
                </div>
                <Button onClick={addCondition} size="sm">
                  <Plus className="h-4 w-4 mr-1" />
                  Add Filter
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {conditions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Filter className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No filters added</p>
                  <p className="text-sm">Click "Add Filter" to create conditions</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {conditions.map((condition, index) => (
                    <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                      <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label className="text-xs">Field</Label>
                          <Select
                            value={condition.field}
                            onValueChange={(value) => updateCondition(index, { field: value })}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select field" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableFields.map((field) => (
                                <SelectItem key={field.id} value={field.id}>
                                  {field.name} ({field.source})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-xs">Operator</Label>
                          <Select
                            value={condition.operator}
                            onValueChange={(value) => updateCondition(index, { operator: value as any })}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select operator" />
                            </SelectTrigger>
                            <SelectContent>
                              {operators.map((op) => (
                                <SelectItem key={op.id} value={op.id}>
                                  {op.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-xs">Value</Label>
                          <Input
                            value={condition.value}
                            onChange={(e) => updateCondition(index, { value: e.target.value })}
                            placeholder="Enter value"
                          />
                        </div>
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCondition(index)}
                        className="text-destructive hover:text-destructive"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="visualization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Visualization Settings</CardTitle>
              <CardDescription>
                Choose how to display your report data
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Visualization Type</Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        visualization.type === 'table' ? 'border-primary bg-primary/5' : 'hover:border-primary/50'
                      }`}
                      onClick={() => setVisualization({ ...visualization, type: 'table' })}
                    >
                      <div className="text-center">
                        <TableIcon className="h-8 w-8 mx-auto mb-2" />
                        <div className="text-sm font-medium">Table</div>
                      </div>
                    </div>

                    <div
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        visualization.type === 'chart' && visualization.chartType === 'bar' ? 'border-primary bg-primary/5' : 'hover:border-primary/50'
                      }`}
                      onClick={() => setVisualization({ ...visualization, type: 'chart', chartType: 'bar' })}
                    >
                      <div className="text-center">
                        <BarChart3 className="h-8 w-8 mx-auto mb-2" />
                        <div className="text-sm font-medium">Bar Chart</div>
                      </div>
                    </div>

                    <div
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        visualization.type === 'chart' && visualization.chartType === 'pie' ? 'border-primary bg-primary/5' : 'hover:border-primary/50'
                      }`}
                      onClick={() => setVisualization({ ...visualization, type: 'chart', chartType: 'pie' })}
                    >
                      <div className="text-center">
                        <PieChart className="h-8 w-8 mx-auto mb-2" />
                        <div className="text-sm font-medium">Pie Chart</div>
                      </div>
                    </div>

                    <div
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        visualization.type === 'chart' && visualization.chartType === 'line' ? 'border-primary bg-primary/5' : 'hover:border-primary/50'
                      }`}
                      onClick={() => setVisualization({ ...visualization, type: 'chart', chartType: 'line' })}
                    >
                      <div className="text-center">
                        <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                        <div className="text-sm font-medium">Line Chart</div>
                      </div>
                    </div>
                  </div>
                </div>

                {visualization.type === 'chart' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>X-Axis Field</Label>
                      <Select
                        value={visualization.xAxis || ""}
                        onValueChange={(value) => setVisualization({ ...visualization, xAxis: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select X-axis field" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableFields.map((field) => (
                            <SelectItem key={field.id} value={field.id}>
                              {field.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Y-Axis Field</Label>
                      <Select
                        value={visualization.yAxis || ""}
                        onValueChange={(value) => setVisualization({ ...visualization, yAxis: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Y-axis field" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableFields.filter(f => f.type === 'number').map((field) => (
                            <SelectItem key={field.id} value={field.id}>
                              {field.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label>Group By (Optional)</Label>
                  <div className="flex flex-wrap gap-2">
                    {availableFields.map((field) => (
                      <div key={field.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`group-${field.id}`}
                          checked={groupBy.includes(field.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setGroupBy([...groupBy, field.id])
                            } else {
                              setGroupBy(groupBy.filter(id => id !== field.id))
                            }
                          }}
                        />
                        <Label htmlFor={`group-${field.id}`} className="text-sm">
                          {field.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h3 className="text-lg font-medium">Generate Custom Report</h3>
              <p className="text-sm text-muted-foreground">
                {selectedDataSources.length} data source(s), {selectedFields.length} field(s), {conditions.length} filter(s)
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleSaveReport} disabled={!reportName}>
                <Save className="mr-2 h-4 w-4" />
                Save Template
              </Button>
              <Button variant="outline" onClick={handlePreviewReport} disabled={selectedFields.length === 0}>
                <Eye className="mr-2 h-4 w-4" />
                Preview
              </Button>
              <Button onClick={handleGenerateReport} disabled={selectedFields.length === 0}>
                <Download className="mr-2 h-4 w-4" />
                Generate Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
