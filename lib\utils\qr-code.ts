import { Student, QRCodeData, getFullName } from "@/lib/types/student"

// QR Code generation utilities
export class QRCodeGenerator {
  private static instance: QRCodeGenerator
  
  static getInstance(): QRCodeGenerator {
    if (!QRCodeGenerator.instance) {
      QRCodeGenerator.instance = new QRCodeGenerator()
    }
    return QRCodeGenerator.instance
  }

  // Generate QR code data for a student
  generateQRData(student: Student): QRCodeData {
    return {
      studentId: student.id,
      name: getFullName(student),
      grade: student.grade,
      section: student.section,
      validUntil: this.getValidUntilDate()
    }
  }

  // Generate QR code string (what gets encoded in the QR code)
  generateQRString(student: Student): string {
    const data = this.generateQRData(student)
    return JSON.stringify({
      id: data.studentId,
      name: data.name,
      grade: data.grade,
      section: data.section,
      school: "Tanauan School of Arts and Trade",
      type: "student_id",
      validUntil: data.validUntil,
      generated: new Date().toISOString()
    })
  }

  // Generate a unique QR code identifier
  generateQRCodeId(student: Student): string {
    const timestamp = Date.now()
    const year = new Date().getFullYear()
    return `QR_${student.id}_${year}_${timestamp.toString().slice(-6)}`
  }

  // Get validity date (1 year from now)
  private getValidUntilDate(): string {
    const date = new Date()
    date.setFullYear(date.getFullYear() + 1)
    return date.toISOString().split('T')[0]
  }

  // Validate QR code data
  validateQRData(qrString: string): { valid: boolean; data?: any; error?: string } {
    try {
      const data = JSON.parse(qrString)
      
      if (!data.id || !data.name || !data.type) {
        return { valid: false, error: 'Missing required fields' }
      }

      if (data.type !== 'student_id') {
        return { valid: false, error: 'Invalid QR code type' }
      }

      if (data.validUntil && new Date(data.validUntil) < new Date()) {
        return { valid: false, error: 'QR code has expired' }
      }

      return { valid: true, data }
    } catch (error) {
      return { valid: false, error: 'Invalid QR code format' }
    }
  }
}

// QR Code printing utilities
export interface QRPrintOptions {
  size: 'small' | 'medium' | 'large'
  includeStudentInfo: boolean
  includeSchoolLogo: boolean
  paperSize: 'A4' | 'Letter'
  codesPerSheet: number
}

export class QRCodePrinter {
  private static instance: QRCodePrinter
  
  static getInstance(): QRCodePrinter {
    if (!QRCodePrinter.instance) {
      QRCodePrinter.instance = new QRCodePrinter()
    }
    return QRCodePrinter.instance
  }

  // Get QR code size in pixels
  getQRSize(size: 'small' | 'medium' | 'large'): number {
    switch (size) {
      case 'small': return 96  // 1 inch at 96 DPI
      case 'medium': return 144 // 1.5 inches at 96 DPI
      case 'large': return 192  // 2 inches at 96 DPI
      default: return 144
    }
  }

  // Generate print layout for multiple QR codes
  generatePrintLayout(students: Student[], options: QRPrintOptions): PrintLayout {
    const qrGenerator = QRCodeGenerator.getInstance()
    const qrSize = this.getQRSize(options.size)
    
    const qrCodes = students.map(student => ({
      student,
      qrData: qrGenerator.generateQRString(student),
      qrId: qrGenerator.generateQRCodeId(student)
    }))

    const sheets = this.layoutQRCodes(qrCodes, options)
    
    return {
      sheets,
      totalCodes: qrCodes.length,
      totalSheets: sheets.length,
      options
    }
  }

  // Layout QR codes on sheets
  private layoutQRCodes(qrCodes: any[], options: QRPrintOptions): PrintSheet[] {
    const sheets: PrintSheet[] = []
    const codesPerSheet = options.codesPerSheet
    
    for (let i = 0; i < qrCodes.length; i += codesPerSheet) {
      const sheetCodes = qrCodes.slice(i, i + codesPerSheet)
      sheets.push({
        id: `sheet_${Math.floor(i / codesPerSheet) + 1}`,
        codes: sheetCodes,
        paperSize: options.paperSize
      })
    }
    
    return sheets
  }

  // Generate CSS for print styles
  generatePrintCSS(options: QRPrintOptions): string {
    const qrSize = this.getQRSize(options.size)
    const margin = 10 // 10px margin
    
    return `
      @media print {
        @page {
          size: ${options.paperSize};
          margin: 0.5in;
        }
        
        .qr-sheet {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(${qrSize + margin * 2}px, 1fr));
          gap: ${margin}px;
          page-break-after: always;
        }
        
        .qr-code-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          padding: ${margin}px;
          border: 1px solid #ddd;
          border-radius: 4px;
        }
        
        .qr-code-image {
          width: ${qrSize}px;
          height: ${qrSize}px;
          margin-bottom: 8px;
        }
        
        .student-info {
          font-size: 10px;
          line-height: 1.2;
        }
        
        .student-name {
          font-weight: bold;
          margin-bottom: 2px;
        }
        
        .student-details {
          color: #666;
        }
      }
    `
  }
}

// Types for print layout
export interface PrintLayout {
  sheets: PrintSheet[]
  totalCodes: number
  totalSheets: number
  options: QRPrintOptions
}

export interface PrintSheet {
  id: string
  codes: Array<{
    student: Student
    qrData: string
    qrId: string
  }>
  paperSize: 'A4' | 'Letter'
}

// QR Code management utilities
export class QRCodeManager {
  private static instance: QRCodeManager
  
  static getInstance(): QRCodeManager {
    if (!QRCodeManager.instance) {
      QRCodeManager.instance = new QRCodeManager()
    }
    return QRCodeManager.instance
  }

  // Generate QR code for a student
  async generateForStudent(student: Student): Promise<string> {
    const generator = QRCodeGenerator.getInstance()
    const qrString = generator.generateQRString(student)
    const qrId = generator.generateQRCodeId(student)
    
    // In a real implementation, you would:
    // 1. Generate the actual QR code image using a library like qrcode
    // 2. Save it to storage (file system, cloud storage, etc.)
    // 3. Update the student record with the QR code ID
    
    // For now, we'll simulate this
    await new Promise(resolve => setTimeout(resolve, 100))
    
    return qrId
  }

  // Generate QR codes for multiple students
  async generateBatch(students: Student[], onProgress?: (progress: number) => void): Promise<string[]> {
    const qrIds: string[] = []
    
    for (let i = 0; i < students.length; i++) {
      const qrId = await this.generateForStudent(students[i])
      qrIds.push(qrId)
      
      if (onProgress) {
        onProgress(((i + 1) / students.length) * 100)
      }
    }
    
    return qrIds
  }

  // Regenerate QR code for a student
  async regenerateForStudent(student: Student): Promise<string> {
    // Invalidate old QR code and generate new one
    return this.generateForStudent(student)
  }

  // Validate and decode QR code
  validateAndDecode(qrString: string): { valid: boolean; studentId?: string; error?: string } {
    const generator = QRCodeGenerator.getInstance()
    const result = generator.validateQRData(qrString)
    
    if (result.valid && result.data) {
      return {
        valid: true,
        studentId: result.data.id
      }
    }
    
    return {
      valid: false,
      error: result.error
    }
  }
}

// Export singleton instances
export const qrGenerator = QRCodeGenerator.getInstance()
export const qrPrinter = QRCodePrinter.getInstance()
export const qrManager = QRCodeManager.getInstance()
