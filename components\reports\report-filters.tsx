"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { DateRangePicker, DateRange, dateRangePresets } from "@/components/ui/date-range-picker"
import { ReportFilters as ReportFiltersType, ReportType } from "@/lib/types/reports"
import { Filter, X, Calendar } from "lucide-react"

interface ReportFiltersProps {
  filters: ReportFiltersType
  onFiltersChange: (filters: ReportFiltersType) => void
  reportType: ReportType
  className?: string
}

// Mock data for filter options
const gradeOptions = ["7", "8", "9", "10", "11", "12"]
const sectionOptions = ["A", "B", "C", "D", "E"]
const courseOptions = ["Junior High School", "Senior High School - STEM", "Senior High School - ABM", "Senior High School - HUMSS", "Senior High School - GAS"]
const subjectOptions = ["Mathematics", "English", "Science", "Filipino", "Social Studies", "PE", "TLE", "Arts", "Music"]
const teacherOptions = ["Prof. Santos", "Mrs. Reyes", "Mr. Torres", "Ms. Garcia", "Dr. Rodriguez"]
const attendanceStatusOptions = ["Present", "Late", "Absent"]

export function ReportFilters({ 
  filters, 
  onFiltersChange, 
  reportType,
  className 
}: ReportFiltersProps) {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: filters.dateRange ? new Date(filters.dateRange.startDate) : undefined,
    to: filters.dateRange ? new Date(filters.dateRange.endDate) : undefined
  })

  const handleDateRangeChange = (range: DateRange) => {
    setDateRange(range)
    if (range.from && range.to) {
      onFiltersChange({
        ...filters,
        dateRange: {
          startDate: range.from.toISOString().split('T')[0],
          endDate: range.to.toISOString().split('T')[0]
        }
      })
    }
  }

  const handleArrayFilterChange = (key: keyof ReportFiltersType, value: string, checked: boolean) => {
    const currentArray = (filters[key] as string[]) || []
    let newArray: string[]
    
    if (checked) {
      newArray = [...currentArray, value]
    } else {
      newArray = currentArray.filter(item => item !== value)
    }
    
    onFiltersChange({
      ...filters,
      [key]: newArray.length > 0 ? newArray : undefined
    })
  }

  const handleBooleanFilterChange = (key: keyof ReportFiltersType, checked: boolean) => {
    onFiltersChange({
      ...filters,
      [key]: checked
    })
  }

  const clearFilters = () => {
    onFiltersChange({})
    setDateRange({ from: undefined, to: undefined })
  }

  const applyPreset = (preset: typeof dateRangePresets[0]) => {
    const range = preset.value()
    setDateRange(range)
    onFiltersChange({
      ...filters,
      dateRange: {
        startDate: range.from.toISOString().split('T')[0],
        endDate: range.to.toISOString().split('T')[0]
      }
    })
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.grades?.length) count++
    if (filters.sections?.length) count++
    if (filters.courses?.length) count++
    if (filters.students?.length) count++
    if (filters.subjects?.length) count++
    if (filters.teachers?.length) count++
    if (filters.attendanceStatus?.length) count++
    if (filters.includeTransferred) count++
    if (filters.includeInactive) count++
    return count
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Report Filters
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary">{getActiveFiltersCount()} active</Badge>
            )}
          </div>
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            <X className="h-4 w-4 mr-1" />
            Clear All
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Date Range */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Date Range</Label>
          <DateRangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            className="w-full"
          />
          
          {/* Quick Date Presets */}
          <div className="flex flex-wrap gap-2">
            {dateRangePresets.map((preset) => (
              <Button
                key={preset.label}
                variant="outline"
                size="sm"
                onClick={() => applyPreset(preset)}
                className="text-xs"
              >
                <Calendar className="h-3 w-3 mr-1" />
                {preset.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Grade Levels */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Grade Levels</Label>
          <div className="grid grid-cols-3 gap-2">
            {gradeOptions.map((grade) => (
              <div key={grade} className="flex items-center space-x-2">
                <Checkbox
                  id={`grade-${grade}`}
                  checked={filters.grades?.includes(grade) || false}
                  onCheckedChange={(checked) => 
                    handleArrayFilterChange('grades', grade, checked as boolean)
                  }
                />
                <Label htmlFor={`grade-${grade}`} className="text-sm">
                  Grade {grade}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Sections */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Sections</Label>
          <div className="grid grid-cols-5 gap-2">
            {sectionOptions.map((section) => (
              <div key={section} className="flex items-center space-x-2">
                <Checkbox
                  id={`section-${section}`}
                  checked={filters.sections?.includes(section) || false}
                  onCheckedChange={(checked) => 
                    handleArrayFilterChange('sections', section, checked as boolean)
                  }
                />
                <Label htmlFor={`section-${section}`} className="text-sm">
                  {section}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Courses */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Courses</Label>
          <div className="space-y-2">
            {courseOptions.map((course) => (
              <div key={course} className="flex items-center space-x-2">
                <Checkbox
                  id={`course-${course}`}
                  checked={filters.courses?.includes(course) || false}
                  onCheckedChange={(checked) => 
                    handleArrayFilterChange('courses', course, checked as boolean)
                  }
                />
                <Label htmlFor={`course-${course}`} className="text-sm">
                  {course}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Subjects (for subject-specific reports) */}
        {(reportType === 'CUSTOM' || reportType === 'SF2') && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">Subjects</Label>
            <div className="space-y-2">
              {subjectOptions.map((subject) => (
                <div key={subject} className="flex items-center space-x-2">
                  <Checkbox
                    id={`subject-${subject}`}
                    checked={filters.subjects?.includes(subject) || false}
                    onCheckedChange={(checked) => 
                      handleArrayFilterChange('subjects', subject, checked as boolean)
                    }
                  />
                  <Label htmlFor={`subject-${subject}`} className="text-sm">
                    {subject}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Attendance Status */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Attendance Status</Label>
          <div className="space-y-2">
            {attendanceStatusOptions.map((status) => (
              <div key={status} className="flex items-center space-x-2">
                <Checkbox
                  id={`status-${status}`}
                  checked={filters.attendanceStatus?.includes(status as any) || false}
                  onCheckedChange={(checked) => 
                    handleArrayFilterChange('attendanceStatus', status, checked as boolean)
                  }
                />
                <Label htmlFor={`status-${status}`} className="text-sm">
                  {status}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Options */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Additional Options</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-transferred"
                checked={filters.includeTransferred || false}
                onCheckedChange={(checked) => 
                  handleBooleanFilterChange('includeTransferred', checked as boolean)
                }
              />
              <Label htmlFor="include-transferred" className="text-sm">
                Include transferred students
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-inactive"
                checked={filters.includeInactive || false}
                onCheckedChange={(checked) => 
                  handleBooleanFilterChange('includeInactive', checked as boolean)
                }
              />
              <Label htmlFor="include-inactive" className="text-sm">
                Include inactive students
              </Label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
