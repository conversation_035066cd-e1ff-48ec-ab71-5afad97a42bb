"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  BarChart3,
  Download,
  Eye
} from "lucide-react"
import { Student, AttendanceStats } from "@/lib/types/student"
import { cn } from "@/lib/utils"

interface AttendanceStatisticsProps {
  student: Student
  showDetailed?: boolean
  showActions?: boolean
  className?: string
}

export function AttendanceStatistics({
  student,
  showDetailed = false,
  showActions = false,
  className
}: AttendanceStatisticsProps) {
  const stats = student.attendanceStats

  if (!stats) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="text-center py-8">
          <Clock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Attendance Data</h3>
          <p className="text-muted-foreground">
            Attendance statistics will appear here once the student starts attending classes.
          </p>
        </CardContent>
      </Card>
    )
  }

  const getAttendanceGrade = (rate: number): { grade: string; color: string; icon: React.ReactNode } => {
    if (rate >= 95) {
      return {
        grade: 'Excellent',
        color: 'text-green-600',
        icon: <CheckCircle className="h-4 w-4 text-green-600" />
      }
    } else if (rate >= 85) {
      return {
        grade: 'Good',
        color: 'text-blue-600',
        icon: <CheckCircle className="h-4 w-4 text-blue-600" />
      }
    } else if (rate >= 75) {
      return {
        grade: 'Fair',
        color: 'text-yellow-600',
        icon: <AlertCircle className="h-4 w-4 text-yellow-600" />
      }
    } else {
      return {
        grade: 'Poor',
        color: 'text-red-600',
        icon: <XCircle className="h-4 w-4 text-red-600" />
      }
    }
  }

  const attendanceGrade = getAttendanceGrade(stats.attendanceRate)

  const getTrendIcon = () => {
    // Mock trend calculation - in real app, this would compare with previous period
    const trend = stats.attendanceRate >= 85 ? 'up' : 'down'
    return trend === 'up' ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-600" />
    )
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          Attendance Statistics
          {showActions && (
            <div className="ml-auto flex gap-2">
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          )}
        </CardTitle>
        <CardDescription>
          Attendance summary for the current academic period
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Attendance Rate */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center gap-2">
            <span className="text-3xl font-bold">{stats.attendanceRate.toFixed(1)}%</span>
            {getTrendIcon()}
          </div>
          <div className="flex items-center justify-center gap-2">
            {attendanceGrade.icon}
            <span className={cn("font-medium", attendanceGrade.color)}>
              {attendanceGrade.grade} Attendance
            </span>
          </div>
          <Progress value={stats.attendanceRate} className="w-full max-w-xs mx-auto" />
        </div>

        <Separator />

        {/* Attendance Breakdown */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center space-y-1">
            <div className="text-2xl font-bold text-green-600">{stats.presentDays}</div>
            <div className="text-sm text-muted-foreground">Present</div>
            <div className="text-xs text-muted-foreground">
              {((stats.presentDays / stats.totalDays) * 100).toFixed(1)}%
            </div>
          </div>
          
          <div className="text-center space-y-1">
            <div className="text-2xl font-bold text-yellow-600">{stats.lateDays}</div>
            <div className="text-sm text-muted-foreground">Late</div>
            <div className="text-xs text-muted-foreground">
              {((stats.lateDays / stats.totalDays) * 100).toFixed(1)}%
            </div>
          </div>
          
          <div className="text-center space-y-1">
            <div className="text-2xl font-bold text-red-600">{stats.absentDays}</div>
            <div className="text-sm text-muted-foreground">Absent</div>
            <div className="text-xs text-muted-foreground">
              {((stats.absentDays / stats.totalDays) * 100).toFixed(1)}%
            </div>
          </div>
          
          <div className="text-center space-y-1">
            <div className="text-2xl font-bold">{stats.totalDays}</div>
            <div className="text-sm text-muted-foreground">Total Days</div>
            <div className="text-xs text-muted-foreground">
              School Days
            </div>
          </div>
        </div>

        {showDetailed && (
          <>
            <Separator />
            
            {/* Detailed Breakdown */}
            <div className="space-y-3">
              <h4 className="font-medium">Detailed Breakdown</h4>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="font-medium">Present Days</span>
                  </div>
                  <div className="text-right">
                    <div className="font-bold">{stats.presentDays}</div>
                    <div className="text-sm text-muted-foreground">
                      {((stats.presentDays / stats.totalDays) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <span className="font-medium">Late Arrivals</span>
                  </div>
                  <div className="text-right">
                    <div className="font-bold">{stats.lateDays}</div>
                    <div className="text-sm text-muted-foreground">
                      {((stats.lateDays / stats.totalDays) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-600" />
                    <span className="font-medium">Absent Days</span>
                  </div>
                  <div className="text-right">
                    <div className="font-bold">{stats.absentDays}</div>
                    <div className="text-sm text-muted-foreground">
                      {((stats.absentDays / stats.totalDays) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Last Attendance */}
        {stats.lastAttendance && (
          <>
            <Separator />
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>
                Last attendance: {new Date(stats.lastAttendance).toLocaleDateString()}
              </span>
            </div>
          </>
        )}

        {/* Attendance Alerts */}
        {stats.attendanceRate < 75 && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Low Attendance Alert</span>
            </div>
            <p className="text-sm text-red-700 mt-1">
              This student's attendance rate is below the minimum requirement of 75%. 
              Consider reaching out to the student and guardian.
            </p>
          </div>
        )}

        {stats.absentDays >= 5 && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 text-yellow-800">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Frequent Absences</span>
            </div>
            <p className="text-sm text-yellow-700 mt-1">
              This student has been absent for {stats.absentDays} days. 
              Monitor attendance closely and consider intervention.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Compact version for table display
export function AttendanceStatisticsCompact({
  student,
  className
}: {
  student: Student
  className?: string
}) {
  const stats = student.attendanceStats

  if (!stats) {
    return (
      <div className={cn("text-center", className)}>
        <span className="text-muted-foreground text-sm">No data</span>
      </div>
    )
  }

  const getAttendanceIcon = (rate: number) => {
    if (rate >= 95) return <CheckCircle className="h-3 w-3 text-green-500" />
    if (rate >= 85) return <CheckCircle className="h-3 w-3 text-blue-500" />
    if (rate >= 75) return <AlertCircle className="h-3 w-3 text-yellow-500" />
    return <XCircle className="h-3 w-3 text-red-500" />
  }

  return (
    <div className={cn("flex items-center gap-1", className)}>
      {getAttendanceIcon(stats.attendanceRate)}
      <span className="text-sm font-medium">
        {stats.attendanceRate.toFixed(1)}%
      </span>
    </div>
  )
}
