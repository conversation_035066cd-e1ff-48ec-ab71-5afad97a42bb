import { Student, DetailedAttendanceRecord } from './scanner'

// Base report types
export type ReportType = 'SF2' | 'SF4' | 'CUSTOM' | 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'ANNUAL'
export type ReportStatus = 'DRAFT' | 'GENERATING' | 'READY' | 'FAILED' | 'ARCHIVED'
export type ExportFormat = 'PDF' | 'EXCEL' | 'CSV' | 'PRINT'

// Report configuration
export interface ReportConfig {
  id: string
  name: string
  type: ReportType
  description: string
  dateRange: {
    startDate: string
    endDate: string
  }
  filters: ReportFilters
  template?: string
  settings: ReportSettings
  createdBy: string
  createdAt: string
  lastModified: string
}

export interface ReportFilters {
  grades?: string[]
  sections?: string[]
  courses?: string[]
  students?: string[]
  subjects?: string[]
  teachers?: string[]
  attendanceStatus?: ('Present' | 'Late' | 'Absent')[]
  includeTransferred?: boolean
  includeInactive?: boolean
}

export interface ReportSettings {
  includePhotos?: boolean
  includeSignatures?: boolean
  includeRemarks?: boolean
  showStatistics?: boolean
  groupBy?: 'grade' | 'section' | 'course' | 'subject'
  sortBy?: 'name' | 'id' | 'attendance' | 'grade'
  pageOrientation?: 'portrait' | 'landscape'
  fontSize?: 'small' | 'medium' | 'large'
  includeHeader?: boolean
  includeFooter?: boolean
  watermark?: string
}

// Generated report
export interface GeneratedReport {
  id: string
  config: ReportConfig
  status: ReportStatus
  generatedAt: string
  fileSize?: number
  downloadCount: number
  expiresAt?: string
  error?: string
  metadata: ReportMetadata
}

export interface ReportMetadata {
  totalStudents: number
  totalRecords: number
  dateRange: {
    startDate: string
    endDate: string
  }
  statistics: {
    presentCount: number
    lateCount: number
    absentCount: number
    attendanceRate: number
  }
  schoolInfo: SchoolInfo
}

// School information for DepEd forms
export interface SchoolInfo {
  schoolId: string
  schoolName: string
  address: string
  district: string
  division: string
  region: string
  principalName: string
  principalSignature?: string
  schoolYear: string
  semester?: string
  quarter?: string
}

// SF2 Daily Attendance Report specific types
export interface SF2Report {
  id: string
  date: string
  grade: string
  section: string
  subject?: string
  teacher: TeacherInfo
  students: SF2StudentRecord[]
  summary: SF2Summary
  schoolInfo: SchoolInfo
  generatedAt: string
}

export interface SF2StudentRecord {
  studentId: string
  studentName: string
  attendance: {
    [period: string]: 'P' | 'L' | 'A' | 'E' // Present, Late, Absent, Excused
  }
  dailyStatus: 'P' | 'L' | 'A' | 'E'
  remarks?: string
}

export interface SF2Summary {
  totalStudents: number
  presentCount: number
  lateCount: number
  absentCount: number
  excusedCount: number
  attendanceRate: number
}

export interface TeacherInfo {
  id: string
  name: string
  position: string
  signature?: string
  dateChecked?: string
}

// SF4 Monthly Learner's Movement specific types
export interface SF4Report {
  id: string
  month: string
  year: string
  grade: string
  section?: string
  enrollment: SF4Enrollment
  movement: SF4Movement
  attendance: SF4Attendance
  schoolInfo: SchoolInfo
  principalReview: PrincipalReview
  generatedAt: string
}

export interface SF4Enrollment {
  beginningOfMonth: number
  newAdmissions: SF4Student[]
  transfers: {
    transferredIn: SF4Student[]
    transferredOut: SF4Student[]
  }
  dropouts: SF4Student[]
  endOfMonth: number
}

export interface SF4Student {
  studentId: string
  name: string
  dateOfAction: string
  reason?: string
  destination?: string // For transfers
}

export interface SF4Movement {
  totalEnrolled: number
  maleCount: number
  femaleCount: number
  newAdmissions: number
  transfersIn: number
  transfersOut: number
  dropouts: number
  graduations?: number
}

export interface SF4Attendance {
  totalSchoolDays: number
  averageAttendance: number
  attendanceRate: number
  chronicAbsentees: string[] // Student IDs
  perfectAttendance: string[] // Student IDs
}

export interface PrincipalReview {
  reviewedBy: string
  reviewDate: string
  signature?: string
  remarks?: string
  approved: boolean
}

// Custom report types
export interface CustomReport {
  id: string
  name: string
  description: string
  query: ReportQuery
  visualization?: ReportVisualization
  data: any[]
  generatedAt: string
}

export interface ReportQuery {
  tables: string[]
  fields: string[]
  conditions: QueryCondition[]
  groupBy?: string[]
  orderBy?: string[]
  limit?: number
}

export interface QueryCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'between' | 'in'
  value: any
}

export interface ReportVisualization {
  type: 'table' | 'chart' | 'graph'
  chartType?: 'bar' | 'line' | 'pie' | 'area'
  xAxis?: string
  yAxis?: string
  groupBy?: string
}

// Report templates
export interface ReportTemplate {
  id: string
  name: string
  type: ReportType
  description: string
  config: Partial<ReportConfig>
  isDefault: boolean
  isPublic: boolean
  createdBy: string
  createdAt: string
}

// Report scheduling
export interface ReportSchedule {
  id: string
  reportConfigId: string
  name: string
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually'
  dayOfWeek?: number // 0-6 for weekly
  dayOfMonth?: number // 1-31 for monthly
  time: string // HH:MM format
  timezone: string
  isActive: boolean
  lastRun?: string
  nextRun: string
  recipients: string[] // Email addresses
  createdBy: string
  createdAt: string
}

// Report archive
export interface ReportArchive {
  id: string
  reportId: string
  archivedAt: string
  archivedBy: string
  reason: string
  retentionPeriod: number // Days
  autoDelete: boolean
}

// Export job
export interface ExportJob {
  id: string
  reportId: string
  format: ExportFormat
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
  progress: number // 0-100
  downloadUrl?: string
  error?: string
  createdAt: string
  completedAt?: string
  expiresAt?: string
}

// Report analytics
export interface ReportAnalytics {
  reportId: string
  views: number
  downloads: number
  exports: {
    [format in ExportFormat]: number
  }
  lastAccessed: string
  popularFilters: Record<string, number>
  averageGenerationTime: number
}
