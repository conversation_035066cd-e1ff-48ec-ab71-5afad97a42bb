import { prisma } from '@/lib/prisma'
import { auth } from './config'
import { logSecurityEvent, logAuditEntry } from './security'
import { SecurityEventType, SecuritySeverity } from '@/lib/generated/prisma'
import { SessionInfo } from './types'

// Session configuration
const SESSION_CONFIG = {
  maxAge: 8 * 60 * 60 * 1000, // 8 hours in milliseconds
  idleTimeout: 2 * 60 * 60 * 1000, // 2 hours idle timeout
  maxConcurrentSessions: 3, // Maximum concurrent sessions per user
  extendOnActivity: true, // Extend session on user activity
  secureSessionsOnly: process.env.NODE_ENV === 'production'
}

/**
 * Create a new session for a user
 */
export async function createSession(
  userId: string,
  ipAddress: string,
  userAgent?: string
): Promise<string> {
  try {
    // Generate session token
    const sessionToken = generateSessionToken()
    const expiresAt = new Date(Date.now() + SESSION_CONFIG.maxAge)

    // Check for existing sessions and enforce concurrent session limit
    await enforceSessionLimit(userId)

    // Update user with new session
    await prisma.user.update({
      where: { id: userId },
      data: {
        sessionToken,
        sessionExpiresAt: expiresAt
      }
    })

    // Create login history record
    await prisma.loginHistory.create({
      data: {
        userId,
        ipAddress,
        userAgent,
        success: true,
        sessionId: sessionToken
      }
    })

    // Log security event
    await logSecurityEvent({
      userId,
      eventType: SecurityEventType.LOGIN_SUCCESS,
      severity: SecuritySeverity.LOW,
      description: 'New session created',
      ipAddress,
      userAgent
    })

    return sessionToken
  } catch (error) {
    console.error('Failed to create session:', error)
    throw new Error('Session creation failed')
  }
}

/**
 * Validate and refresh a session
 */
export async function validateSession(sessionToken: string): Promise<{
  isValid: boolean
  user?: any
  shouldRefresh?: boolean
}> {
  try {
    if (!sessionToken) {
      return { isValid: false }
    }

    // Find user with this session token
    const user = await prisma.user.findFirst({
      where: {
        sessionToken,
        isActive: true
      }
    })

    if (!user || !user.sessionExpiresAt) {
      return { isValid: false }
    }

    const now = new Date()
    
    // Check if session has expired
    if (user.sessionExpiresAt < now) {
      await invalidateSession(sessionToken, 'expired')
      return { isValid: false }
    }

    // Check idle timeout
    const lastActivity = user.lastLoginAt || user.updatedAt
    const idleTime = now.getTime() - lastActivity.getTime()
    
    if (idleTime > SESSION_CONFIG.idleTimeout) {
      await invalidateSession(sessionToken, 'idle_timeout')
      return { isValid: false }
    }

    // Determine if session should be refreshed
    const timeUntilExpiry = user.sessionExpiresAt.getTime() - now.getTime()
    const shouldRefresh = SESSION_CONFIG.extendOnActivity && 
                         timeUntilExpiry < (SESSION_CONFIG.maxAge / 2)

    return {
      isValid: true,
      user,
      shouldRefresh
    }
  } catch (error) {
    console.error('Session validation error:', error)
    return { isValid: false }
  }
}

/**
 * Refresh a session (extend expiry time)
 */
export async function refreshSession(
  sessionToken: string,
  ipAddress?: string
): Promise<boolean> {
  try {
    const newExpiresAt = new Date(Date.now() + SESSION_CONFIG.maxAge)

    const result = await prisma.user.updateMany({
      where: {
        sessionToken,
        isActive: true
      },
      data: {
        sessionExpiresAt: newExpiresAt,
        lastLoginAt: new Date(),
        lastLoginIp: ipAddress
      }
    })

    return result.count > 0
  } catch (error) {
    console.error('Session refresh error:', error)
    return false
  }
}

/**
 * Invalidate a session
 */
export async function invalidateSession(
  sessionToken: string,
  reason: string = 'manual'
): Promise<void> {
  try {
    // Get user info before invalidating
    const user = await prisma.user.findFirst({
      where: { sessionToken }
    })

    // Clear session from user
    await prisma.user.updateMany({
      where: { sessionToken },
      data: {
        sessionToken: null,
        sessionExpiresAt: null
      }
    })

    // Update login history
    await prisma.loginHistory.updateMany({
      where: {
        sessionId: sessionToken,
        logoutAt: null
      },
      data: {
        logoutAt: new Date(),
        logoutReason: reason
      }
    })

    // Log security event
    if (user) {
      await logSecurityEvent({
        userId: user.id,
        eventType: SecurityEventType.LOGOUT,
        severity: SecuritySeverity.LOW,
        description: `Session invalidated: ${reason}`,
      })
    }
  } catch (error) {
    console.error('Session invalidation error:', error)
  }
}

/**
 * Invalidate all sessions for a user
 */
export async function invalidateAllUserSessions(
  userId: string,
  reason: string = 'security'
): Promise<void> {
  try {
    // Get user's current session
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (user?.sessionToken) {
      await invalidateSession(user.sessionToken, reason)
    }

    // Update all login history records for this user
    await prisma.loginHistory.updateMany({
      where: {
        userId,
        logoutAt: null
      },
      data: {
        logoutAt: new Date(),
        logoutReason: reason
      }
    })

    // Log security event
    await logSecurityEvent({
      userId,
      eventType: SecurityEventType.LOGOUT,
      severity: SecuritySeverity.MEDIUM,
      description: `All sessions invalidated: ${reason}`
    })
  } catch (error) {
    console.error('Failed to invalidate all user sessions:', error)
  }
}

/**
 * Enforce concurrent session limit
 */
async function enforceSessionLimit(userId: string): Promise<void> {
  try {
    // Count active sessions (this is simplified - in production you'd track sessions properly)
    const activeSessions = await prisma.loginHistory.count({
      where: {
        userId,
        logoutAt: null,
        loginAt: {
          gte: new Date(Date.now() - SESSION_CONFIG.maxAge)
        }
      }
    })

    if (activeSessions >= SESSION_CONFIG.maxConcurrentSessions) {
      // Force logout oldest sessions
      const oldestSessions = await prisma.loginHistory.findMany({
        where: {
          userId,
          logoutAt: null
        },
        orderBy: {
          loginAt: 'asc'
        },
        take: activeSessions - SESSION_CONFIG.maxConcurrentSessions + 1
      })

      for (const session of oldestSessions) {
        if (session.sessionId) {
          await invalidateSession(session.sessionId, 'concurrent_limit')
        }
      }

      // Log security event
      await logSecurityEvent({
        userId,
        eventType: SecurityEventType.LOGOUT,
        severity: SecuritySeverity.MEDIUM,
        description: `Concurrent session limit enforced. Logged out ${oldestSessions.length} sessions.`
      })
    }
  } catch (error) {
    console.error('Failed to enforce session limit:', error)
  }
}

/**
 * Generate a secure session token
 */
function generateSessionToken(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let token = ''
  
  for (let i = 0; i < 64; i++) {
    token += chars[Math.floor(Math.random() * chars.length)]
  }
  
  return token
}

/**
 * Get active sessions for a user
 */
export async function getUserActiveSessions(userId: string): Promise<SessionInfo[]> {
  try {
    const sessions = await prisma.loginHistory.findMany({
      where: {
        userId,
        logoutAt: null,
        loginAt: {
          gte: new Date(Date.now() - SESSION_CONFIG.maxAge)
        }
      },
      orderBy: {
        loginAt: 'desc'
      }
    })

    return sessions.map(session => ({
      id: session.id,
      userId: session.userId,
      token: session.sessionId || '',
      expiresAt: new Date(session.loginAt.getTime() + SESSION_CONFIG.maxAge),
      ipAddress: session.ipAddress,
      userAgent: session.userAgent,
      isActive: !session.logoutAt,
      lastActivity: session.loginAt
    }))
  } catch (error) {
    console.error('Failed to get user active sessions:', error)
    return []
  }
}

/**
 * Clean up expired sessions
 */
export async function cleanupExpiredSessions(): Promise<number> {
  try {
    const expiredTime = new Date(Date.now() - SESSION_CONFIG.maxAge)
    
    // Update expired sessions in login history
    const result = await prisma.loginHistory.updateMany({
      where: {
        logoutAt: null,
        loginAt: {
          lt: expiredTime
        }
      },
      data: {
        logoutAt: new Date(),
        logoutReason: 'expired'
      }
    })

    // Clear expired session tokens from users
    await prisma.user.updateMany({
      where: {
        sessionExpiresAt: {
          lt: new Date()
        }
      },
      data: {
        sessionToken: null,
        sessionExpiresAt: null
      }
    })

    console.log(`Cleaned up ${result.count} expired sessions`)
    return result.count
  } catch (error) {
    console.error('Failed to cleanup expired sessions:', error)
    return 0
  }
}

/**
 * Get session statistics
 */
export async function getSessionStatistics(): Promise<{
  totalActiveSessions: number
  totalUsers: number
  averageSessionDuration: number
  topUserAgents: Array<{ userAgent: string; count: number }>
}> {
  try {
    const [activeSessions, totalUsers, recentSessions] = await Promise.all([
      prisma.loginHistory.count({
        where: {
          logoutAt: null,
          loginAt: {
            gte: new Date(Date.now() - SESSION_CONFIG.maxAge)
          }
        }
      }),
      prisma.user.count({
        where: { isActive: true }
      }),
      prisma.loginHistory.findMany({
        where: {
          logoutAt: { not: null },
          sessionDuration: { not: null }
        },
        select: {
          sessionDuration: true,
          userAgent: true
        },
        take: 1000
      })
    ])

    const averageSessionDuration = recentSessions.length > 0
      ? recentSessions.reduce((sum, s) => sum + (s.sessionDuration || 0), 0) / recentSessions.length
      : 0

    const userAgentCounts = recentSessions.reduce((acc, session) => {
      const ua = session.userAgent || 'Unknown'
      acc[ua] = (acc[ua] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const topUserAgents = Object.entries(userAgentCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([userAgent, count]) => ({ userAgent, count }))

    return {
      totalActiveSessions: activeSessions,
      totalUsers,
      averageSessionDuration,
      topUserAgents
    }
  } catch (error) {
    console.error('Failed to get session statistics:', error)
    return {
      totalActiveSessions: 0,
      totalUsers: 0,
      averageSessionDuration: 0,
      topUserAgents: []
    }
  }
}
