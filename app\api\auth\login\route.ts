import { NextRequest, NextResponse } from 'next/server'
import { signIn } from '@/lib/auth/config'
import { prisma } from '@/lib/prisma'
import { verifyPassword } from '@/lib/auth/password'
import { 
  handleSuccessfulLogin, 
  handleFailedLogin, 
  checkAccountLockout, 
  logSecurityEvent,
  getClientIP,
  getUserAgent,
  checkRateLimit
} from '@/lib/auth/security'
import { SecurityEventType, SecuritySeverity } from '@/lib/generated/prisma'
import { LoginCredentials, LoginResult, ApiResponse } from '@/lib/auth/types'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { username, password, twoFactorCode }: LoginCredentials = body

    // Validate input
    if (!username || !password) {
      return NextResponse.json({
        success: false,
        error: 'Username and password are required'
      } as ApiResponse, { status: 400 })
    }

    // Get client information
    const ipAddress = getClientIP(request)
    const userAgent = getUserAgent(request)

    // Check rate limiting
    const rateLimitKey = `login:${ipAddress}`
    const rateLimit = await checkRateLimit(rateLimitKey, 5, 15 * 60 * 1000) // 5 attempts per 15 minutes

    if (rateLimit.blocked) {
      await logSecurityEvent({
        eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
        severity: SecuritySeverity.HIGH,
        description: `Rate limit exceeded for IP: ${ipAddress}`,
        ipAddress,
        userAgent
      })

      return NextResponse.json({
        success: false,
        error: 'Too many login attempts. Please try again later.'
      } as ApiResponse, { status: 429 })
    }

    // Find user
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: username },
          { email: username }
        ]
      },
      include: {
        teacher: true
      }
    })

    if (!user) {
      // Log failed login attempt for unknown user
      await logSecurityEvent({
        eventType: SecurityEventType.LOGIN_FAILURE,
        severity: SecuritySeverity.MEDIUM,
        description: `Login attempt with unknown username: ${username}`,
        ipAddress,
        userAgent
      })

      return NextResponse.json({
        success: false,
        error: 'Invalid username or password'
      } as LoginResult, { status: 401 })
    }

    // Check if account is active
    if (!user.isActive) {
      await logSecurityEvent({
        userId: user.id,
        eventType: SecurityEventType.LOGIN_FAILURE,
        severity: SecuritySeverity.HIGH,
        description: 'Login attempt on inactive account',
        ipAddress,
        userAgent
      })

      return NextResponse.json({
        success: false,
        error: 'Account is inactive. Please contact administrator.'
      } as LoginResult, { status: 401 })
    }

    // Check account lockout
    const lockoutStatus = await checkAccountLockout(user.id)
    if (lockoutStatus.isLocked) {
      await logSecurityEvent({
        userId: user.id,
        eventType: SecurityEventType.LOGIN_FAILURE,
        severity: SecuritySeverity.HIGH,
        description: 'Login attempt on locked account',
        ipAddress,
        userAgent
      })

      return NextResponse.json({
        success: false,
        error: 'Account is temporarily locked due to multiple failed login attempts.',
        accountLocked: true,
        lockoutUntil: lockoutStatus.lockoutUntil
      } as LoginResult, { status: 423 })
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.passwordHash)
    
    if (!isValidPassword) {
      // Handle failed login
      const lockoutResult = await handleFailedLogin(user.id, ipAddress, userAgent)
      
      return NextResponse.json({
        success: false,
        error: 'Invalid username or password',
        accountLocked: lockoutResult.shouldLock,
        lockoutUntil: lockoutResult.lockoutUntil
      } as LoginResult, { status: 401 })
    }

    // Check two-factor authentication if enabled
    if (user.twoFactorEnabled && !twoFactorCode) {
      return NextResponse.json({
        success: false,
        error: 'Two-factor authentication code required',
        requiresTwoFactor: true
      } as LoginResult, { status: 200 })
    }

    if (user.twoFactorEnabled && twoFactorCode) {
      // Verify 2FA code (implementation would depend on your 2FA provider)
      // For now, we'll skip this implementation
      // const isValid2FA = await verify2FACode(user.twoFactorSecret, twoFactorCode)
      // if (!isValid2FA) {
      //   return NextResponse.json({
      //     success: false,
      //     error: 'Invalid two-factor authentication code'
      //   } as LoginResult, { status: 401 })
      // }
    }

    // Handle successful login
    await handleSuccessfulLogin(user.id, ipAddress, userAgent)

    // Create login history record
    await prisma.loginHistory.create({
      data: {
        userId: user.id,
        ipAddress,
        userAgent,
        success: true,
        deviceType: getDeviceType(userAgent),
        browser: getBrowser(userAgent),
        operatingSystem: getOperatingSystem(userAgent)
      }
    })

    // Parse permissions
    let permissions: string[] = []
    if (user.permissions) {
      try {
        permissions = JSON.parse(user.permissions)
      } catch (error) {
        console.error('Failed to parse user permissions:', error)
      }
    }

    const authUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      middleName: user.middleName,
      role: user.role,
      permissions,
      isActive: user.isActive,
      lastLoginAt: user.lastLoginAt,
      mustChangePassword: user.mustChangePassword,
      twoFactorEnabled: user.twoFactorEnabled,
      phoneNumber: user.phoneNumber,
      department: user.department,
      position: user.position
    }

    return NextResponse.json({
      success: true,
      user: authUser,
      mustChangePassword: user.mustChangePassword
    } as LoginResult, { status: 200 })

  } catch (error) {
    console.error('Login error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// Helper functions for device detection
function getDeviceType(userAgent: string): string {
  if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
    return 'mobile'
  } else if (/Tablet|iPad/.test(userAgent)) {
    return 'tablet'
  }
  return 'desktop'
}

function getBrowser(userAgent: string): string {
  if (userAgent.includes('Chrome')) return 'Chrome'
  if (userAgent.includes('Firefox')) return 'Firefox'
  if (userAgent.includes('Safari')) return 'Safari'
  if (userAgent.includes('Edge')) return 'Edge'
  return 'Unknown'
}

function getOperatingSystem(userAgent: string): string {
  if (userAgent.includes('Windows')) return 'Windows'
  if (userAgent.includes('Mac')) return 'macOS'
  if (userAgent.includes('Linux')) return 'Linux'
  if (userAgent.includes('Android')) return 'Android'
  if (userAgent.includes('iOS')) return 'iOS'
  return 'Unknown'
}
