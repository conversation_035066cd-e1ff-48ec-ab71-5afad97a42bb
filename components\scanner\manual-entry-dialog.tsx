"use client"

import { useState, useEffect, useMemo } from "react"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Student, AttendanceAction, ManualEntry } from "@/lib/types/scanner"
import { Search, User, KeyboardIcon, CheckCircle, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface ManualEntryDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  students: Student[]
  onSubmitEntry: (entry: ManualEntry) => void
  isLoading?: boolean
}

export function ManualEntryDialog({
  isOpen,
  onOpenChange,
  students,
  onSubmitEntry,
  isLoading = false
}: ManualEntryDialogProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [selectedAction, setSelectedAction] = useState<AttendanceAction>('present')
  const [reason, setReason] = useState("")

  // Filter students based on search query
  const filteredStudents = useMemo(() => {
    if (!searchQuery.trim()) return students.slice(0, 10) // Show first 10 by default
    
    const query = searchQuery.toLowerCase()
    return students.filter(student => 
      student.id.toLowerCase().includes(query) ||
      student.name.toLowerCase().includes(query) ||
      student.email.toLowerCase().includes(query) ||
      student.course.toLowerCase().includes(query)
    ).slice(0, 20) // Limit to 20 results
  }, [searchQuery, students])

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!isOpen) {
      setSearchQuery("")
      setSelectedStudent(null)
      setSelectedAction('present')
      setReason("")
    }
  }, [isOpen])

  const handleSubmit = () => {
    if (!selectedStudent) return

    const entry: ManualEntry = {
      studentId: selectedStudent.id,
      action: selectedAction,
      timestamp: new Date(),
      reason: reason.trim() || undefined
    }

    onSubmitEntry(entry)
    onOpenChange(false)
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const actionOptions: { value: AttendanceAction; label: string; description: string }[] = [
    { value: 'present', label: 'Present', description: 'Mark student as present' },
    { value: 'late', label: 'Late', description: 'Mark student as late arrival' },
    { value: 'absent', label: 'Absent', description: 'Mark student as absent' },
    { value: 'check-in', label: 'Check In', description: 'Manual check-in entry' },
    { value: 'check-out', label: 'Check Out', description: 'Manual check-out entry' }
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <KeyboardIcon className="h-5 w-5" />
            Manual Entry
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-6">
          {/* Student Search */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="search">Search Student</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Enter student ID, name, or email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                  autoFocus
                />
              </div>
            </div>

            {/* Student Results */}
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {filteredStudents.length > 0 ? (
                filteredStudents.map((student) => (
                  <Card
                    key={student.id}
                    className={cn(
                      "cursor-pointer transition-colors hover:bg-muted/50",
                      selectedStudent?.id === student.id && "ring-2 ring-primary bg-muted/50"
                    )}
                    onClick={() => setSelectedStudent(student)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={student.photo} alt={student.name} />
                          <AvatarFallback className="text-sm">
                            {getInitials(student.name)}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium truncate">{student.name}</h4>
                            <Badge variant="outline" className="text-xs">
                              {student.id}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground truncate">
                            {student.course} • {student.year}
                          </p>
                        </div>
                        
                        {selectedStudent?.id === student.id && (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : searchQuery.trim() ? (
                <div className="text-center py-8 text-muted-foreground">
                  <User className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No students found matching "{searchQuery}"</p>
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  <p className="text-sm">Start typing to search for students</p>
                </div>
              )}
            </div>
          </div>

          {/* Selected Student Info */}
          {selectedStudent && (
            <Card className="border-primary/20 bg-primary/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={selectedStudent.photo} alt={selectedStudent.name} />
                    <AvatarFallback>
                      {getInitials(selectedStudent.name)}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <h3 className="font-semibold">{selectedStudent.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {selectedStudent.id} • {selectedStudent.course} • {selectedStudent.year}
                    </p>
                  </div>
                  
                  <Badge variant={selectedStudent.status === 'Active' ? 'default' : 'secondary'}>
                    {selectedStudent.status}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Selection */}
          {selectedStudent && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Attendance Action</Label>
                <div className="grid grid-cols-2 gap-2">
                  {actionOptions.map((option) => (
                    <Button
                      key={option.value}
                      variant={selectedAction === option.value ? "default" : "outline"}
                      onClick={() => setSelectedAction(option.value)}
                      className="h-auto p-3 flex flex-col items-start"
                    >
                      <span className="font-medium">{option.label}</span>
                      <span className="text-xs opacity-75">{option.description}</span>
                    </Button>
                  ))}
                </div>
              </div>

              {/* Reason/Notes */}
              <div className="space-y-2">
                <Label htmlFor="reason">Reason (Optional)</Label>
                <Input
                  id="reason"
                  placeholder="Enter reason for manual entry..."
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                />
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={!selectedStudent || isLoading}
          >
            {isLoading ? (
              <>
                <AlertCircle className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Submit Entry
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
