"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "lucide-react"
import { format } from "date-fns"

interface AttendanceFiltersProps {
  onFiltersChange: (filters: AttendanceFilters) => void
  className?: string
}

export interface AttendanceFilters {
  dateRange: {
    from: Date
    to: Date
  }
  grade: string
  section: string
  course: string
  status: string
  searchQuery: string
}

export function AttendanceFilters({ onFiltersChange, className }: AttendanceFiltersProps) {
  const [filters, setFilters] = useState<AttendanceFilters>({
    dateRange: {
      from: new Date(),
      to: new Date()
    },
    grade: "all",
    section: "all",
    course: "all",
    status: "all",
    searchQuery: ""
  })

  const handleFilterChange = (key: keyof AttendanceFilters, value: any) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    onFiltersChange(newFilters)
  }

  const clearFilters = () => {
    const defaultFilters: AttendanceFilters = {
      dateRange: {
        from: new Date(),
        to: new Date()
      },
      grade: "all",
      section: "all",
      course: "all",
      status: "all",
      searchQuery: ""
    }
    setFilters(defaultFilters)
    onFiltersChange(defaultFilters)
  }

  const grades = [
    { value: "all", label: "All Grades" },
    { value: "7", label: "Grade 7" },
    { value: "8", label: "Grade 8" },
    { value: "9", label: "Grade 9" },
    { value: "10", label: "Grade 10" },
    { value: "11", label: "Grade 11" },
    { value: "12", label: "Grade 12" }
  ]

  const courses = [
    { value: "all", label: "All Courses" },
    { value: "junior-high", label: "Junior High School" },
    { value: "ict", label: "Information and Communications Technology" },
    { value: "abm", label: "Accountancy, Business and Management" },
    { value: "humss", label: "Humanities and Social Sciences" },
    { value: "stem", label: "Science, Technology, Engineering and Mathematics" }
  ]

  const statuses = [
    { value: "all", label: "All Status" },
    { value: "present", label: "Present" },
    { value: "late", label: "Late" },
    { value: "absent", label: "Absent" }
  ]

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">Filters</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search">Search Student</Label>
          <Input
            id="search"
            placeholder="Search by name or ID..."
            value={filters.searchQuery}
            onChange={(e) => handleFilterChange("searchQuery", e.target.value)}
          />
        </div>

        {/* Date Range */}
        <div className="space-y-2">
          <Label>Date Range</Label>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="justify-start text-left font-normal">
              <Calendar className="mr-2 h-4 w-4" />
              {format(filters.dateRange.from, "MMM dd, yyyy")}
            </Button>
            <span className="text-sm text-muted-foreground">to</span>
            <Button variant="outline" size="sm" className="justify-start text-left font-normal">
              <Calendar className="mr-2 h-4 w-4" />
              {format(filters.dateRange.to, "MMM dd, yyyy")}
            </Button>
          </div>
        </div>

        {/* Grade Filter */}
        <div className="space-y-2">
          <Label>Grade Level</Label>
          <Select value={filters.grade} onValueChange={(value) => handleFilterChange("grade", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select grade" />
            </SelectTrigger>
            <SelectContent>
              {grades.map((grade) => (
                <SelectItem key={grade.value} value={grade.value}>
                  {grade.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Course Filter */}
        <div className="space-y-2">
          <Label>Course/Track</Label>
          <Select value={filters.course} onValueChange={(value) => handleFilterChange("course", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select course" />
            </SelectTrigger>
            <SelectContent>
              {courses.map((course) => (
                <SelectItem key={course.value} value={course.value}>
                  {course.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <Label>Attendance Status</Label>
          <Select value={filters.status} onValueChange={(value) => handleFilterChange("status", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              {statuses.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Active Filters */}
        <div className="space-y-2">
          <Label>Active Filters</Label>
          <div className="flex flex-wrap gap-2">
            {filters.grade !== "all" && (
              <Badge variant="secondary">
                Grade: {grades.find(g => g.value === filters.grade)?.label}
              </Badge>
            )}
            {filters.course !== "all" && (
              <Badge variant="secondary">
                Course: {courses.find(c => c.value === filters.course)?.label}
              </Badge>
            )}
            {filters.status !== "all" && (
              <Badge variant="secondary">
                Status: {statuses.find(s => s.value === filters.status)?.label}
              </Badge>
            )}
            {filters.searchQuery && (
              <Badge variant="secondary">
                Search: {filters.searchQuery}
              </Badge>
            )}
          </div>
        </div>

        {/* Clear Filters */}
        <Button variant="outline" onClick={clearFilters} className="w-full">
          Clear All Filters
        </Button>
      </CardContent>
    </Card>
  )
}
