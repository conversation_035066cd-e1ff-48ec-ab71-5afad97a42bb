"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Cloud, 
  CloudRain, 
  Sun, 
  CloudSnow, 
  Wind, 
  Droplets, 
  Thermometer,
  Eye,
  Gauge
} from "lucide-react"
import { cn } from "@/lib/utils"

interface WeatherData {
  location: string
  temperature: number
  condition: string
  humidity: number
  windSpeed: number
  visibility: number
  pressure: number
  feelsLike: number
  uvIndex: number
  icon: string
  lastUpdated: string
}

// Fallback weather data for Tanauan, Leyte
const fallbackWeatherData: WeatherData = {
  location: "Tanauan, Leyte",
  temperature: 28,
  condition: "Partly Cloudy",
  humidity: 75,
  windSpeed: 12,
  visibility: 10,
  pressure: 1013,
  feelsLike: 32,
  uvIndex: 7,
  icon: "partly-cloudy",
  lastUpdated: "Loading..."
}

const weatherIcons = {
  "sunny": Sun,
  "partly-cloudy": Cloud,
  "cloudy": Cloud,
  "rainy": CloudRain,
  "stormy": CloudRain,
  "snowy": CloudSnow,
  "default": Sun
}

interface WeatherWidgetProps {
  className?: string
}

export function WeatherWidget({ className }: WeatherWidgetProps) {
  const [weather, setWeather] = useState<WeatherData>(fallbackWeatherData)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Initialize lastUpdated time on client side to prevent hydration mismatch
    setWeather(prev => ({
      ...prev,
      lastUpdated: new Date().toLocaleTimeString()
    }))

    // Simulate weather data updates every 30 minutes
    const interval = setInterval(() => {
      // In a real implementation, this would fetch from a weather API
      setWeather(prev => ({
        ...prev,
        temperature: Math.round(26 + Math.random() * 6), // 26-32°C range
        humidity: Math.round(65 + Math.random() * 20), // 65-85% range
        windSpeed: Math.round(8 + Math.random() * 8), // 8-16 km/h range
        lastUpdated: new Date().toLocaleTimeString()
      }))
    }, 30 * 60 * 1000) // 30 minutes

    return () => clearInterval(interval)
  }, [])

  const WeatherIcon = weatherIcons[weather.icon as keyof typeof weatherIcons] || weatherIcons.default

  const getTemperatureColor = (temp: number) => {
    if (temp >= 30) return "text-red-500"
    if (temp >= 25) return "text-orange-500"
    if (temp >= 20) return "text-yellow-500"
    return "text-blue-500"
  }

  const getUVIndexColor = (uv: number) => {
    if (uv >= 8) return "text-red-500"
    if (uv >= 6) return "text-orange-500"
    if (uv >= 3) return "text-yellow-500"
    return "text-green-500"
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center justify-between">
          <span>Weather</span>
          <Badge variant="outline" className="text-xs">
            {weather.location}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Main weather display */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <WeatherIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <div className={cn("text-3xl font-bold", getTemperatureColor(weather.temperature))}>
                {weather.temperature}°C
              </div>
              <div className="text-sm text-muted-foreground">
                {weather.condition}
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-muted-foreground">Feels like</div>
            <div className="text-lg font-semibold">
              {weather.feelsLike}°C
            </div>
          </div>
        </div>

        {/* Weather details grid */}
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="flex items-center space-x-2">
            <Droplets className="h-4 w-4 text-blue-500" />
            <div>
              <div className="font-medium">{weather.humidity}%</div>
              <div className="text-muted-foreground">Humidity</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Wind className="h-4 w-4 text-gray-500" />
            <div>
              <div className="font-medium">{weather.windSpeed} km/h</div>
              <div className="text-muted-foreground">Wind</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Eye className="h-4 w-4 text-green-500" />
            <div>
              <div className="font-medium">{weather.visibility} km</div>
              <div className="text-muted-foreground">Visibility</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Gauge className="h-4 w-4 text-purple-500" />
            <div>
              <div className="font-medium">{weather.pressure} hPa</div>
              <div className="text-muted-foreground">Pressure</div>
            </div>
          </div>
        </div>

        {/* UV Index */}
        <div className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
          <div className="flex items-center space-x-2">
            <Sun className="h-4 w-4 text-yellow-500" />
            <span className="text-sm font-medium">UV Index</span>
          </div>
          <Badge 
            variant="outline" 
            className={cn("font-bold", getUVIndexColor(weather.uvIndex))}
          >
            {weather.uvIndex}
          </Badge>
        </div>

        {/* Last updated */}
        <div className="text-xs text-muted-foreground text-center pt-2 border-t">
          Last updated: {weather.lastUpdated}
        </div>
      </CardContent>
    </Card>
  )
}

// Compact weather widget for smaller spaces
export function CompactWeatherWidget({ className }: WeatherWidgetProps) {
  const [weather] = useState<WeatherData>(fallbackWeatherData)
  const WeatherIcon = weatherIcons[weather.icon as keyof typeof weatherIcons] || weatherIcons.default

  return (
    <div className={cn("flex items-center space-x-3 p-3 rounded-lg bg-muted/50", className)}>
      <WeatherIcon className="h-6 w-6 text-blue-600" />
      <div>
        <div className="font-semibold">{weather.temperature}°C</div>
        <div className="text-xs text-muted-foreground">{weather.condition}</div>
      </div>
      <div className="text-xs text-muted-foreground">
        {weather.location}
      </div>
    </div>
  )
}
