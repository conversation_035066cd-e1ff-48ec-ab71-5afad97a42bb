"use client"

import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer
} from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

// Weekly trend chart
interface WeeklyTrendChartProps {
  data: Array<{
    day: string
    present: number
    late: number
    absent: number
  }>
}

export function WeeklyTrendChart({ data }: WeeklyTrendChartProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Weekly Attendance Trend</CardTitle>
        <CardDescription>Daily attendance patterns for this week</CardDescription>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="day" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Area
              type="monotone"
              dataKey="present"
              stackId="1"
              stroke="#22c55e"
              fill="#22c55e"
              fillOpacity={0.8}
            />
            <Area
              type="monotone"
              dataKey="late"
              stackId="1"
              stroke="#f59e0b"
              fill="#f59e0b"
              fillOpacity={0.8}
            />
            <Area
              type="monotone"
              dataKey="absent"
              stackId="1"
              stroke="#ef4444"
              fill="#ef4444"
              fillOpacity={0.8}
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

// Grade level breakdown chart
interface GradeBreakdownChartProps {
  data: Array<{
    grade: string
    total: number
    present: number
    late: number
    absent: number
  }>
}

export function GradeBreakdownChart({ data }: GradeBreakdownChartProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Grade Level Breakdown</CardTitle>
        <CardDescription>Attendance by grade level</CardDescription>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="grade" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="present" fill="#22c55e" name="Present" />
            <Bar dataKey="late" fill="#f59e0b" name="Late" />
            <Bar dataKey="absent" fill="#ef4444" name="Absent" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

// Attendance rate pie chart
interface AttendanceRatePieChartProps {
  present: number
  late: number
  absent: number
}

export function AttendanceRatePieChart({ present, late, absent }: AttendanceRatePieChartProps) {
  const data = [
    { name: "Present", value: present, color: "#22c55e" },
    { name: "Late", value: late, color: "#f59e0b" },
    { name: "Absent", value: absent, color: "#ef4444" }
  ]

  const total = present + late + absent

  return (
    <Card>
      <CardHeader>
        <CardTitle>Today&apos;s Attendance Distribution</CardTitle>
        <CardDescription>Current attendance breakdown</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <ResponsiveContainer width="60%" height={200}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={5}
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
          <div className="space-y-2">
            {data.map((entry, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: entry.color }}
                />
                <div className="text-sm">
                  <div className="font-medium">{entry.name}</div>
                  <div className="text-muted-foreground">
                    {entry.value} ({((entry.value / total) * 100).toFixed(1)}%)
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Monthly trend line chart
interface MonthlyTrendChartProps {
  data: Array<{
    date: string
    attendanceRate: number
    totalStudents: number
  }>
}

export function MonthlyTrendChart({ data }: MonthlyTrendChartProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Attendance Trend</CardTitle>
        <CardDescription>Attendance rate over the past month</CardDescription>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis domain={[80, 100]} />
            <Tooltip />
            <Line
              type="monotone"
              dataKey="attendanceRate"
              stroke="#3b82f6"
              strokeWidth={2}
              dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

// Attendance heat map component
interface AttendanceHeatMapProps {
  data: Array<{
    day: string
    hour: number
    count: number
  }>
}

export function AttendanceHeatMap({ data }: AttendanceHeatMapProps) {
  const days = ["Mon", "Tue", "Wed", "Thu", "Fri"]
  const hours = Array.from({ length: 10 }, (_, i) => i + 7) // 7 AM to 4 PM

  const getIntensity = (day: string, hour: number) => {
    const entry = data.find(d => d.day === day && d.hour === hour)
    return entry ? entry.count : 0
  }

  const maxCount = Math.max(...data.map(d => d.count))

  return (
    <Card>
      <CardHeader>
        <CardTitle>Attendance Heat Map</CardTitle>
        <CardDescription>Student check-in patterns by day and hour</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex space-x-1">
            <div className="w-12"></div>
            {hours.map(hour => (
              <div key={hour} className="w-8 text-xs text-center text-muted-foreground">
                {hour}
              </div>
            ))}
          </div>
          {days.map(day => (
            <div key={day} className="flex items-center space-x-1">
              <div className="w-12 text-xs text-muted-foreground">{day}</div>
              {hours.map(hour => {
                const count = getIntensity(day, hour)
                const intensity = count / maxCount
                return (
                  <div
                    key={`${day}-${hour}`}
                    className="w-8 h-6 rounded-sm border border-border"
                    style={{
                      backgroundColor: `rgba(34, 197, 94, ${intensity})`,
                    }}
                    title={`${day} ${hour}:00 - ${count} students`}
                  />
                )
              })}
            </div>
          ))}
        </div>
        <div className="flex items-center justify-between mt-4 text-xs text-muted-foreground">
          <span>Less</span>
          <div className="flex space-x-1">
            {[0, 0.2, 0.4, 0.6, 0.8, 1].map(intensity => (
              <div
                key={intensity}
                className="w-3 h-3 rounded-sm border border-border"
                style={{
                  backgroundColor: `rgba(34, 197, 94, ${intensity})`,
                }}
              />
            ))}
          </div>
          <span>More</span>
        </div>
      </CardContent>
    </Card>
  )
}
