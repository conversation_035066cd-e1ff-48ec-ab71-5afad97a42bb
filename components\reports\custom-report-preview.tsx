"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { CustomReport, ExportFormat } from "@/lib/types/reports"
import { ReportExportManager } from "@/lib/utils/export-utils"
import { 
  Users, 
  Download, 
  Printer, 
  BarChart3,
  Table as TableIcon,
  PieChart,
  TrendingUp,
  FileText
} from "lucide-react"
import { format } from "date-fns"

interface CustomReportPreviewProps {
  report: CustomReport
  onDownload?: (format: ExportFormat) => void
  onPrint?: () => void
  className?: string
}

// Mock data for preview
const mockData = [
  {
    "students.id": "STU001",
    "students.name": "<PERSON>",
    "students.grade": "7",
    "students.section": "A",
    "attendance.status": "Present",
    "attendance.date": "2025-01-02"
  },
  {
    "students.id": "STU002", 
    "students.name": "<PERSON>",
    "students.grade": "7",
    "students.section": "B",
    "attendance.status": "Late",
    "attendance.date": "2025-01-02"
  },
  {
    "students.id": "STU003",
    "students.name": "Ana Marie Reyes", 
    "students.grade": "8",
    "students.section": "A",
    "attendance.status": "Present",
    "attendance.date": "2025-01-02"
  },
  {
    "students.id": "STU004",
    "students.name": "Jose Miguel Rodriguez",
    "students.grade": "8", 
    "students.section": "B",
    "attendance.status": "Absent",
    "attendance.date": "2025-01-02"
  },
  {
    "students.id": "STU005",
    "students.name": "Princess Mae Garcia",
    "students.grade": "9",
    "students.section": "A", 
    "attendance.status": "Present",
    "attendance.date": "2025-01-02"
  }
]

export function CustomReportPreview({
  report,
  onDownload,
  onPrint,
  className
}: CustomReportPreviewProps) {
  const handleDownload = async (format: ExportFormat) => {
    try {
      const { blob, filename } = await ReportExportManager.exportReport(report, format)
      ReportExportManager.downloadFile(blob, filename)
      onDownload?.(format)
    } catch (error) {
      console.error('Export failed:', error)
    }
  }
  // Use mock data for preview, in real implementation this would come from report.data
  const data = report.data.length > 0 ? report.data : mockData.slice(0, Math.min(5, report.query.limit || 5))

  const getVisualizationIcon = () => {
    if (report.visualization?.type === 'chart') {
      switch (report.visualization.chartType) {
        case 'bar': return <BarChart3 className="h-6 w-6 text-blue-600" />
        case 'pie': return <PieChart className="h-6 w-6 text-green-600" />
        case 'line': return <TrendingUp className="h-6 w-6 text-purple-600" />
        default: return <BarChart3 className="h-6 w-6 text-blue-600" />
      }
    }
    return <TableIcon className="h-6 w-6 text-gray-600" />
  }

  const formatFieldName = (fieldId: string) => {
    const parts = fieldId.split('.')
    if (parts.length === 2) {
      const [source, field] = parts
      return field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1')
    }
    return fieldId
  }

  const formatCellValue = (value: any, fieldId: string) => {
    if (value === null || value === undefined) return '-'
    
    // Format dates
    if (fieldId.includes('date') || fieldId.includes('Date')) {
      try {
        return format(new Date(value), "MMM dd, yyyy")
      } catch {
        return value
      }
    }
    
    // Format status with badges
    if (fieldId.includes('status') || fieldId.includes('Status')) {
      const variant = value === 'Present' ? 'default' : 
                    value === 'Late' ? 'secondary' : 
                    value === 'Absent' ? 'destructive' : 'outline'
      return <Badge variant={variant as any}>{value}</Badge>
    }
    
    return value
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gray-100 rounded-lg">
            {getVisualizationIcon()}
          </div>
          <div>
            <h2 className="text-xl font-semibold">{report.name || "Custom Report"}</h2>
            <p className="text-sm text-muted-foreground">
              {report.description || "Custom report preview"}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => handleDownload('CSV')}>
            <Download className="mr-2 h-4 w-4" />
            CSV
          </Button>
          <Button variant="outline" onClick={() => handleDownload('EXCEL')}>
            <Download className="mr-2 h-4 w-4" />
            Excel
          </Button>
          <Button variant="outline" onClick={() => handleDownload('PDF')}>
            <Download className="mr-2 h-4 w-4" />
            PDF
          </Button>
          <Button onClick={onPrint}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      {/* Report Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Report Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <div className="text-sm font-medium text-muted-foreground">Data Sources</div>
              <div className="text-lg font-semibold">{report.query.tables.length}</div>
              <div className="text-xs text-muted-foreground">
                {report.query.tables.join(', ')}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Fields</div>
              <div className="text-lg font-semibold">{report.query.fields.length}</div>
              <div className="text-xs text-muted-foreground">Selected columns</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Filters</div>
              <div className="text-lg font-semibold">{report.query.conditions.length}</div>
              <div className="text-xs text-muted-foreground">Applied conditions</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Records</div>
              <div className="text-lg font-semibold">{data.length}</div>
              <div className="text-xs text-muted-foreground">
                {data.length < (report.query.limit || 100) ? 'All records' : `Limited to ${report.query.limit}`}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Query Details */}
      {(report.query.conditions.length > 0 || report.query.groupBy || report.query.orderBy) && (
        <Card>
          <CardHeader>
            <CardTitle>Query Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {report.query.conditions.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2">Applied Filters:</h4>
                <div className="space-y-1">
                  {report.query.conditions.map((condition, index) => (
                    <div key={index} className="text-sm text-muted-foreground">
                      • {formatFieldName(condition.field)} {condition.operator.replace('_', ' ')} "{condition.value}"
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {report.query.groupBy && report.query.groupBy.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2">Grouped By:</h4>
                <div className="text-sm text-muted-foreground">
                  {report.query.groupBy.map(formatFieldName).join(', ')}
                </div>
              </div>
            )}
            
            {report.query.orderBy && report.query.orderBy.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2">Ordered By:</h4>
                <div className="text-sm text-muted-foreground">
                  {report.query.orderBy.map(formatFieldName).join(', ')}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Data Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Data Preview</CardTitle>
          <div className="text-sm text-muted-foreground">
            Showing {data.length} of {data.length} records
            {data.length >= (report.query.limit || 100) && " (limited)"}
          </div>
        </CardHeader>
        <CardContent>
          {data.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <TableIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No data available</p>
              <p className="text-sm">Check your filters and data sources</p>
            </div>
          ) : (
            <div className="rounded-md border overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    {report.query.fields.map((fieldId) => (
                      <TableHead key={fieldId}>
                        {formatFieldName(fieldId)}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.map((row, index) => (
                    <TableRow key={index}>
                      {report.query.fields.map((fieldId) => (
                        <TableCell key={fieldId}>
                          {formatCellValue(row[fieldId], fieldId)}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Visualization Preview */}
      {report.visualization?.type === 'chart' && (
        <Card>
          <CardHeader>
            <CardTitle>Visualization Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12 text-muted-foreground">
              {getVisualizationIcon()}
              <p className="mt-4">Chart visualization preview</p>
              <p className="text-sm">
                {report.visualization.chartType?.toUpperCase()} chart with {report.visualization.xAxis} vs {report.visualization.yAxis}
              </p>
              <div className="mt-4 text-xs text-muted-foreground">
                Chart rendering would be implemented with a charting library like Chart.js or Recharts
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Footer */}
      <div className="text-center text-xs text-muted-foreground pt-4 border-t">
        <p>Custom Report - Generated on {format(new Date(report.generatedAt), "MMMM dd, yyyy 'at' HH:mm")}</p>
      </div>
    </div>
  )
}
