"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { mockGeneratedReports, mockReportAnalytics } from "@/lib/data/reports-mock-data"
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  FileText, 
  Download, 
  Clock,
  Calendar,
  Eye,
  Share
} from "lucide-react"
import { format, subDays, subMonths } from "date-fns"
import { useState } from "react"

interface ReportAnalyticsProps {
  className?: string
}

export function ReportAnalytics({ className }: ReportAnalyticsProps) {
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d" | "1y">("30d")

  // Mock analytics data based on time range
  const getAnalyticsData = () => {
    const baseData = mockReportAnalytics
    
    // Simulate different data based on time range
    const multipliers = {
      "7d": 0.2,
      "30d": 1,
      "90d": 3,
      "1y": 12
    }
    
    const multiplier = multipliers[timeRange]
    
    return {
      ...baseData,
      totalReports: Math.round(baseData.totalReports * multiplier),
      totalDownloads: Math.round(baseData.totalDownloads * multiplier),
      uniqueUsers: Math.round(baseData.uniqueUsers * multiplier * 0.8),
      averageGenerationTime: baseData.averageGenerationTime,
      reportTypeDistribution: baseData.reportTypeDistribution.map(item => ({
        ...item,
        count: Math.round(item.count * multiplier)
      })),
      dailyActivity: baseData.dailyActivity.map(item => ({
        ...item,
        reports: Math.round(item.reports * multiplier * 0.1),
        downloads: Math.round(item.downloads * multiplier * 0.1)
      }))
    }
  }

  const analytics = getAnalyticsData()

  // Calculate trends (mock data)
  const getTrendPercentage = (current: number, previous: number) => {
    if (previous === 0) return 0
    return ((current - previous) / previous) * 100
  }

  const reportsTrend = getTrendPercentage(analytics.totalReports, analytics.totalReports * 0.8)
  const downloadsTrend = getTrendPercentage(analytics.totalDownloads, analytics.totalDownloads * 0.9)
  const usersTrend = getTrendPercentage(analytics.uniqueUsers, analytics.uniqueUsers * 0.85)

  const formatTrend = (trend: number) => {
    const isPositive = trend >= 0
    return {
      value: Math.abs(trend).toFixed(1),
      isPositive,
      icon: isPositive ? TrendingUp : TrendingDown,
      color: isPositive ? "text-green-600" : "text-red-600"
    }
  }

  const getTimeRangeLabel = () => {
    const labels = {
      "7d": "Last 7 days",
      "30d": "Last 30 days", 
      "90d": "Last 90 days",
      "1y": "Last year"
    }
    return labels[timeRange]
  }

  // Most popular reports
  const popularReports = mockGeneratedReports
    .sort((a, b) => b.downloadCount - a.downloadCount)
    .slice(0, 5)

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="h-6 w-6" />
            Report Analytics
          </h2>
          <p className="text-muted-foreground">
            Track report usage, performance, and trends
          </p>
        </div>
        <Select value={timeRange} onValueChange={(value) => setTimeRange(value as typeof timeRange)}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
            <SelectItem value="1y">Last year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Reports</p>
                <p className="text-2xl font-bold">{analytics.totalReports.toLocaleString()}</p>
                <div className="flex items-center gap-1 text-xs">
                  {(() => {
                    const trend = formatTrend(reportsTrend)
                    const Icon = trend.icon
                    return (
                      <>
                        <Icon className={`h-3 w-3 ${trend.color}`} />
                        <span className={trend.color}>
                          {trend.value}% vs previous period
                        </span>
                      </>
                    )
                  })()}
                </div>
              </div>
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Downloads</p>
                <p className="text-2xl font-bold">{analytics.totalDownloads.toLocaleString()}</p>
                <div className="flex items-center gap-1 text-xs">
                  {(() => {
                    const trend = formatTrend(downloadsTrend)
                    const Icon = trend.icon
                    return (
                      <>
                        <Icon className={`h-3 w-3 ${trend.color}`} />
                        <span className={trend.color}>
                          {trend.value}% vs previous period
                        </span>
                      </>
                    )
                  })()}
                </div>
              </div>
              <Download className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Users</p>
                <p className="text-2xl font-bold">{analytics.uniqueUsers}</p>
                <div className="flex items-center gap-1 text-xs">
                  {(() => {
                    const trend = formatTrend(usersTrend)
                    const Icon = trend.icon
                    return (
                      <>
                        <Icon className={`h-3 w-3 ${trend.color}`} />
                        <span className={trend.color}>
                          {trend.value}% vs previous period
                        </span>
                      </>
                    )
                  })()}
                </div>
              </div>
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Generation Time</p>
                <p className="text-2xl font-bold">{analytics.averageGenerationTime}s</p>
                <div className="flex items-center gap-1 text-xs text-green-600">
                  <TrendingDown className="h-3 w-3" />
                  <span>12% faster</span>
                </div>
              </div>
              <Clock className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Report Type Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Report Type Distribution</CardTitle>
            <CardDescription>
              Breakdown of reports by type for {getTimeRangeLabel().toLowerCase()}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.reportTypeDistribution.map((item) => {
                const percentage = analytics.totalReports > 0 
                  ? (item.count / analytics.totalReports) * 100 
                  : 0
                
                return (
                  <div key={item.type} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        item.type === 'SF2' ? 'bg-blue-500' :
                        item.type === 'SF4' ? 'bg-green-500' :
                        item.type === 'DAILY' ? 'bg-purple-500' :
                        item.type === 'WEEKLY' ? 'bg-orange-500' :
                        item.type === 'MONTHLY' ? 'bg-pink-500' :
                        'bg-gray-500'
                      }`} />
                      <span className="text-sm font-medium">{item.type}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        {item.count.toLocaleString()}
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {percentage.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Most Popular Reports</CardTitle>
            <CardDescription>
              Reports with the highest download counts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {popularReports.map((report, index) => (
                <div key={report.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-muted text-xs font-medium">
                      {index + 1}
                    </div>
                    <div>
                      <div className="text-sm font-medium">{report.config.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {format(new Date(report.generatedAt), "MMM dd, yyyy")}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Download className="h-3 w-3 text-muted-foreground" />
                    <span className="text-sm">{report.downloadCount}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Daily Activity Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Daily Activity</CardTitle>
          <CardDescription>
            Report generation and download activity over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Simple bar chart representation */}
            <div className="grid grid-cols-7 gap-2">
              {analytics.dailyActivity.slice(-7).map((day, index) => {
                const maxValue = Math.max(...analytics.dailyActivity.map(d => d.reports + d.downloads))
                const totalActivity = day.reports + day.downloads
                const height = maxValue > 0 ? (totalActivity / maxValue) * 100 : 0
                
                return (
                  <div key={index} className="flex flex-col items-center gap-2">
                    <div className="w-full bg-muted rounded-t" style={{ height: '100px' }}>
                      <div 
                        className="w-full bg-primary rounded-t transition-all"
                        style={{ height: `${height}%`, marginTop: `${100 - height}%` }}
                      />
                    </div>
                    <div className="text-xs text-center">
                      <div className="font-medium">{format(new Date(day.date), "MMM dd")}</div>
                      <div className="text-muted-foreground">{totalActivity}</div>
                    </div>
                  </div>
                )
              })}
            </div>
            
            <div className="flex items-center justify-center gap-6 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-primary rounded" />
                <span>Total Activity</span>
              </div>
              <div className="text-muted-foreground">
                Last 7 days activity
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Insights</CardTitle>
          <CardDescription>
            Key insights and recommendations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium">Key Insights</h4>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2" />
                  <div>
                    <p className="text-sm font-medium">SF2 reports are most popular</p>
                    <p className="text-xs text-muted-foreground">
                      {analytics.reportTypeDistribution.find(r => r.type === 'SF2')?.count || 0} generated this period
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                  <div>
                    <p className="text-sm font-medium">Peak usage on weekdays</p>
                    <p className="text-xs text-muted-foreground">
                      Monday-Friday show highest activity
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2" />
                  <div>
                    <p className="text-sm font-medium">Generation time improved</p>
                    <p className="text-xs text-muted-foreground">
                      12% faster than previous period
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium">Recommendations</h4>
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm font-medium text-blue-800">Optimize SF2 templates</p>
                  <p className="text-xs text-blue-600">
                    Consider creating more SF2 template variations
                  </p>
                </div>
                
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="text-sm font-medium text-green-800">Schedule bulk reports</p>
                  <p className="text-xs text-green-600">
                    Use bulk generation for recurring reports
                  </p>
                </div>
                
                <div className="p-3 bg-purple-50 rounded-lg">
                  <p className="text-sm font-medium text-purple-800">Archive old reports</p>
                  <p className="text-xs text-purple-600">
                    Set up automatic archival for reports older than 90 days
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
