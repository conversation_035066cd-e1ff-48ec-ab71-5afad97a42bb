"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { AttendanceFilters, type AttendanceFilters as FilterType } from "@/components/attendance/attendance-filters"
import { AttendanceGrid } from "@/components/attendance/attendance-grid"
import { AttendanceStats } from "@/components/dashboard/attendance-stats"
import { WeeklyTrendChart, GradeBreakdownChart } from "@/components/dashboard/attendance-charts"
import { mockDashboardStats, mockAttendanceRecords } from "@/lib/data/mock-data"
import { exportToCSV, printDailyReport } from "@/lib/utils/export-utils"
import {
  Calendar,
  QrCode,
  Download,
  Printer,
  Users,
  <PERSON><PERSON><PERSON>,
  Al<PERSON>Triangle
} from "lucide-react"
import { format } from "date-fns"

// Enhanced attendance record type
interface AttendanceRecord {
  id: string
  studentId: string
  studentName: string
  course: string
  grade: string
  section: string
  checkIn?: string
  checkOut?: string
  date: string
  status: "Present" | "Late" | "Absent"
  type: "gate" | "subject"
  subject?: string
  period?: string
  reason?: string
  photo?: string
}

// Mock enhanced attendance data
const mockEnhancedAttendance: AttendanceRecord[] = [
  {
    id: "ATT001",
    studentId: "STU001",
    studentName: "Maria Cristina Santos",
    course: "Junior High School",
    grade: "7",
    section: "Grade 7-A",
    checkIn: "7:45 AM",
    checkOut: "4:30 PM",
    date: new Date().toISOString().split('T')[0],
    status: "Present",
    type: "gate",
    photo: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
  },
  {
    id: "ATT002",
    studentId: "STU002",
    studentName: "Juan Carlos Dela Cruz",
    course: "Junior High School",
    grade: "7",
    section: "Grade 7-B",
    checkIn: "7:50 AM",
    checkOut: "4:25 PM",
    date: new Date().toISOString().split('T')[0],
    status: "Present",
    type: "gate",
    photo: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
  },
  {
    id: "ATT003",
    studentId: "STU003",
    studentName: "Ana Marie Reyes",
    course: "Junior High School",
    grade: "7",
    section: "Grade 7-A",
    checkIn: "8:15 AM",
    checkOut: "4:35 PM",
    date: new Date().toISOString().split('T')[0],
    status: "Late",
    type: "gate",
    reason: "Traffic delay",
    photo: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"
  },
  {
    id: "ATT004",
    studentId: "STU004",
    studentName: "Jose Miguel Rodriguez",
    course: "Junior High School",
    grade: "8",
    section: "Grade 8-A",
    date: new Date().toISOString().split('T')[0],
    status: "Absent",
    type: "subject",
    subject: "Mathematics",
    period: "1st Period",
    reason: "Sick leave",
    photo: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
  }
]

export default function AttendancePage() {
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>(mockEnhancedAttendance)
  const [filteredRecords, setFilteredRecords] = useState<AttendanceRecord[]>(mockEnhancedAttendance)
  const [filters, setFilters] = useState<FilterType>({
    dateRange: { from: new Date(), to: new Date() },
    grade: "all",
    section: "all",
    course: "all",
    status: "all",
    searchQuery: ""
  })

  // Filter records based on current filters
  useEffect(() => {
    let filtered = attendanceRecords

    // Apply search filter
    if (filters.searchQuery) {
      filtered = filtered.filter(record =>
        record.studentName.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
        record.studentId.toLowerCase().includes(filters.searchQuery.toLowerCase())
      )
    }

    // Apply grade filter
    if (filters.grade !== "all") {
      filtered = filtered.filter(record => record.grade === filters.grade)
    }

    // Apply status filter
    if (filters.status !== "all") {
      filtered = filtered.filter(record => record.status.toLowerCase() === filters.status)
    }

    setFilteredRecords(filtered)
  }, [filters, attendanceRecords])

  const handleFiltersChange = (newFilters: FilterType) => {
    setFilters(newFilters)
  }

  const handleUpdateRecord = (recordId: string, updates: Partial<AttendanceRecord>) => {
    setAttendanceRecords(prev =>
      prev.map(record =>
        record.id === recordId ? { ...record, ...updates } : record
      )
    )
  }

  const handleExportData = () => {
    exportToCSV(filteredRecords, `attendance-report-${format(new Date(), "yyyy-MM-dd")}.csv`)
  }

  const handlePrintReport = () => {
    printDailyReport(filteredRecords, new Date())
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Attendance Management</h1>
          <p className="text-muted-foreground">
            Monitor, track, and manage student attendance records
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExportData}>
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
          <Button variant="outline" onClick={handlePrintReport}>
            <Printer className="mr-2 h-4 w-4" />
            Print Report
          </Button>
          <Button>
            <QrCode className="mr-2 h-4 w-4" />
            Open Scanner
          </Button>
        </div>
      </div>

      {/* Statistics Overview */}
      <AttendanceStats
        totalStudents={mockDashboardStats.totalStudents}
        presentToday={mockDashboardStats.presentToday}
        lateToday={mockDashboardStats.lateToday}
        absentToday={mockDashboardStats.absentToday}
        attendanceRate={mockDashboardStats.attendanceRate}
      />

      {/* Main Content */}
      <div className="grid gap-6 lg:grid-cols-4">
        {/* Filters Sidebar */}
        <div className="lg:col-span-1">
          <AttendanceFilters onFiltersChange={handleFiltersChange} />
        </div>

        {/* Main Content Area */}
        <div className="lg:col-span-3 space-y-6">
          <Tabs defaultValue="daily" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="daily">Daily View</TabsTrigger>
              <TabsTrigger value="subject">By Subject</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="reports">Reports</TabsTrigger>
            </TabsList>

            <TabsContent value="daily" className="space-y-4">
              <AttendanceGrid
                records={filteredRecords}
                onUpdateRecord={handleUpdateRecord}
              />
            </TabsContent>

            <TabsContent value="subject" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Subject-wise Attendance</CardTitle>
                  <CardDescription>Track attendance by subject and period</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {["Mathematics", "Science", "English", "Filipino", "Social Studies", "PE"].map((subject) => (
                      <Card key={subject}>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base">{subject}</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Present:</span>
                              <span className="font-medium text-green-600">85%</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span>Late:</span>
                              <span className="font-medium text-yellow-600">8%</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span>Absent:</span>
                              <span className="font-medium text-red-600">7%</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <WeeklyTrendChart data={mockDashboardStats.weeklyTrend} />
                <GradeBreakdownChart data={mockDashboardStats.gradeBreakdown} />
              </div>
            </TabsContent>

            <TabsContent value="reports" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Generate Reports</CardTitle>
                  <CardDescription>Create and download attendance reports</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <Button variant="outline" className="h-20 flex-col">
                      <Calendar className="h-6 w-6 mb-2" />
                      Daily Report
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <Users className="h-6 w-6 mb-2" />
                      Student Summary
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <BookOpen className="h-6 w-6 mb-2" />
                      Subject Report
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <AlertTriangle className="h-6 w-6 mb-2" />
                      Absence Report
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
