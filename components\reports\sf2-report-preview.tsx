"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Separator } from "@/components/ui/separator"
import { SF2Report, ExportFormat } from "@/lib/types/reports"
import { ReportExportManager } from "@/lib/utils/export-utils"
import { 
  FileText, 
  Download, 
  Printer, 
  School,
  Calendar,
  User,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle
} from "lucide-react"
import { format } from "date-fns"

interface SF2ReportPreviewProps {
  report: SF2Report
  onDownload?: (format: ExportFormat) => void
  onPrint?: () => void
  className?: string
}

export function SF2ReportPreview({
  report,
  onDownload,
  onPrint,
  className
}: SF2ReportPreviewProps) {
  const handleDownload = async (format: ExportFormat) => {
    try {
      const { blob, filename } = await ReportExportManager.exportReport(report, format)
      ReportExportManager.downloadFile(blob, filename)
      onDownload?.(format)
    } catch (error) {
      console.error('Export failed:', error)
    }
  }
  const getAttendanceIcon = (status: string) => {
    switch (status) {
      case 'P':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'L':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'A':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'E':
        return <CheckCircle className="h-4 w-4 text-blue-600" />
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getAttendanceLabel = (status: string) => {
    switch (status) {
      case 'P': return 'Present'
      case 'L': return 'Late'
      case 'A': return 'Absent'
      case 'E': return 'Excused'
      default: return 'Unknown'
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <FileText className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold">SF2 Report Preview</h2>
            <p className="text-sm text-muted-foreground">
              Daily Attendance Report - {format(new Date(report.date), "MMMM dd, yyyy")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => handleDownload('EXCEL')}>
            <Download className="mr-2 h-4 w-4" />
            Excel
          </Button>
          <Button variant="outline" onClick={() => handleDownload('PDF')}>
            <Download className="mr-2 h-4 w-4" />
            PDF
          </Button>
          <Button variant="outline" onClick={() => handleDownload('CSV')}>
            <Download className="mr-2 h-4 w-4" />
            CSV
          </Button>
          <Button onClick={onPrint}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      {/* SF2 Form Preview */}
      <Card className="print:shadow-none print:border-none">
        <CardContent className="p-8 space-y-6">
          {/* Official Header */}
          <div className="text-center space-y-2">
            <h1 className="text-lg font-bold">Republic of the Philippines</h1>
            <h2 className="text-base font-semibold">Department of Education</h2>
            <h3 className="text-base font-semibold">{report.schoolInfo.region}</h3>
            <h4 className="text-base font-semibold">{report.schoolInfo.division}</h4>
            <h5 className="text-base font-semibold">{report.schoolInfo.schoolName}</h5>
            <div className="pt-4">
              <h2 className="text-xl font-bold">SCHOOL FORM 2 (SF2)</h2>
              <h3 className="text-lg font-semibold">DAILY ATTENDANCE REPORT OF LEARNERS</h3>
            </div>
          </div>

          <Separator />

          {/* School and Class Information */}
          <div className="grid grid-cols-2 gap-8">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="font-medium">School:</span>
                <span className="underline">{report.schoolInfo.schoolName}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">School ID:</span>
                <span className="underline">{report.schoolInfo.schoolId}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Division:</span>
                <span className="underline">{report.schoolInfo.division}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Region:</span>
                <span className="underline">{report.schoolInfo.region}</span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="font-medium">Grade & Section:</span>
                <span className="underline">Grade {report.grade} - {report.section}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Subject:</span>
                <span className="underline">{report.subject || 'All Subjects'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Teacher:</span>
                <span className="underline">{report.teacher.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Date:</span>
                <span className="underline">{format(new Date(report.date), "MMMM dd, yyyy")}</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Attendance Table */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">DAILY ATTENDANCE</h3>
            
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-12 text-center border-r">No.</TableHead>
                    <TableHead className="border-r">LEARNER'S NAME</TableHead>
                    <TableHead className="w-24 text-center border-r">ATTENDANCE</TableHead>
                    <TableHead className="w-32 text-center border-r">STATUS</TableHead>
                    <TableHead className="text-center">REMARKS</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {report.students.map((student, index) => (
                    <TableRow key={student.studentId} className="border-b">
                      <TableCell className="text-center border-r font-medium">
                        {index + 1}
                      </TableCell>
                      <TableCell className="border-r">
                        {student.studentName}
                      </TableCell>
                      <TableCell className="text-center border-r">
                        <div className="flex items-center justify-center">
                          {getAttendanceIcon(student.dailyStatus)}
                        </div>
                      </TableCell>
                      <TableCell className="text-center border-r">
                        <Badge 
                          variant={
                            student.dailyStatus === 'P' ? 'default' :
                            student.dailyStatus === 'L' ? 'secondary' :
                            student.dailyStatus === 'E' ? 'outline' : 'destructive'
                          }
                          className="text-xs"
                        >
                          {getAttendanceLabel(student.dailyStatus)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-center text-sm">
                        {student.remarks || '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>

          <Separator />

          {/* Summary Statistics */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">ATTENDANCE SUMMARY</h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{report.summary.totalStudents}</div>
                <div className="text-sm text-muted-foreground">Total Students</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-green-600">{report.summary.presentCount}</div>
                <div className="text-sm text-muted-foreground">Present</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">{report.summary.lateCount}</div>
                <div className="text-sm text-muted-foreground">Late</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-red-600">{report.summary.absentCount}</div>
                <div className="text-sm text-muted-foreground">Absent</div>
              </div>
            </div>

            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-xl font-bold text-blue-800">
                {report.summary.attendanceRate.toFixed(1)}%
              </div>
              <div className="text-sm text-blue-600">Overall Attendance Rate</div>
            </div>
          </div>

          <Separator />

          {/* Signature Section */}
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="text-center">
                  <div className="border-b border-black w-48 mx-auto mb-2 h-12"></div>
                  <div className="text-sm font-medium">Teacher's Signature</div>
                  <div className="text-xs text-muted-foreground">{report.teacher.name}</div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="text-center">
                  <div className="border-b border-black w-48 mx-auto mb-2 h-12"></div>
                  <div className="text-sm font-medium">Date</div>
                  <div className="text-xs text-muted-foreground">
                    {report.teacher.dateChecked ? format(new Date(report.teacher.dateChecked), "MMMM dd, yyyy") : ''}
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <div className="border-b border-black w-64 mx-auto mb-2 h-12"></div>
              <div className="text-sm font-medium">Principal's Signature</div>
              <div className="text-xs text-muted-foreground">{report.schoolInfo.principalName}</div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center text-xs text-muted-foreground pt-4 border-t">
            <p>SF2 - Daily Attendance Report of Learners</p>
            <p>Generated on {format(new Date(report.generatedAt), "MMMM dd, yyyy 'at' HH:mm")}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
