import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { prisma } from '@/lib/prisma'
import { hashPassword, generateTemporaryPassword } from '@/lib/auth/password'
import { logAuditEntry, hasPermission, getClientIP, getUserAgent } from '@/lib/auth/security'
import { ApiResponse, UpdateUserRequest, AuthUser } from '@/lib/auth/types'

// GET /api/auth/users/[id] - Get specific user (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions (users can view their own profile, admins can view any)
    const isOwnProfile = session.user.id === params.id
    if (!isOwnProfile && !hasPermission(session.user.role, session.user.permissions, 'users.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        username: true,
        email: true,
        firstName: true,
        lastName: true,
        middleName: true,
        role: true,
        permissions: true,
        isActive: true,
        lastLoginAt: true,
        mustChangePassword: true,
        twoFactorEnabled: true,
        phoneNumber: true,
        department: true,
        position: true,
        failedLoginAttempts: true,
        lockedUntil: true,
        passwordChangedAt: true,
        createdAt: true,
        updatedAt: true
      }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      } as ApiResponse, { status: 404 })
    }

    // Parse permissions
    let permissions: string[] = []
    if (user.permissions) {
      try {
        permissions = JSON.parse(user.permissions)
      } catch (error) {
        console.error('Failed to parse user permissions:', error)
      }
    }

    const authUser: AuthUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      middleName: user.middleName,
      role: user.role,
      permissions,
      isActive: user.isActive,
      lastLoginAt: user.lastLoginAt,
      mustChangePassword: user.mustChangePassword,
      twoFactorEnabled: user.twoFactorEnabled,
      phoneNumber: user.phoneNumber,
      department: user.department,
      position: user.position
    }

    return NextResponse.json({
      success: true,
      data: authUser
    } as ApiResponse<AuthUser>, { status: 200 })

  } catch (error) {
    console.error('Get user error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// PUT /api/auth/users/[id] - Update user (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'users.update')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const body = await request.json()
    const updateData: UpdateUserRequest = body

    // Get current user data for audit log
    const currentUser = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!currentUser) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      } as ApiResponse, { status: 404 })
    }

    // Prevent self-deactivation
    if (session.user.id === params.id && updateData.isActive === false) {
      return NextResponse.json({
        success: false,
        error: 'Cannot deactivate your own account'
      } as ApiResponse, { status: 400 })
    }

    // Validate email uniqueness if being updated
    if (updateData.email && updateData.email !== currentUser.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email: updateData.email }
      })

      if (existingUser) {
        return NextResponse.json({
          success: false,
          error: 'Email address is already in use'
        } as ApiResponse, { status: 400 })
      }
    }

    // Prepare update data
    const updateFields: any = {}
    
    if (updateData.firstName) updateFields.firstName = updateData.firstName
    if (updateData.lastName) updateFields.lastName = updateData.lastName
    if (updateData.middleName !== undefined) updateFields.middleName = updateData.middleName
    if (updateData.email) updateFields.email = updateData.email
    if (updateData.phoneNumber !== undefined) updateFields.phoneNumber = updateData.phoneNumber
    if (updateData.department !== undefined) updateFields.department = updateData.department
    if (updateData.position !== undefined) updateFields.position = updateData.position
    if (updateData.role) updateFields.role = updateData.role
    if (updateData.permissions) updateFields.permissions = JSON.stringify(updateData.permissions)
    if (updateData.isActive !== undefined) updateFields.isActive = updateData.isActive

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: params.id },
      data: updateFields
    })

    // Log audit entry
    await logAuditEntry({
      userId: session.user.id,
      action: 'UPDATE_USER',
      entityType: 'User',
      entityId: params.id,
      oldValues: {
        firstName: currentUser.firstName,
        lastName: currentUser.lastName,
        middleName: currentUser.middleName,
        email: currentUser.email,
        phoneNumber: currentUser.phoneNumber,
        department: currentUser.department,
        position: currentUser.position,
        role: currentUser.role,
        isActive: currentUser.isActive
      },
      newValues: updateFields,
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request)
    })

    return NextResponse.json({
      success: true,
      message: 'User updated successfully'
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Update user error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// DELETE /api/auth/users/[id] - Delete user (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'users.delete')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    // Prevent self-deletion
    if (session.user.id === params.id) {
      return NextResponse.json({
        success: false,
        error: 'Cannot delete your own account'
      } as ApiResponse, { status: 400 })
    }

    // Get user data for audit log
    const user = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      } as ApiResponse, { status: 404 })
    }

    // Soft delete by deactivating instead of hard delete
    await prisma.user.update({
      where: { id: params.id },
      data: {
        isActive: false,
        email: `deleted_${Date.now()}_${user.email}`, // Prevent email conflicts
        username: `deleted_${Date.now()}_${user.username}` // Prevent username conflicts
      }
    })

    // Log audit entry
    await logAuditEntry({
      userId: session.user.id,
      action: 'DELETE_USER',
      entityType: 'User',
      entityId: params.id,
      oldValues: {
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        isActive: user.isActive
      },
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request)
    })

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Delete user error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
