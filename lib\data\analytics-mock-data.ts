// Comprehensive mock data for analytics dashboard

import { 
  StudentRiskAssessment, 
  AttendancePattern, 
  AIInsight, 
  AnalyticsKPI,
  AttendanceHeatmapData,
  TrendAnalysis,
  InterventionRecommendation,
  ParentEngagementMetrics,
  RiskFactor,
  GradeComparison,
  SubjectAnalysis,
  AttendanceMetrics
} from "@/lib/types/analytics"

// Mock Student Risk Assessments
export const mockRiskAssessments: StudentRiskAssessment[] = [
  {
    studentId: "STU001",
    riskScore: 85,
    riskLevel: "high",
    dropoutProbability: 0.35,
    riskFactors: [
      {
        type: "attendance",
        severity: "high",
        description: "Attendance rate below 75% for the past month",
        impact: 40,
        trend: "declining",
        firstDetected: "2025-01-15T00:00:00Z",
        lastOccurrence: "2025-01-30T00:00:00Z"
      },
      {
        type: "punctuality",
        severity: "medium",
        description: "Late arrival in 30% of attended days",
        impact: 25,
        trend: "stable",
        firstDetected: "2025-01-10T00:00:00Z",
        lastOccurrence: "2025-01-29T00:00:00Z"
      }
    ],
    interventions: [
      {
        id: "INT_001",
        type: "counseling",
        priority: "high",
        title: "Attendance Counseling Session",
        description: "Schedule immediate counseling to address attendance barriers",
        estimatedImpact: 70,
        timeframe: "1-2 weeks",
        status: "in_progress",
        createdAt: "2025-01-20T00:00:00Z",
        dueDate: "2025-02-03T00:00:00Z"
      }
    ],
    parentEngagement: {
      communicationFrequency: 2.5,
      responseRate: 0.8,
      meetingAttendance: 0.6,
      lastContact: "2025-01-25T00:00:00Z",
      preferredMethod: "sms",
      engagementScore: 65
    },
    lastAssessment: "2025-01-30T00:00:00Z",
    nextReview: "2025-02-06T00:00:00Z"
  },
  {
    studentId: "STU002",
    riskScore: 25,
    riskLevel: "low",
    dropoutProbability: 0.05,
    riskFactors: [],
    interventions: [],
    parentEngagement: {
      communicationFrequency: 1.2,
      responseRate: 0.95,
      meetingAttendance: 0.9,
      lastContact: "2025-01-28T00:00:00Z",
      preferredMethod: "email",
      engagementScore: 90
    },
    lastAssessment: "2025-01-30T00:00:00Z",
    nextReview: "2025-02-27T00:00:00Z"
  },
  {
    studentId: "STU003",
    riskScore: 92,
    riskLevel: "critical",
    dropoutProbability: 0.65,
    riskFactors: [
      {
        type: "attendance",
        severity: "critical",
        description: "Attendance rate below 60% with 7 consecutive absences",
        impact: 60,
        trend: "declining",
        firstDetected: "2025-01-05T00:00:00Z",
        lastOccurrence: "2025-01-30T00:00:00Z"
      },
      {
        type: "behavioral",
        severity: "high",
        description: "Irregular attendance patterns and disengagement",
        impact: 32,
        trend: "declining",
        firstDetected: "2025-01-08T00:00:00Z",
        lastOccurrence: "2025-01-29T00:00:00Z"
      }
    ],
    interventions: [
      {
        id: "INT_002",
        type: "parent_meeting",
        priority: "urgent",
        title: "Emergency Parent Conference",
        description: "Immediate meeting with parents to develop intervention plan",
        estimatedImpact: 85,
        timeframe: "3 days",
        status: "pending",
        createdAt: "2025-01-30T00:00:00Z",
        dueDate: "2025-02-02T00:00:00Z"
      }
    ],
    parentEngagement: {
      communicationFrequency: 0.5,
      responseRate: 0.3,
      meetingAttendance: 0.2,
      lastContact: "2025-01-15T00:00:00Z",
      preferredMethod: "call",
      engagementScore: 25
    },
    lastAssessment: "2025-01-30T00:00:00Z",
    nextReview: "2025-02-02T00:00:00Z"
  }
]

// Mock Attendance Patterns
export const mockAttendancePatterns: AttendancePattern[] = [
  {
    studentId: "STU001",
    patterns: {
      weeklyTrend: [0.8, 0.7, 0.6, 0.8, 0.5, 0.9, 0.0], // Mon-Sun
      monthlyTrend: Array.from({ length: 30 }, (_, i) => 0.7 + Math.sin(i / 7) * 0.2),
      seasonalTrend: [0.85, 0.82, 0.78, 0.75, 0.72, 0.68, 0.65, 0.70, 0.75, 0.80, 0.83, 0.85],
      timeOfDayPattern: Array.from({ length: 24 }, (_, i) => i >= 7 && i <= 16 ? 0.8 : 0.1),
      subjectPattern: {
        "MATH": 0.75,
        "ENGLISH": 0.82,
        "SCIENCE": 0.68,
        "HISTORY": 0.79,
        "PE": 0.85
      }
    },
    riskFactors: [
      {
        type: "attendance",
        severity: "high",
        description: "Declining weekly attendance trend",
        impact: 40,
        trend: "declining",
        firstDetected: "2025-01-15T00:00:00Z",
        lastOccurrence: "2025-01-30T00:00:00Z"
      }
    ],
    lastUpdated: "2025-01-30T00:00:00Z"
  }
]

// Mock AI Insights
export const mockAIInsights: AIInsight[] = [
  {
    id: "INS_001",
    type: "anomaly",
    severity: "warning",
    title: "Unusual Attendance Drop on Fridays",
    description: "Friday attendance has dropped 15% compared to other weekdays across Grade 11 students",
    confidence: 0.87,
    affectedGrades: ["11"],
    actionRequired: true,
    suggestedActions: [
      "Review Friday class schedules",
      "Survey students about Friday challenges",
      "Consider engaging Friday activities"
    ],
    createdAt: "2025-01-30T08:00:00Z"
  },
  {
    id: "INS_002",
    type: "prediction",
    severity: "critical",
    title: "High Dropout Risk Prediction",
    description: "AI model predicts 3 students have >70% probability of dropping out within 30 days",
    confidence: 0.92,
    affectedStudents: ["STU003", "STU015", "STU027"],
    actionRequired: true,
    suggestedActions: [
      "Immediate counseling intervention",
      "Parent engagement meetings",
      "Academic support programs"
    ],
    createdAt: "2025-01-30T09:15:00Z"
  },
  {
    id: "INS_003",
    type: "pattern",
    severity: "info",
    title: "Seasonal Attendance Pattern Detected",
    description: "Attendance typically decreases by 8% during rainy season (June-September)",
    confidence: 0.78,
    actionRequired: false,
    suggestedActions: [
      "Prepare weather-related attendance strategies",
      "Increase transportation support during rainy season",
      "Plan indoor engagement activities"
    ],
    createdAt: "2025-01-30T10:30:00Z"
  }
]

// Mock KPIs
export const mockAnalyticsKPIs: AnalyticsKPI[] = [
  {
    id: "overall-attendance",
    name: "Overall Attendance Rate",
    value: 92.3,
    unit: "%",
    trend: {
      direction: "up",
      percentage: 2.5,
      period: "vs last month"
    },
    target: 95,
    status: "warning",
    description: "School-wide attendance rate across all grades"
  },
  {
    id: "punctuality-rate",
    name: "Punctuality Rate",
    value: 87.1,
    unit: "%",
    trend: {
      direction: "down",
      percentage: 1.2,
      period: "vs last month"
    },
    target: 90,
    status: "warning",
    description: "Percentage of students arriving on time"
  },
  {
    id: "at-risk-students",
    name: "At-Risk Students",
    value: 23,
    unit: "students",
    trend: {
      direction: "up",
      percentage: 15.0,
      period: "vs last month"
    },
    target: 15,
    status: "critical",
    description: "Students identified as high or critical risk"
  },
  {
    id: "intervention-success",
    name: "Intervention Success Rate",
    value: 78.5,
    unit: "%",
    trend: {
      direction: "up",
      percentage: 5.2,
      period: "vs last quarter"
    },
    target: 80,
    status: "good",
    description: "Percentage of successful interventions"
  }
]

// Mock Attendance Heatmap Data
export const mockHeatmapData: AttendanceHeatmapData[] = []

// Generate heatmap data for the last 30 days
for (let day = 0; day < 30; day++) {
  for (let hour = 7; hour <= 16; hour++) {
    const date = new Date()
    date.setDate(date.getDate() - day)
    
    // Simulate attendance patterns
    let baseRate = 0.85
    if (hour < 8 || hour > 15) baseRate *= 0.3 // Low attendance outside school hours
    if (date.getDay() === 0 || date.getDay() === 6) baseRate *= 0.1 // Weekend
    if (date.getDay() === 5) baseRate *= 0.9 // Slightly lower on Fridays
    
    const variance = (Math.random() - 0.5) * 0.2
    const rate = Math.max(0, Math.min(1, baseRate + variance))
    const value = Math.floor(rate * 450) // Assuming ~450 total students
    
    mockHeatmapData.push({
      date: date.toISOString().split('T')[0],
      hour,
      value,
      rate
    })
  }
}

// Mock Trend Analysis
export const mockTrendAnalysis: TrendAnalysis = {
  period: "daily",
  data: Array.from({ length: 30 }, (_, i) => {
    const date = new Date()
    date.setDate(date.getDate() - (29 - i))
    return {
      date: date.toISOString().split('T')[0],
      value: 0.85 + Math.sin(i / 7) * 0.1 + (Math.random() - 0.5) * 0.05
    }
  }),
  trend: "stable",
  seasonality: true,
  forecast: Array.from({ length: 7 }, (_, i) => {
    const date = new Date()
    date.setDate(date.getDate() + i + 1)
    return {
      date: date.toISOString().split('T')[0],
      predicted: 0.87 + Math.sin((30 + i) / 7) * 0.08,
      confidence: 0.75 - i * 0.05
    }
  })
}

// Mock Grade Comparisons
export const mockGradeComparisons: GradeComparison[] = [
  {
    grade: "7",
    metrics: {
      totalStudents: 78,
      presentCount: 72,
      lateCount: 4,
      absentCount: 2,
      attendanceRate: 0.923,
      punctualityRate: 0.947,
      averageArrivalTime: "07:45",
      peakHours: ["07:30-08:00", "13:00-13:30"]
    },
    trend: mockTrendAnalysis,
    riskDistribution: { low: 65, medium: 10, high: 2, critical: 1 }
  },
  {
    grade: "8",
    metrics: {
      totalStudents: 82,
      presentCount: 75,
      lateCount: 5,
      absentCount: 2,
      attendanceRate: 0.915,
      punctualityRate: 0.938,
      averageArrivalTime: "07:47",
      peakHours: ["07:30-08:00", "13:00-13:30"]
    },
    trend: mockTrendAnalysis,
    riskDistribution: { low: 68, medium: 11, high: 2, critical: 1 }
  }
]

// Utility functions for mock data
export function getStudentRiskAssessment(studentId: string): StudentRiskAssessment | undefined {
  return mockRiskAssessments.find(assessment => assessment.studentId === studentId)
}

export function getAttendancePattern(studentId: string): AttendancePattern | undefined {
  return mockAttendancePatterns.find(pattern => pattern.studentId === studentId)
}

export function getAIInsightsByType(type: string): AIInsight[] {
  return mockAIInsights.filter(insight => insight.type === type)
}

export function getKPIById(id: string): AnalyticsKPI | undefined {
  return mockAnalyticsKPIs.find(kpi => kpi.id === id)
}

export function getHeatmapDataByDateRange(startDate: string, endDate: string): AttendanceHeatmapData[] {
  return mockHeatmapData.filter(data => data.date >= startDate && data.date <= endDate)
}

export function getRiskAssessmentsByLevel(level: string): StudentRiskAssessment[] {
  return mockRiskAssessments.filter(assessment => assessment.riskLevel === level)
}
