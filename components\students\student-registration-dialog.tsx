"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { StudentRegistrationForm } from "./student-registration-form"
import { type StudentRegistrationFormData } from "@/lib/validations/student"
import { Student } from "@/lib/types/student"
import { toast } from "sonner"

interface StudentRegistrationDialogProps {
  trigger?: React.ReactNode
  onStudentCreated?: (student: Student) => void
  initialData?: Partial<StudentRegistrationFormData>
  mode?: 'create' | 'edit'
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function StudentRegistrationDialog({
  trigger,
  onStudentCreated,
  initialData,
  mode = 'create',
  open,
  onOpenChange
}: StudentRegistrationDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleOpenChange = (newOpen: boolean) => {
    if (onOpenChange) {
      onOpenChange(newOpen)
    } else {
      setIsOpen(newOpen)
    }
  }

  const handleSubmit = async (data: StudentRegistrationFormData) => {
    setIsLoading(true)
    try {
      // Convert form data to Student object
      const student: Student = {
        id: data.id,
        firstName: data.firstName,
        middleName: data.middleName,
        lastName: data.lastName,
        email: data.email,
        course: data.course,
        year: data.year,
        section: data.section,
        grade: data.grade,
        status: 'Active',
        photo: typeof data.photo === 'string' ? data.photo : undefined,
        qrCode: `QR_${data.id}_2025`,
        dateOfBirth: data.dateOfBirth,
        gender: data.gender,
        guardian: {
          name: data.guardianName,
          phone: data.guardianPhone,
          email: data.guardianEmail,
          relationship: data.guardianRelationship,
          address: data.guardianAddress
        },
        emergencyContacts: data.emergencyContacts,
        address: {
          street: data.street,
          barangay: data.barangay,
          city: data.city,
          province: data.province,
          zipCode: data.zipCode,
          country: data.country || 'Philippines'
        },
        enrollmentDate: new Date().toISOString().split('T')[0],
        lastUpdated: new Date().toISOString()
      }

      // Here you would typically make an API call to save the student
      // For now, we'll simulate a successful save
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast.success(
        mode === 'edit' 
          ? "Student information updated successfully!" 
          : "Student registered successfully!"
      )

      if (onStudentCreated) {
        onStudentCreated(student)
      }

      handleOpenChange(false)
    } catch (error) {
      console.error('Error saving student:', error)
      toast.error("Failed to save student information. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    handleOpenChange(false)
  }

  const dialogOpen = open !== undefined ? open : isOpen

  return (
    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      
      {!trigger && (
        <DialogTrigger asChild>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Student
          </Button>
        </DialogTrigger>
      )}

      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === 'edit' ? 'Edit Student Information' : 'Register New Student'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'edit' 
              ? 'Update the student\'s information below.' 
              : 'Fill in the student\'s information to register them in the system.'
            }
          </DialogDescription>
        </DialogHeader>

        <StudentRegistrationForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          initialData={initialData}
          isLoading={isLoading}
          mode={mode}
        />
      </DialogContent>
    </Dialog>
  )
}

// Quick registration dialog with minimal fields
export function QuickStudentRegistrationDialog({
  trigger,
  onStudentCreated
}: {
  trigger?: React.ReactNode
  onStudentCreated?: (student: Student) => void
}) {
  return (
    <StudentRegistrationDialog
      trigger={trigger}
      onStudentCreated={onStudentCreated}
      mode="create"
    />
  )
}
