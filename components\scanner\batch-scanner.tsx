"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { BatchSession, BatchScanItem, Student, AttendanceAction } from "@/lib/types/scanner"
import { 
  Users, 
  CheckCircle, 
  Clock, 
  XCircle, 
  Play, 
  Pause, 
  RotateCcw,
  Download,
  Filter
} from "lucide-react"
import { cn } from "@/lib/utils"

interface BatchScannerProps {
  session: BatchSession | null
  onStartSession: (students: Student[], subject?: string, period?: string) => void
  onPauseSession: () => void
  onResumeSession: () => void
  onResetSession: () => void
  onMarkStudent: (studentId: string, action: AttendanceAction) => void
  onExportResults: () => void
  isScanning: boolean
  currentStudent?: Student
}

export function BatchScanner({
  session,
  onStartSession,
  onPauseSession,
  onResumeSession,
  onResetSession,
  onMarkStudent,
  onExportResults,
  isScanning,
  currentStudent
}: BatchScannerProps) {
  const [filter, setFilter] = useState<'all' | 'scanned' | 'pending'>('all')

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getStatusIcon = (item: BatchScanItem) => {
    if (!item.scanned) return <Clock className="h-4 w-4 text-muted-foreground" />
    
    switch (item.status) {
      case 'present':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'late':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'absent':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <CheckCircle className="h-4 w-4 text-green-600" />
    }
  }

  const getStatusColor = (item: BatchScanItem) => {
    if (!item.scanned) return 'bg-muted'
    
    switch (item.status) {
      case 'present':
        return 'bg-green-100 border-green-200 dark:bg-green-950 dark:border-green-800'
      case 'late':
        return 'bg-yellow-100 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800'
      case 'absent':
        return 'bg-red-100 border-red-200 dark:bg-red-950 dark:border-red-800'
      default:
        return 'bg-green-100 border-green-200 dark:bg-green-950 dark:border-green-800'
    }
  }

  const filteredStudents = session?.students.filter(item => {
    switch (filter) {
      case 'scanned':
        return item.scanned
      case 'pending':
        return !item.scanned
      default:
        return true
    }
  }) || []

  const progress = session ? (session.completedCount / session.totalCount) * 100 : 0

  if (!session) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Batch Scanner
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <Users className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-semibold mb-2">No Active Session</h3>
          <p className="text-muted-foreground mb-4">
            Start a batch scanning session to process multiple students
          </p>
          <Button onClick={() => onStartSession([])}>
            <Play className="mr-2 h-4 w-4" />
            Start Batch Session
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="w-full max-w-2xl mx-auto space-y-4">
      {/* Session Header */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              {session.name}
            </CardTitle>
            <Badge variant="outline">
              {session.completedCount} / {session.totalCount}
            </Badge>
          </div>
          
          {(session.subject || session.period) && (
            <div className="flex gap-2 text-sm text-muted-foreground">
              {session.subject && <span>Subject: {session.subject}</span>}
              {session.period && <span>Period: {session.period}</span>}
            </div>
          )}
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Controls */}
          <div className="flex gap-2">
            {isScanning ? (
              <Button onClick={onPauseSession} variant="outline">
                <Pause className="mr-2 h-4 w-4" />
                Pause
              </Button>
            ) : (
              <Button onClick={onResumeSession}>
                <Play className="mr-2 h-4 w-4" />
                Resume
              </Button>
            )}
            
            <Button onClick={onResetSession} variant="outline">
              <RotateCcw className="mr-2 h-4 w-4" />
              Reset
            </Button>
            
            <Button onClick={onExportResults} variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>

          {/* Filter */}
          <div className="flex gap-2">
            <Button
              variant={filter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('all')}
            >
              All ({session.totalCount})
            </Button>
            <Button
              variant={filter === 'scanned' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('scanned')}
            >
              Scanned ({session.completedCount})
            </Button>
            <Button
              variant={filter === 'pending' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('pending')}
            >
              Pending ({session.totalCount - session.completedCount})
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Student List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Students ({filteredStudents.length})
          </CardTitle>
        </CardHeader>
        
        <CardContent className="p-0">
          <ScrollArea className="h-96">
            <div className="space-y-2 p-4">
              {filteredStudents.map((item, index) => (
                <Card
                  key={item.student.id}
                  className={cn(
                    "transition-all duration-200",
                    getStatusColor(item),
                    currentStudent?.id === item.student.id && "ring-2 ring-primary scale-[1.02]"
                  )}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-mono text-muted-foreground w-6">
                          {index + 1}
                        </span>
                        {getStatusIcon(item)}
                      </div>
                      
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={item.student.photo} alt={item.student.name} />
                        <AvatarFallback className="text-sm">
                          {getInitials(item.student.name)}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium truncate">{item.student.name}</h4>
                          <Badge variant="outline" className="text-xs">
                            {item.student.id}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground truncate">
                          {item.student.course} • {item.student.year}
                        </p>
                      </div>
                      
                      <div className="flex flex-col items-end gap-1">
                        {item.scanned && item.timestamp && (
                          <span className="text-xs text-muted-foreground">
                            {item.timestamp.toLocaleTimeString()}
                          </span>
                        )}
                        
                        {item.scanned && item.status && (
                          <Badge 
                            variant={
                              item.status === 'present' ? 'default' :
                              item.status === 'late' ? 'secondary' : 'destructive'
                            }
                            className="text-xs"
                          >
                            {item.status}
                          </Badge>
                        )}
                        
                        {!item.scanned && (
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onMarkStudent(item.student.id, 'present')}
                              className="h-6 px-2 text-xs"
                            >
                              Present
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onMarkStudent(item.student.id, 'absent')}
                              className="h-6 px-2 text-xs"
                            >
                              Absent
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {filteredStudents.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No students match the current filter</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  )
}
