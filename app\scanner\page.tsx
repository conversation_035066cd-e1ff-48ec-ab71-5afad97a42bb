"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { QRScanner } from "@/components/scanner/qr-scanner"
import { StudentInfoCard } from "@/components/scanner/student-info-card"
import { AttendanceMarking } from "@/components/scanner/attendance-marking"
import { FeedbackSystem } from "@/components/scanner/feedback-system"
import { ManualEntryDialog } from "@/components/scanner/manual-entry-dialog"
import { BatchScanner } from "@/components/scanner/batch-scanner"
import { SettingsPanel } from "@/components/scanner/settings-panel"
import { SyncStatusIndicator } from "@/components/scanner/sync-status-indicator"
import { OfflineManager } from "@/lib/utils/offline-manager"
import {
  <PERSON>,
  ScanResult,
  AttendanceAction,
  ScanMode,
  ScannerSettings,
  ScanNotification,
  ManualEntry,
  BatchSession,
  CameraDevice,
  AttendanceRecord
} from "@/lib/types/scanner"
import {
  mockStudents,
  mockSubjects,
  mockTimePeriods,
  findStudentByQRCode,
  getTodayAttendanceRecord
} from "@/lib/data/mock-data"
import { QrCode, Camera, Settings, KeyboardIcon, Users, Wifi } from "lucide-react"



export default function ScannerPage() {
  // Core scanner state
  const [isScanning, setIsScanning] = useState(false)
  const [scanMode, setScanMode] = useState<ScanMode>('gate')
  const [currentStudent, setCurrentStudent] = useState<Student | null>(null)
  const [attendanceRecord, setAttendanceRecord] = useState<AttendanceRecord | null>(null)

  // Settings and configuration
  const [settings, setSettings] = useState<ScannerSettings>({
    audioEnabled: true,
    vibrationEnabled: true,
    scanDelay: 1000,
    autoAdvance: false,
    offlineMode: false,
    syncInterval: 5
  })
  const [selectedSubject, setSelectedSubject] = useState<string>("")
  const [selectedPeriod, setSelectedPeriod] = useState<string>("")
  const [availableCameras, setAvailableCameras] = useState<CameraDevice[]>([])

  // UI state
  const [notifications, setNotifications] = useState<ScanNotification[]>([])
  const [showManualEntry, setShowManualEntry] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Batch scanning state
  const [batchSession, setBatchSession] = useState<BatchSession | null>(null)

  // Initialize offline manager
  useEffect(() => {
    const offlineManager = OfflineManager.getInstance()
    offlineManager.updateSyncInterval(settings.syncInterval)
  }, [settings.syncInterval])

  // Add notification helper
  const addNotification = useCallback((notification: Omit<ScanNotification, 'id' | 'timestamp'>) => {
    const newNotification: ScanNotification = {
      ...notification,
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    }
    setNotifications(prev => [...prev, newNotification])
  }, [])

  // Remove notification
  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }, [])

  // Handle QR scan result
  const handleScanResult = useCallback(async (result: ScanResult) => {
    if (!result.success || !result.data) {
      addNotification({
        type: 'error',
        title: 'Scan Failed',
        message: result.error || 'Invalid QR code',
        duration: 3000
      })
      return
    }

    setIsLoading(true)

    try {
      // Look up student by QR code
      const response = await fetch('/api/scanner/lookup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ qrCode: result.data })
      })

      const data = await response.json()

      if (data.success && data.data) {
        const student = data.data as Student
        setCurrentStudent(student)

        // Get today's attendance record
        const todayRecord = getTodayAttendanceRecord(student.id)
        setAttendanceRecord(todayRecord || null)

        addNotification({
          type: 'success',
          title: 'Student Found',
          message: `${student.name} (${student.id})`,
          duration: 2000
        })
      } else {
        addNotification({
          type: 'error',
          title: 'Student Not Found',
          message: data.error || 'QR code not recognized',
          duration: 3000
        })
      }
    } catch (error) {
      console.error('Lookup error:', error)
      addNotification({
        type: 'error',
        title: 'Lookup Failed',
        message: 'Failed to look up student',
        duration: 3000
      })
    } finally {
      setIsLoading(false)
    }
  }, [addNotification])

  // Handle attendance marking
  const handleMarkAttendance = useCallback(async (action: AttendanceAction, options?: any) => {
    if (!currentStudent) return

    setIsLoading(true)

    try {
      const attendanceData = {
        studentId: currentStudent.id,
        action,
        subject: options?.subject || selectedSubject,
        period: options?.period || selectedPeriod,
        reason: options?.reason
      }

      const response = await fetch('/api/scanner/attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(attendanceData)
      })

      const data = await response.json()

      if (data.success) {
        addNotification({
          type: 'success',
          title: 'Attendance Recorded',
          message: `${currentStudent.name} marked as ${action}`,
          duration: 2000
        })

        // Update attendance record
        setAttendanceRecord(data.data)

        // Clear current student after a delay
        setTimeout(() => {
          setCurrentStudent(null)
          setAttendanceRecord(null)
        }, 2000)
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      console.error('Attendance error:', error)

      // Add to offline queue
      const offlineManager = OfflineManager.getInstance()
      const attendanceRecord: AttendanceRecord = {
        id: `ATT_${Date.now()}`,
        studentId: currentStudent.id,
        studentName: currentStudent.name,
        course: currentStudent.course,
        date: new Date().toISOString().split('T')[0],
        status: action === 'present' ? 'Present' : action === 'late' ? 'Late' : 'Absent',
        type: scanMode === 'gate' ? 'gate' : 'subject',
        timestamp: new Date(),
        subject: selectedSubject,
        period: selectedPeriod
      }

      offlineManager.addToQueue('attendance', attendanceRecord)

      addNotification({
        type: 'warning',
        title: 'Saved Offline',
        message: 'Attendance saved locally and will sync when online',
        duration: 3000
      })
    } finally {
      setIsLoading(false)
    }
  }, [currentStudent, selectedSubject, selectedPeriod, scanMode, addNotification])

  // Handle manual entry
  const handleManualEntry = useCallback(async (entry: ManualEntry) => {
    setIsLoading(true)

    try {
      const response = await fetch('/api/scanner/attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          studentId: entry.studentId,
          action: entry.action,
          reason: entry.reason
        })
      })

      const data = await response.json()

      if (data.success) {
        addNotification({
          type: 'success',
          title: 'Manual Entry Recorded',
          message: `Attendance recorded for ${entry.studentId}`,
          duration: 2000
        })
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      console.error('Manual entry error:', error)
      addNotification({
        type: 'error',
        title: 'Entry Failed',
        message: 'Failed to record manual entry',
        duration: 3000
      })
    } finally {
      setIsLoading(false)
    }
  }, [addNotification])

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-semibold">QR Scanner</h1>
            <Badge variant="outline" className="capitalize">
              {scanMode} Mode
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            <SyncStatusIndicator />
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowManualEntry(true)}
            >
              <KeyboardIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto p-4 space-y-6">
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Left Column - Scanner */}
          <div className="lg:col-span-2 space-y-6">
            {/* Mode Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Scanner Mode</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    variant={scanMode === 'gate' ? 'default' : 'outline'}
                    onClick={() => setScanMode('gate')}
                    className="h-16 flex-col"
                  >
                    <QrCode className="h-6 w-6 mb-1" />
                    <span className="text-sm">Gate</span>
                  </Button>
                  <Button
                    variant={scanMode === 'subject' ? 'default' : 'outline'}
                    onClick={() => setScanMode('subject')}
                    className="h-16 flex-col"
                  >
                    <Camera className="h-6 w-6 mb-1" />
                    <span className="text-sm">Subject</span>
                  </Button>
                  <Button
                    variant={scanMode === 'batch' ? 'default' : 'outline'}
                    onClick={() => setScanMode('batch')}
                    className="h-16 flex-col"
                  >
                    <Users className="h-6 w-6 mb-1" />
                    <span className="text-sm">Batch</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Scanner Interface */}
            {scanMode !== 'batch' && (
              <QRScanner
                onScanResult={handleScanResult}
                isActive={isScanning}
                onCameraError={(error) => addNotification({
                  type: 'error',
                  title: 'Camera Error',
                  message: error,
                  duration: 5000
                })}
              />
            )}

            {/* Batch Scanner */}
            {scanMode === 'batch' && (
              <BatchScanner
                session={batchSession}
                onStartSession={(students, subject, period) => {
                  setBatchSession({
                    id: `batch_${Date.now()}`,
                    name: `Batch Session ${new Date().toLocaleTimeString()}`,
                    students: students.map(student => ({
                      student,
                      scanned: false
                    })),
                    subject,
                    period,
                    startTime: new Date(),
                    completedCount: 0,
                    totalCount: students.length
                  })
                }}
                onPauseSession={() => setIsScanning(false)}
                onResumeSession={() => setIsScanning(true)}
                onResetSession={() => setBatchSession(null)}
                onMarkStudent={(studentId, action) => {
                  // Handle batch student marking
                  console.log('Mark student:', studentId, action)
                }}
                onExportResults={() => {
                  // Handle export
                  console.log('Export results')
                }}
                isScanning={isScanning}
                currentStudent={currentStudent}
              />
            )}
          </div>

          {/* Right Column - Student Info & Controls */}
          <div className="space-y-6">
            {/* Student Information */}
            {currentStudent && (
              <StudentInfoCard
                student={currentStudent}
                attendanceRecord={attendanceRecord}
              />
            )}

            {/* Attendance Marking */}
            {currentStudent && scanMode !== 'batch' && (
              <AttendanceMarking
                student={currentStudent}
                mode={scanMode}
                onMarkAttendance={handleMarkAttendance}
                isLoading={isLoading}
                subjects={mockSubjects}
                periods={mockTimePeriods}
                selectedSubject={selectedSubject}
                selectedPeriod={selectedPeriod}
                onSubjectChange={setSelectedSubject}
                onPeriodChange={setSelectedPeriod}
              />
            )}

            {/* Settings Panel */}
            {showSettings && (
              <SettingsPanel
                settings={settings}
                onUpdateSettings={(newSettings) => setSettings(prev => ({ ...prev, ...newSettings }))}
                availableCameras={availableCameras}
                subjects={mockSubjects}
                periods={mockTimePeriods}
                selectedSubject={selectedSubject}
                selectedPeriod={selectedPeriod}
                onSubjectChange={setSelectedSubject}
                onPeriodChange={setSelectedPeriod}
                scanMode={scanMode}
                onScanModeChange={setScanMode}
                isOnline={true}
              />
            )}
          </div>
        </div>
      </div>

      {/* Feedback System */}
      <FeedbackSystem
        notifications={notifications}
        onDismissNotification={removeNotification}
        audioEnabled={settings.audioEnabled}
        onToggleAudio={() => setSettings(prev => ({ ...prev, audioEnabled: !prev.audioEnabled }))}
      />

      {/* Manual Entry Dialog */}
      <ManualEntryDialog
        isOpen={showManualEntry}
        onOpenChange={setShowManualEntry}
        students={mockStudents}
        onSubmitEntry={handleManualEntry}
        isLoading={isLoading}
      />
    </div>
  )
}
