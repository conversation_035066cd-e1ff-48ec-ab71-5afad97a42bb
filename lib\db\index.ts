import { prisma } from '../prisma'
import type {
  Student,
  Teacher,
  Subject,
  Attendance,
  AttendancePattern,
  StudentRiskAssessment,
  SMSLog,
  StudentFormData,
  TeacherFormData,
  AttendanceFormData,
  StudentFilters,
  TeacherFilters,
  AttendanceFilters,
  PaginationConfig,
  StudentWithComputed,
  TeacherWithComputed,
  AttendanceWithRelations,
  convertPrismaStudentToLegacy,
  convertPrismaTeacherToLegacy
} from '../types/prisma'

// Student operations
export class StudentService {
  static async create(data: StudentFormData): Promise<Student> {
    return await prisma.student.create({
      data: {
        ...data,
        enrollmentDate: new Date()
      }
    })
  }

  static async findById(id: string): Promise<StudentWithComputed | null> {
    const student = await prisma.student.findUnique({
      where: { id },
      include: {
        attendanceRecords: {
          take: 10,
          orderBy: { date: 'desc' }
        },
        attendancePatterns: true,
        riskAssessments: {
          take: 1,
          orderBy: { assessmentDate: 'desc' }
        }
      }
    })

    if (!student) return null

    return {
      ...convertPrismaStudentToLegacy(student),
      attendanceStats: await this.calculateAttendanceStats(id)
    }
  }

  static async findByStudentId(studentId: string): Promise<StudentWithComputed | null> {
    const student = await prisma.student.findUnique({
      where: { studentId }
    })

    if (!student) return null
    return convertPrismaStudentToLegacy(student)
  }

  static async list(
    filters: StudentFilters = {},
    pagination: PaginationConfig = { page: 1, pageSize: 20, total: 0 }
  ): Promise<{ students: StudentWithComputed[], total: number }> {
    const where: any = {}

    if (filters.search) {
      where.OR = [
        { firstName: { contains: filters.search, mode: 'insensitive' } },
        { lastName: { contains: filters.search, mode: 'insensitive' } },
        { studentId: { contains: filters.search } }
      ]
    }

    if (filters.gradeLevel?.length) {
      where.gradeLevel = { in: filters.gradeLevel }
    }

    if (filters.section?.length) {
      where.section = { in: filters.section }
    }

    if (filters.status?.length) {
      where.status = { in: filters.status }
    }

    if (filters.course?.length) {
      where.course = { in: filters.course }
    }

    if (filters.year?.length) {
      where.year = { in: filters.year }
    }

    const [students, total] = await Promise.all([
      prisma.student.findMany({
        where,
        skip: (pagination.page - 1) * pagination.pageSize,
        take: pagination.pageSize,
        orderBy: [
          { lastName: 'asc' },
          { firstName: 'asc' }
        ]
      }),
      prisma.student.count({ where })
    ])

    return {
      students: students.map(convertPrismaStudentToLegacy),
      total
    }
  }

  static async update(id: string, data: Partial<StudentFormData>): Promise<Student> {
    return await prisma.student.update({
      where: { id },
      data
    })
  }

  static async delete(id: string): Promise<void> {
    await prisma.student.delete({
      where: { id }
    })
  }

  static async calculateAttendanceStats(studentId: string) {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const attendance = await prisma.attendance.findMany({
      where: {
        studentId,
        date: { gte: thirtyDaysAgo }
      }
    })

    const totalDays = attendance.length
    const presentDays = attendance.filter(a => a.status === 'PRESENT').length
    const lateDays = attendance.filter(a => a.status === 'LATE').length
    const absentDays = attendance.filter(a => a.status === 'ABSENT').length
    const attendanceRate = totalDays > 0 ? (presentDays + lateDays) / totalDays : 0

    const lastAttendance = attendance
      .sort((a, b) => b.date.getTime() - a.date.getTime())[0]

    return {
      totalDays,
      presentDays,
      lateDays,
      absentDays,
      attendanceRate,
      lastAttendance: lastAttendance?.date.toISOString()
    }
  }
}

// Teacher operations
export class TeacherService {
  static async create(data: TeacherFormData): Promise<Teacher> {
    return await prisma.teacher.create({
      data: {
        ...data,
        subjects: data.subjects ? JSON.stringify(data.subjects) : null,
        gradeLevels: data.gradeLevels ? JSON.stringify(data.gradeLevels) : null,
        sections: data.sections ? JSON.stringify(data.sections) : null
      }
    })
  }

  static async findById(id: string): Promise<TeacherWithComputed | null> {
    const teacher = await prisma.teacher.findUnique({
      where: { id },
      include: {
        subjectsTeaching: true,
        attendanceScanned: {
          take: 10,
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!teacher) return null
    return convertPrismaTeacherToLegacy(teacher)
  }

  static async list(
    filters: TeacherFilters = {},
    pagination: PaginationConfig = { page: 1, pageSize: 20, total: 0 }
  ): Promise<{ teachers: TeacherWithComputed[], total: number }> {
    const where: any = {}

    if (filters.search) {
      where.OR = [
        { firstName: { contains: filters.search, mode: 'insensitive' } },
        { lastName: { contains: filters.search, mode: 'insensitive' } },
        { employeeId: { contains: filters.search } }
      ]
    }

    if (filters.role?.length) {
      where.role = { in: filters.role }
    }

    if (filters.status?.length) {
      where.status = { in: filters.status }
    }

    const [teachers, total] = await Promise.all([
      prisma.teacher.findMany({
        where,
        skip: (pagination.page - 1) * pagination.pageSize,
        take: pagination.pageSize,
        orderBy: [
          { lastName: 'asc' },
          { firstName: 'asc' }
        ]
      }),
      prisma.teacher.count({ where })
    ])

    return {
      teachers: teachers.map(convertPrismaTeacherToLegacy),
      total
    }
  }

  static async update(id: string, data: Partial<TeacherFormData>): Promise<Teacher> {
    const updateData: any = { ...data }
    
    if (data.subjects) {
      updateData.subjects = JSON.stringify(data.subjects)
    }
    if (data.gradeLevels) {
      updateData.gradeLevels = JSON.stringify(data.gradeLevels)
    }
    if (data.sections) {
      updateData.sections = JSON.stringify(data.sections)
    }

    return await prisma.teacher.update({
      where: { id },
      data: updateData
    })
  }

  static async delete(id: string): Promise<void> {
    await prisma.teacher.delete({
      where: { id }
    })
  }
}

// Attendance operations
export class AttendanceService {
  static async create(data: AttendanceFormData): Promise<Attendance> {
    return await prisma.attendance.create({
      data
    })
  }

  static async findById(id: string): Promise<AttendanceWithRelations | null> {
    return await prisma.attendance.findUnique({
      where: { id },
      include: {
        student: true,
        subject: true,
        scannedByTeacher: true
      }
    })
  }

  static async list(
    filters: AttendanceFilters = {},
    pagination: PaginationConfig = { page: 1, pageSize: 20, total: 0 }
  ): Promise<{ attendance: AttendanceWithRelations[], total: number }> {
    const where: any = {}

    if (filters.studentId) {
      where.studentId = filters.studentId
    }

    if (filters.dateFrom || filters.dateTo) {
      where.date = {}
      if (filters.dateFrom) where.date.gte = filters.dateFrom
      if (filters.dateTo) where.date.lte = filters.dateTo
    }

    if (filters.attendanceType?.length) {
      where.attendanceType = { in: filters.attendanceType }
    }

    if (filters.status?.length) {
      where.status = { in: filters.status }
    }

    if (filters.subjectId) {
      where.subjectId = filters.subjectId
    }

    const [attendance, total] = await Promise.all([
      prisma.attendance.findMany({
        where,
        skip: (pagination.page - 1) * pagination.pageSize,
        take: pagination.pageSize,
        include: {
          student: true,
          subject: true,
          scannedByTeacher: true
        },
        orderBy: { date: 'desc' }
      }),
      prisma.attendance.count({ where })
    ])

    return { attendance, total }
  }

  static async recordAttendance(
    studentId: string,
    attendanceType: 'GATE' | 'SUBJECT' = 'GATE',
    subjectId?: string,
    scannedBy?: string
  ): Promise<Attendance> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Check if attendance already exists for today
    const existing = await prisma.attendance.findFirst({
      where: {
        studentId,
        date: today,
        attendanceType,
        subjectId: subjectId || null
      }
    })

    if (existing) {
      // Update time out if checking out
      return await prisma.attendance.update({
        where: { id: existing.id },
        data: {
          timeOut: new Date(),
          updatedAt: new Date()
        }
      })
    }

    // Create new attendance record
    const now = new Date()
    const cutoffTime = new Date(today)
    cutoffTime.setHours(7, 30, 0, 0) // 7:30 AM cutoff

    const status = now > cutoffTime ? 'LATE' : 'PRESENT'

    return await prisma.attendance.create({
      data: {
        studentId,
        date: today,
        timeIn: now,
        attendanceType,
        subjectId,
        status,
        scannedBy,
        isManualEntry: false,
        location: attendanceType === 'GATE' ? 'Main Gate' : 'Classroom'
      }
    })
  }
}

// Subject operations
export class SubjectService {
  static async list(): Promise<Subject[]> {
    return await prisma.subject.findMany({
      include: {
        teacher: true
      },
      orderBy: [
        { gradeLevel: 'asc' },
        { subjectName: 'asc' }
      ]
    })
  }

  static async findByGradeLevel(gradeLevel: string): Promise<Subject[]> {
    return await prisma.subject.findMany({
      where: { gradeLevel: gradeLevel as any },
      include: {
        teacher: true
      },
      orderBy: { subjectName: 'asc' }
    })
  }
}

// System settings operations
export class SystemSettingsService {
  static async get(key: string): Promise<string | null> {
    const setting = await prisma.systemSettings.findUnique({
      where: { key }
    })
    return setting?.value || null
  }

  static async set(key: string, value: string, description?: string): Promise<void> {
    await prisma.systemSettings.upsert({
      where: { key },
      update: { value, description },
      create: { key, value, description }
    })
  }

  static async getAll(): Promise<Record<string, string>> {
    const settings = await prisma.systemSettings.findMany()
    return settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value
      return acc
    }, {} as Record<string, string>)
  }
}
