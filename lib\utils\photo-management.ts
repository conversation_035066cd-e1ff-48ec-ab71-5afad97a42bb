// Photo management utilities for student photos

export interface PhotoValidationResult {
  valid: boolean
  error?: string
  warnings?: string[]
}

export interface PhotoCompressionOptions {
  maxWidth: number
  maxHeight: number
  quality: number
  format: 'jpeg' | 'png' | 'webp'
}

export interface PhotoMetadata {
  originalName: string
  size: number
  type: string
  dimensions: { width: number; height: number }
  lastModified: number
}

export class PhotoManager {
  private static instance: PhotoManager
  
  static getInstance(): PhotoManager {
    if (!PhotoManager.instance) {
      PhotoManager.instance = new PhotoManager()
    }
    return PhotoManager.instance
  }

  // Default compression options
  private defaultOptions: PhotoCompressionOptions = {
    maxWidth: 800,
    maxHeight: 800,
    quality: 0.8,
    format: 'jpeg'
  }

  // Validate photo file
  validatePhoto(file: File): PhotoValidationResult {
    const result: PhotoValidationResult = { valid: true, warnings: [] }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Please upload a JPEG, PNG, or WebP image.'
      }
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size too large. Please upload an image smaller than 10MB.'
      }
    }

    // Add warnings for large files
    if (file.size > 5 * 1024 * 1024) { // 5MB
      result.warnings?.push('Large file size detected. Consider compressing the image.')
    }

    // Check file name
    if (file.name.length > 100) {
      result.warnings?.push('File name is very long. It will be shortened during upload.')
    }

    return result
  }

  // Get image dimensions
  async getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      const url = URL.createObjectURL(file)
      
      img.onload = () => {
        URL.revokeObjectURL(url)
        resolve({ width: img.width, height: img.height })
      }
      
      img.onerror = () => {
        URL.revokeObjectURL(url)
        reject(new Error('Failed to load image'))
      }
      
      img.src = url
    })
  }

  // Compress image
  async compressImage(
    file: File, 
    options: Partial<PhotoCompressionOptions> = {}
  ): Promise<{ file: File; metadata: PhotoMetadata }> {
    const opts = { ...this.defaultOptions, ...options }
    
    return new Promise(async (resolve, reject) => {
      try {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) {
          reject(new Error('Canvas context not available'))
          return
        }

        const img = new Image()
        const url = URL.createObjectURL(file)
        
        img.onload = () => {
          URL.revokeObjectURL(url)
          
          // Calculate new dimensions
          let { width, height } = img
          const aspectRatio = width / height
          
          if (width > opts.maxWidth) {
            width = opts.maxWidth
            height = width / aspectRatio
          }
          
          if (height > opts.maxHeight) {
            height = opts.maxHeight
            width = height * aspectRatio
          }
          
          // Set canvas dimensions
          canvas.width = width
          canvas.height = height
          
          // Draw and compress image
          ctx.drawImage(img, 0, 0, width, height)
          
          canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('Failed to compress image'))
                return
              }
              
              // Create new file
              const compressedFile = new File(
                [blob], 
                this.generateFileName(file.name, opts.format),
                { type: `image/${opts.format}` }
              )
              
              const metadata: PhotoMetadata = {
                originalName: file.name,
                size: compressedFile.size,
                type: compressedFile.type,
                dimensions: { width, height },
                lastModified: Date.now()
              }
              
              resolve({ file: compressedFile, metadata })
            },
            `image/${opts.format}`,
            opts.quality
          )
        }
        
        img.onerror = () => {
          URL.revokeObjectURL(url)
          reject(new Error('Failed to load image for compression'))
        }
        
        img.src = url
      } catch (error) {
        reject(error)
      }
    })
  }

  // Generate unique filename
  private generateFileName(originalName: string, format: string): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8)
    const baseName = originalName.split('.')[0].substring(0, 20)
    return `${baseName}_${timestamp}_${random}.${format}`
  }

  // Create photo preview URL
  createPreviewUrl(file: File): string {
    return URL.createObjectURL(file)
  }

  // Revoke preview URL
  revokePreviewUrl(url: string): void {
    URL.revokeObjectURL(url)
  }

  // Process photo for upload
  async processPhotoForUpload(
    file: File,
    options: Partial<PhotoCompressionOptions> = {}
  ): Promise<{
    processedFile: File
    metadata: PhotoMetadata
    previewUrl: string
    validationResult: PhotoValidationResult
  }> {
    // Validate photo
    const validationResult = this.validatePhoto(file)
    if (!validationResult.valid) {
      throw new Error(validationResult.error)
    }

    // Get original dimensions
    const originalDimensions = await this.getImageDimensions(file)
    
    // Compress if needed
    let processedFile = file
    let metadata: PhotoMetadata = {
      originalName: file.name,
      size: file.size,
      type: file.type,
      dimensions: originalDimensions,
      lastModified: file.lastModified
    }

    // Compress if image is large or if compression is requested
    const shouldCompress = 
      file.size > 1024 * 1024 || // > 1MB
      originalDimensions.width > 1200 || 
      originalDimensions.height > 1200 ||
      Object.keys(options).length > 0

    if (shouldCompress) {
      const compressed = await this.compressImage(file, options)
      processedFile = compressed.file
      metadata = compressed.metadata
    }

    // Create preview URL
    const previewUrl = this.createPreviewUrl(processedFile)

    return {
      processedFile,
      metadata,
      previewUrl,
      validationResult
    }
  }

  // Upload photo (mock implementation)
  async uploadPhoto(file: File, studentId: string): Promise<string> {
    // In a real implementation, this would upload to a storage service
    // For now, we'll simulate the upload and return a mock URL
    
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate upload delay
    
    const timestamp = Date.now()
    const mockUrl = `https://storage.qrsams.edu.ph/students/${studentId}/photo_${timestamp}.jpg`
    
    return mockUrl
  }

  // Delete photo (mock implementation)
  async deletePhoto(photoUrl: string): Promise<void> {
    // In a real implementation, this would delete from storage service
    await new Promise(resolve => setTimeout(resolve, 500))
    console.log('Photo deleted:', photoUrl)
  }

  // Get photo info from URL
  getPhotoInfo(url: string): { studentId?: string; timestamp?: number; filename?: string } {
    try {
      const urlParts = url.split('/')
      const filename = urlParts[urlParts.length - 1]
      const studentId = urlParts[urlParts.length - 2]
      
      const timestampMatch = filename.match(/_(\d+)\./)
      const timestamp = timestampMatch ? parseInt(timestampMatch[1]) : undefined
      
      return { studentId, timestamp, filename }
    } catch (error) {
      return {}
    }
  }

  // Batch process photos
  async batchProcessPhotos(
    files: File[],
    options: Partial<PhotoCompressionOptions> = {}
  ): Promise<Array<{
    originalFile: File
    processedFile: File
    metadata: PhotoMetadata
    previewUrl: string
    validationResult: PhotoValidationResult
    error?: string
  }>> {
    const results = []
    
    for (const file of files) {
      try {
        const result = await this.processPhotoForUpload(file, options)
        results.push({
          originalFile: file,
          ...result
        })
      } catch (error) {
        results.push({
          originalFile: file,
          processedFile: file,
          metadata: {
            originalName: file.name,
            size: file.size,
            type: file.type,
            dimensions: { width: 0, height: 0 },
            lastModified: file.lastModified
          },
          previewUrl: '',
          validationResult: { valid: false, error: error instanceof Error ? error.message : 'Unknown error' },
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    return results
  }
}

// Export singleton instance
export const photoManager = PhotoManager.getInstance()

// Utility functions
export const validatePhotoFile = (file: File): PhotoValidationResult => {
  return photoManager.validatePhoto(file)
}

export const compressPhoto = async (
  file: File, 
  options?: Partial<PhotoCompressionOptions>
): Promise<{ file: File; metadata: PhotoMetadata }> => {
  return photoManager.compressImage(file, options)
}

export const processPhoto = async (
  file: File,
  options?: Partial<PhotoCompressionOptions>
) => {
  return photoManager.processPhotoForUpload(file, options)
}
