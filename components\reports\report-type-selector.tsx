"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ReportType } from "@/lib/types/reports"
import { 
  FileText, 
  Calendar, 
  Users, 
  BarChart3, 
  ClipboardList,
  TrendingUp,
  BookOpen,
  UserCheck
} from "lucide-react"

interface ReportTypeOption {
  type: ReportType
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string
  features: string[]
  recommended?: boolean
}

const reportTypeOptions: ReportTypeOption[] = [
  {
    type: "SF2",
    title: "SF2 Daily Attendance",
    description: "Official DepEd SF2 Daily Attendance Report for class records",
    icon: ClipboardList,
    badge: "DepEd Official",
    features: [
      "Daily attendance tracking",
      "Teacher signature fields",
      "Class schedule integration",
      "Absence reason coding",
      "Print-ready format"
    ],
    recommended: true
  },
  {
    type: "SF4",
    title: "SF4 Monthly Movement",
    description: "Official DepEd SF4 Monthly Learner's Movement Report",
    icon: TrendingUp,
    badge: "DepEd Official",
    features: [
      "Monthly enrollment summary",
      "Transfer student tracking",
      "Dropout identification",
      "Statistical summaries",
      "Principal review interface"
    ],
    recommended: true
  },
  {
    type: "DAILY",
    title: "Daily Summary Report",
    description: "Comprehensive daily attendance summary for all grades",
    icon: Calendar,
    features: [
      "All grades overview",
      "Real-time statistics",
      "Late arrival tracking",
      "Quick export options",
      "Mobile-friendly format"
    ]
  },
  {
    type: "WEEKLY",
    title: "Weekly Attendance Report",
    description: "Weekly attendance trends and patterns analysis",
    icon: BarChart3,
    features: [
      "7-day attendance trends",
      "Grade-level comparisons",
      "Pattern identification",
      "Statistical analysis",
      "Visual charts included"
    ]
  },
  {
    type: "MONTHLY",
    title: "Monthly Summary Report",
    description: "Comprehensive monthly attendance and performance overview",
    icon: FileText,
    features: [
      "Monthly statistics",
      "Attendance rate trends",
      "Student performance metrics",
      "Comparative analysis",
      "Executive summary"
    ]
  },
  {
    type: "CUSTOM",
    title: "Custom Report Builder",
    description: "Build custom reports with flexible filters and formats",
    icon: Users,
    badge: "Flexible",
    features: [
      "Custom date ranges",
      "Advanced filtering",
      "Multiple export formats",
      "Template saving",
      "Scheduled generation"
    ]
  }
]

interface ReportTypeSelectorProps {
  selectedType?: ReportType
  onTypeSelect: (type: ReportType) => void
  className?: string
}

export function ReportTypeSelector({ 
  selectedType, 
  onTypeSelect, 
  className 
}: ReportTypeSelectorProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Select Report Type</CardTitle>
        <CardDescription>
          Choose the type of report you want to generate
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {reportTypeOptions.map((option) => {
            const Icon = option.icon
            const isSelected = selectedType === option.type
            
            return (
              <Card 
                key={option.type}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  isSelected 
                    ? 'ring-2 ring-primary border-primary' 
                    : 'hover:border-primary/50'
                } ${option.recommended ? 'relative' : ''}`}
                onClick={() => onTypeSelect(option.type)}
              >
                {option.recommended && (
                  <div className="absolute -top-2 -right-2">
                    <Badge variant="default" className="text-xs">
                      Recommended
                    </Badge>
                  </div>
                )}
                
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${
                        isSelected 
                          ? 'bg-primary text-primary-foreground' 
                          : 'bg-muted'
                      }`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-base">{option.title}</CardTitle>
                        {option.badge && (
                          <Badge variant="secondary" className="text-xs mt-1">
                            {option.badge}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <CardDescription className="text-sm">
                    {option.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">
                      Key Features:
                    </h4>
                    <ul className="space-y-1">
                      {option.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="text-xs text-muted-foreground flex items-center gap-2">
                          <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                          {feature}
                        </li>
                      ))}
                      {option.features.length > 3 && (
                        <li className="text-xs text-muted-foreground">
                          +{option.features.length - 3} more features
                        </li>
                      )}
                    </ul>
                  </div>
                  
                  <Button 
                    variant={isSelected ? "default" : "outline"} 
                    size="sm" 
                    className="w-full mt-4"
                    onClick={(e) => {
                      e.stopPropagation()
                      onTypeSelect(option.type)
                    }}
                  >
                    {isSelected ? 'Selected' : 'Select'}
                  </Button>
                </CardContent>
              </Card>
            )
          })}
        </div>
        
        {/* Quick Actions for Popular Reports */}
        <div className="mt-6 pt-6 border-t">
          <h3 className="text-sm font-medium mb-3">Quick Actions</h3>
          <div className="flex flex-wrap gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onTypeSelect("SF2")}
              className="text-xs"
            >
              <ClipboardList className="h-3 w-3 mr-1" />
              Today's SF2
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onTypeSelect("SF4")}
              className="text-xs"
            >
              <TrendingUp className="h-3 w-3 mr-1" />
              This Month's SF4
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onTypeSelect("WEEKLY")}
              className="text-xs"
            >
              <BarChart3 className="h-3 w-3 mr-1" />
              Weekly Summary
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onTypeSelect("CUSTOM")}
              className="text-xs"
            >
              <Users className="h-3 w-3 mr-1" />
              Custom Report
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Helper function to get report type info
export function getReportTypeInfo(type: ReportType): ReportTypeOption | undefined {
  return reportTypeOptions.find(option => option.type === type)
}
