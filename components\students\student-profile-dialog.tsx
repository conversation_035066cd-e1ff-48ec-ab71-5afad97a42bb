"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Eye } from "lucide-react"
import { StudentProfileView } from "./student-profile-view"
import { StudentRegistrationDialog } from "./student-registration-dialog"
import { Student, getFullName } from "@/lib/types/student"
import { StudentRegistrationFormData } from "@/lib/validations/student"
import { toast } from "sonner"

interface StudentProfileDialogProps {
  student: Student
  trigger?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  onStudentUpdated?: (student: Student) => void
}

export function StudentProfileDialog({
  student,
  trigger,
  open,
  onOpenChange,
  onStudentUpdated
}: StudentProfileDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)

  const handleOpenChange = (newOpen: boolean) => {
    if (onOpenChange) {
      onOpenChange(newOpen)
    } else {
      setIsOpen(newOpen)
    }
  }

  const handleEdit = () => {
    setShowEditDialog(true)
  }

  const handleGenerateQR = () => {
    // Simulate QR code generation
    toast.success("QR code generated successfully!")
    console.log("Generating QR code for student:", student.id)
  }

  const handlePrintQR = () => {
    // Simulate QR code printing
    toast.success("QR code sent to printer!")
    console.log("Printing QR code for student:", student.id)
  }

  const handleStudentUpdated = (updatedStudent: Student) => {
    if (onStudentUpdated) {
      onStudentUpdated(updatedStudent)
    }
    setShowEditDialog(false)
    toast.success("Student information updated successfully!")
  }

  const convertStudentToFormData = (student: Student): Partial<StudentRegistrationFormData> => {
    return {
      id: student.id,
      firstName: student.firstName,
      middleName: student.middleName,
      lastName: student.lastName,
      email: student.email,
      dateOfBirth: student.dateOfBirth,
      gender: student.gender,
      course: student.course,
      year: student.year,
      section: student.section,
      grade: student.grade,
      guardianName: student.guardian.name,
      guardianPhone: student.guardian.phone,
      guardianEmail: student.guardian.email,
      guardianRelationship: student.guardian.relationship,
      guardianAddress: student.guardian.address,
      emergencyContacts: student.emergencyContacts,
      street: student.address.street,
      barangay: student.address.barangay,
      city: student.address.city,
      province: student.address.province,
      zipCode: student.address.zipCode,
      country: student.address.country,
      photo: student.photo
    }
  }

  const dialogOpen = open !== undefined ? open : isOpen
  const fullName = getFullName(student)

  return (
    <>
      <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>
        {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
        
        {!trigger && (
          <DialogTrigger asChild>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              View
            </Button>
          </DialogTrigger>
        )}

        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Student Profile - {fullName}</DialogTitle>
            <DialogDescription>
              Complete student information and attendance details
            </DialogDescription>
          </DialogHeader>

          <StudentProfileView
            student={student}
            onEdit={handleEdit}
            onGenerateQR={handleGenerateQR}
            onPrintQR={handlePrintQR}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Student Dialog */}
      <StudentRegistrationDialog
        mode="edit"
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        initialData={convertStudentToFormData(student)}
        onStudentCreated={handleStudentUpdated}
      />
    </>
  )
}

// Quick view component for table rows
export function StudentQuickView({ student }: { student: Student }) {
  return (
    <StudentProfileDialog
      student={student}
      trigger={
        <Button variant="ghost" size="sm">
          <Eye className="h-4 w-4 mr-2" />
          View
        </Button>
      }
    />
  )
}
