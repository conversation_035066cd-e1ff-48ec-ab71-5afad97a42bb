"use client"

import { useEffect, useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SyncStatus } from "@/lib/types/scanner"
import { OfflineManager } from "@/lib/utils/offline-manager"
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Database
} from "lucide-react"
import { cn } from "@/lib/utils"

interface SyncStatusIndicatorProps {
  className?: string
  showDetails?: boolean
}

export function SyncStatusIndicator({ 
  className, 
  showDetails = false 
}: SyncStatusIndicatorProps) {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: true,
    pendingItems: 0,
    isSyncing: false
  })
  const [isExpanded, setIsExpanded] = useState(false)

  useEffect(() => {
    const offlineManager = OfflineManager.getInstance()
    
    const handleStatusUpdate = (status: SyncStatus) => {
      setSyncStatus(status)
    }

    offlineManager.addStatusListener(handleStatusUpdate)

    return () => {
      offlineManager.removeStatusListener(handleStatusUpdate)
    }
  }, [])

  const handleForceSync = async () => {
    try {
      const offlineManager = OfflineManager.getInstance()
      await offlineManager.forceSync()
    } catch (error) {
      console.error('Force sync failed:', error)
    }
  }

  const getStatusColor = () => {
    if (!syncStatus.isOnline) return 'text-red-600'
    if (syncStatus.isSyncing) return 'text-blue-600'
    if (syncStatus.pendingItems > 0) return 'text-yellow-600'
    return 'text-green-600'
  }

  const getStatusIcon = () => {
    if (!syncStatus.isOnline) return <WifiOff className="h-4 w-4" />
    if (syncStatus.isSyncing) return <RefreshCw className="h-4 w-4 animate-spin" />
    if (syncStatus.pendingItems > 0) return <AlertCircle className="h-4 w-4" />
    return <CheckCircle className="h-4 w-4" />
  }

  const getStatusText = () => {
    if (!syncStatus.isOnline) return 'Offline'
    if (syncStatus.isSyncing) return 'Syncing...'
    if (syncStatus.pendingItems > 0) return `${syncStatus.pendingItems} pending`
    return 'Synced'
  }

  if (!showDetails) {
    // Compact indicator
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className={cn("flex items-center gap-1", getStatusColor())}>
          {getStatusIcon()}
          <span className="text-sm font-medium">{getStatusText()}</span>
        </div>
        
        {syncStatus.pendingItems > 0 && syncStatus.isOnline && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleForceSync}
            disabled={syncStatus.isSyncing}
            className="h-6 px-2 text-xs"
          >
            Sync Now
          </Button>
        )}
      </div>
    )
  }

  // Detailed card view
  return (
    <Card className={cn("w-full max-w-sm", className)}>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Sync Status</span>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-6 px-2 text-xs"
            >
              {isExpanded ? 'Less' : 'More'}
            </Button>
          </div>

          {/* Status Overview */}
          <div className="flex items-center justify-between">
            <div className={cn("flex items-center gap-2", getStatusColor())}>
              {getStatusIcon()}
              <span className="text-sm font-medium">{getStatusText()}</span>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant={syncStatus.isOnline ? 'default' : 'destructive'} className="text-xs">
                {syncStatus.isOnline ? 'Online' : 'Offline'}
              </Badge>
              
              {syncStatus.pendingItems > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {syncStatus.pendingItems} pending
                </Badge>
              )}
            </div>
          </div>

          {/* Expanded Details */}
          {isExpanded && (
            <div className="space-y-3 border-t pt-3">
              {/* Last Sync */}
              {syncStatus.lastSync && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Last Sync:</span>
                  <span className="font-medium">
                    {syncStatus.lastSync.toLocaleTimeString()}
                  </span>
                </div>
              )}

              {/* Pending Items */}
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Pending Items:</span>
                <span className="font-medium">{syncStatus.pendingItems}</span>
              </div>

              {/* Sync Error */}
              {syncStatus.syncError && (
                <div className="p-2 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded">
                  <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                    <AlertCircle className="h-4 w-4" />
                    <span className="text-xs font-medium">Sync Error</span>
                  </div>
                  <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                    {syncStatus.syncError}
                  </p>
                </div>
              )}

              {/* Actions */}
              <div className="flex gap-2">
                {syncStatus.pendingItems > 0 && syncStatus.isOnline && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleForceSync}
                    disabled={syncStatus.isSyncing}
                    className="flex-1"
                  >
                    {syncStatus.isSyncing ? (
                      <>
                        <RefreshCw className="mr-2 h-3 w-3 animate-spin" />
                        Syncing...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="mr-2 h-3 w-3" />
                        Sync Now
                      </>
                    )}
                  </Button>
                )}
              </div>

              {/* Offline Message */}
              {!syncStatus.isOnline && (
                <div className="p-2 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded">
                  <div className="flex items-center gap-2 text-yellow-600 dark:text-yellow-400">
                    <Clock className="h-4 w-4" />
                    <span className="text-xs font-medium">Offline Mode</span>
                  </div>
                  <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                    Data will sync automatically when connection is restored
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
