import { z } from "zod"
import { GRADE_LEVELS, GUARDIAN_RELATIONSHIPS, GENDERS } from "@/lib/types/student"

// Emergency contact schema
const emergencyContactSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  phone: z.string().min(10, "Phone number must be at least 10 digits").regex(/^[\+]?[0-9\s\-\(\)]+$/, "Invalid phone number format"),
  relationship: z.string().min(1, "Relationship is required"),
  address: z.string().optional()
})

// Address schema
const addressSchema = z.object({
  street: z.string().min(5, "Street address must be at least 5 characters"),
  barangay: z.string().min(2, "Barangay is required"),
  city: z.string().min(2, "City is required"),
  province: z.string().min(2, "Province is required"),
  zipCode: z.string().min(4, "ZIP code must be at least 4 characters").max(10, "ZIP code must be at most 10 characters"),
  country: z.string().default("Philippines")
})

// Guardian schema
const guardianSchema = z.object({
  name: z.string().min(2, "Guardian name must be at least 2 characters"),
  phone: z.string().min(10, "Phone number must be at least 10 digits").regex(/^[\+]?[0-9\s\-\(\)]+$/, "Invalid phone number format"),
  email: z.string().email("Invalid email format").optional().or(z.literal("")),
  relationship: z.enum(GUARDIAN_RELATIONSHIPS, {
    errorMap: () => ({ message: "Please select a valid relationship" })
  }),
  address: z.string().optional()
})

// Main student registration schema
export const studentRegistrationSchema = z.object({
  // Basic Information
  id: z.string()
    .min(12, "DepEd ID must be 12 digits")
    .max(12, "DepEd ID must be 12 digits")
    .regex(/^\d{12}$/, "DepEd ID must contain only numbers"),
  firstName: z.string().min(2, "First name must be at least 2 characters").max(50, "First name must be at most 50 characters"),
  middleName: z.string().max(50, "Middle name must be at most 50 characters").optional(),
  lastName: z.string().min(2, "Last name must be at least 2 characters").max(50, "Last name must be at most 50 characters"),
  email: z.string().email("Invalid email format"),
  dateOfBirth: z.string().optional(),
  gender: z.enum(GENDERS).optional(),
  
  // Academic Information
  course: z.string().min(2, "Course is required"),
  year: z.string().min(1, "Year level is required"),
  section: z.string().optional(),
  grade: z.enum(GRADE_LEVELS, {
    errorMap: () => ({ message: "Please select a valid grade level" })
  }),
  
  // Guardian Information
  guardianName: z.string().min(2, "Guardian name must be at least 2 characters"),
  guardianPhone: z.string().min(10, "Phone number must be at least 10 digits").regex(/^[\+]?[0-9\s\-\(\)]+$/, "Invalid phone number format"),
  guardianEmail: z.string().email("Invalid email format").optional().or(z.literal("")),
  guardianRelationship: z.enum(GUARDIAN_RELATIONSHIPS, {
    errorMap: () => ({ message: "Please select a valid relationship" })
  }),
  guardianAddress: z.string().optional(),
  
  // Emergency Contacts
  emergencyContacts: z.array(emergencyContactSchema).min(1, "At least one emergency contact is required").max(3, "Maximum 3 emergency contacts allowed"),
  
  // Address Information
  street: z.string().min(5, "Street address must be at least 5 characters"),
  barangay: z.string().min(2, "Barangay is required"),
  city: z.string().min(2, "City is required"),
  province: z.string().min(2, "Province is required"),
  zipCode: z.string().min(4, "ZIP code must be at least 4 characters").max(10, "ZIP code must be at most 10 characters"),
  country: z.string().default("Philippines"),
  
  // Photo (optional for now)
  photo: z.any().optional()
})

// Student update schema (allows partial updates)
export const studentUpdateSchema = studentRegistrationSchema.partial().extend({
  id: z.string().min(12, "DepEd ID must be 12 digits").max(12, "DepEd ID must be 12 digits").regex(/^\d{12}$/, "DepEd ID must contain only numbers")
})

// Quick registration schema (minimal required fields)
export const quickStudentSchema = z.object({
  id: z.string().min(12, "DepEd ID must be 12 digits").max(12, "DepEd ID must be 12 digits").regex(/^\d{12}$/, "DepEd ID must contain only numbers"),
  firstName: z.string().min(2, "First name is required"),
  lastName: z.string().min(2, "Last name is required"),
  email: z.string().email("Invalid email format"),
  grade: z.enum(GRADE_LEVELS),
  course: z.string().min(2, "Course is required"),
  year: z.string().min(1, "Year level is required"),
  guardianName: z.string().min(2, "Guardian name is required"),
  guardianPhone: z.string().min(10, "Guardian phone is required").regex(/^[\+]?[0-9\s\-\(\)]+$/, "Invalid phone number format"),
  guardianRelationship: z.enum(GUARDIAN_RELATIONSHIPS)
})

// Bulk import schema
export const bulkImportSchema = z.array(studentRegistrationSchema)

// Export types
export type StudentRegistrationFormData = z.infer<typeof studentRegistrationSchema>
export type StudentUpdateFormData = z.infer<typeof studentUpdateSchema>
export type QuickStudentFormData = z.infer<typeof quickStudentSchema>
export type BulkImportData = z.infer<typeof bulkImportSchema>

// Validation helpers
export const validateDepEdId = (id: string): boolean => {
  return /^\d{12}$/.test(id)
}

export const validatePhoneNumber = (phone: string): boolean => {
  return /^[\+]?[0-9\s\-\(\)]+$/.test(phone) && phone.replace(/\D/g, '').length >= 10
}

export const validateEmail = (email: string): boolean => {
  return z.string().email().safeParse(email).success
}

// Default form values
export const getDefaultFormValues = (): Partial<StudentRegistrationFormData> => ({
  country: "Philippines",
  province: "Batangas",
  city: "Tanauan City",
  emergencyContacts: [
    {
      name: "",
      phone: "",
      relationship: "",
      address: ""
    }
  ]
})
