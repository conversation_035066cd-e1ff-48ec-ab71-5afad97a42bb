// Re-export Prisma generated types
export * from '../generated/prisma'

// Import Prisma types for custom type definitions
import type {
  Student as PrismaStudent,
  Teacher as PrismaTeacher,
  Subject as PrismaSubject,
  Attendance as PrismaAttendance,
  AttendancePattern as PrismaAttendancePattern,
  StudentRiskAssessment as PrismaStudentRiskAssessment,
  SMSLog as PrismaSMSLog,
  NotificationQueue as PrismaNotificationQueue,
  AuditLog as PrismaAuditLog,
  SystemSettings as PrismaSystemSettings,
  Gender,
  GradeLevel,
  StudentStatus,
  GuardianRelationship,
  TeacherRole,
  EmployeeStatus,
  AttendanceType,
  AttendanceStatus,
  RiskLevel,
  SMSStatus,
  MessageType,
  NotificationPriority,
  NotificationStatus
} from '../generated/prisma'

// Enhanced types with computed properties and additional functionality
export interface StudentWithComputed extends PrismaStudent {
  fullName: string
  age?: number
  attendanceStats?: {
    totalDays: number
    presentDays: number
    lateDays: number
    absentDays: number
    attendanceRate: number
    lastAttendance?: string
  }
}

export interface TeacherWithComputed extends PrismaTeacher {
  fullName: string
  subjectsList?: string[]
  gradeLevelsList?: GradeLevel[]
  sectionsList?: string[]
}

export interface AttendanceWithRelations extends PrismaAttendance {
  student: PrismaStudent
  subject?: PrismaSubject
  scannedByTeacher?: PrismaTeacher
}

// Legacy compatibility types (for existing components)
export interface Guardian {
  name: string
  phone: string
  email?: string
  relationship: 'Father' | 'Mother' | 'Guardian' | 'Grandparent' | 'Sibling' | 'Other'
  address?: string
}

export interface EmergencyContact {
  name: string
  phone: string
  relationship: string
  address?: string
}

export interface Address {
  street: string
  barangay: string
  city: string
  province: string
  zipCode: string
  country?: string
}

// Utility functions for type conversion
export const convertPrismaStudentToLegacy = (student: PrismaStudent): StudentWithComputed => {
  return {
    ...student,
    fullName: [student.firstName, student.middleName, student.lastName].filter(Boolean).join(' '),
    age: student.dateOfBirth ? Math.floor((Date.now() - student.dateOfBirth.getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : undefined
  }
}

export const convertPrismaTeacherToLegacy = (teacher: PrismaTeacher): TeacherWithComputed => {
  return {
    ...teacher,
    fullName: [teacher.firstName, teacher.middleName, teacher.lastName].filter(Boolean).join(' '),
    subjectsList: teacher.subjects ? JSON.parse(teacher.subjects) : [],
    gradeLevelsList: teacher.gradeLevels ? JSON.parse(teacher.gradeLevels) : [],
    sectionsList: teacher.sections ? JSON.parse(teacher.sections) : []
  }
}

// Form types for creating/updating records
export interface StudentFormData {
  // Basic Information
  studentId: string
  firstName: string
  middleName?: string
  lastName: string
  email?: string
  dateOfBirth?: Date
  gender?: Gender
  
  // Academic Information
  course: string
  year: string
  section?: string
  gradeLevel: GradeLevel
  
  // Guardian Information
  guardianName: string
  guardianPhone: string
  guardianEmail?: string
  guardianRelationship: GuardianRelationship
  
  // Emergency Contact
  emergencyContactName: string
  emergencyContactPhone: string
  emergencyContactRelationship?: string
  
  // Address Information
  address: string
  barangay?: string
  municipality: string
  province: string
  zipCode?: string
  
  // Status
  status: StudentStatus
  
  // Photo
  photoUrl?: string
}

export interface TeacherFormData {
  employeeId: string
  firstName: string
  middleName?: string
  lastName: string
  email?: string
  phoneNumber?: string
  role: TeacherRole
  subjects?: string[]
  gradeLevels?: GradeLevel[]
  sections?: string[]
  status: EmployeeStatus
  hireDate?: Date
}

export interface AttendanceFormData {
  studentId: string
  date: Date
  timeIn?: Date
  timeOut?: Date
  attendanceType: AttendanceType
  subjectId?: string
  status: AttendanceStatus
  scannedBy?: string
  notes?: string
  isManualEntry: boolean
  location?: string
  deviceId?: string
}

// Filter and search types
export interface StudentFilters {
  search?: string
  gradeLevel?: GradeLevel[]
  section?: string[]
  status?: StudentStatus[]
  course?: string[]
  year?: string[]
}

export interface TeacherFilters {
  search?: string
  role?: TeacherRole[]
  status?: EmployeeStatus[]
  subjects?: string[]
  gradeLevels?: GradeLevel[]
}

export interface AttendanceFilters {
  studentId?: string
  dateFrom?: Date
  dateTo?: Date
  attendanceType?: AttendanceType[]
  status?: AttendanceStatus[]
  subjectId?: string
}

// Pagination
export interface PaginationConfig {
  page: number
  pageSize: number
  total: number
}

// API Response types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface StudentListResponse {
  students: StudentWithComputed[]
  pagination: PaginationConfig
  filters: StudentFilters
}

export interface TeacherListResponse {
  teachers: TeacherWithComputed[]
  pagination: PaginationConfig
  filters: TeacherFilters
}

export interface AttendanceListResponse {
  attendance: AttendanceWithRelations[]
  pagination: PaginationConfig
  filters: AttendanceFilters
}

// Constants
export const GRADE_LEVELS = ['GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12'] as const
export const STUDENT_STATUSES = ['ACTIVE', 'INACTIVE', 'TRANSFERRED', 'GRADUATED', 'DROPPED'] as const
export const GUARDIAN_RELATIONSHIPS = ['FATHER', 'MOTHER', 'GUARDIAN', 'GRANDPARENT', 'SIBLING', 'AUNT', 'UNCLE', 'OTHER'] as const
export const GENDERS = ['MALE', 'FEMALE'] as const
export const TEACHER_ROLES = ['TEACHER', 'DEPARTMENT_HEAD', 'GUIDANCE_COUNSELOR', 'ADMIN', 'PRINCIPAL', 'VICE_PRINCIPAL'] as const
export const EMPLOYEE_STATUSES = ['ACTIVE', 'INACTIVE', 'RESIGNED', 'TERMINATED', 'RETIRED'] as const
export const ATTENDANCE_TYPES = ['GATE', 'SUBJECT', 'EVENT', 'ASSEMBLY'] as const
export const ATTENDANCE_STATUSES = ['PRESENT', 'ABSENT', 'LATE', 'EXCUSED', 'HALF_DAY'] as const
export const RISK_LEVELS = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'] as const
export const SMS_STATUSES = ['PENDING', 'SENT', 'DELIVERED', 'FAILED', 'EXPIRED'] as const
export const MESSAGE_TYPES = ['ATTENDANCE_ALERT', 'LATE_ARRIVAL', 'RISK_WARNING', 'GENERAL', 'EMERGENCY', 'PARENT_MEETING', 'ACADEMIC_UPDATE'] as const

// Default values
export const DEFAULT_STUDENT: Partial<StudentFormData> = {
  status: 'ACTIVE',
  municipality: 'Tanauan',
  province: 'Leyte',
  year: '2024-2025'
}

export const DEFAULT_TEACHER: Partial<TeacherFormData> = {
  role: 'TEACHER',
  status: 'ACTIVE'
}

export const DEFAULT_PAGINATION: PaginationConfig = {
  page: 1,
  pageSize: 20,
  total: 0
}
