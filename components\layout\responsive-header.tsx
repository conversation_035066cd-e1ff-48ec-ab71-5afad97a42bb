"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { NotificationCenter, SystemHealthIndicator } from "@/components/dashboard/notification-center"
import { CompactWeatherWidget } from "@/components/dashboard/weather-widget"
import { AppSidebar } from "@/components/layout/sidebar"
import { 
  Menu, 
  Search, 
  Settings, 
  User, 
  LogOut,
  Moon,
  Sun
} from "lucide-react"
import { useTheme } from "next-themes"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"

export function ResponsiveHeader() {
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const { theme, setTheme } = useTheme()

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between px-4">
        {/* Left Section - Mobile Menu & Logo */}
        <div className="flex items-center space-x-4">
          {/* Mobile Menu */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-72 p-0">
              <AppSidebar />
            </SheetContent>
          </Sheet>

          {/* Logo/Brand */}
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">QR</span>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-lg font-semibold">QRSAMS</h1>
              <p className="text-xs text-muted-foreground">Attendance System</p>
            </div>
          </div>
        </div>

        {/* Center Section - Search (Desktop) */}
        <div className="hidden md:flex flex-1 max-w-md mx-4">
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search students, classes, or records..."
              className="pl-10 pr-4"
            />
          </div>
        </div>

        {/* Right Section - Actions & User Menu */}
        <div className="flex items-center space-x-2">
          {/* Mobile Search */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={() => setIsSearchOpen(!isSearchOpen)}
          >
            <Search className="h-5 w-5" />
          </Button>

          {/* Weather Widget (Hidden on mobile) */}
          <div className="hidden lg:block">
            <CompactWeatherWidget />
          </div>

          {/* Theme Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setTheme(theme === "light" ? "dark" : "light")}
          >
            <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            <span className="sr-only">Toggle theme</span>
          </Button>

          {/* Notifications */}
          <NotificationCenter />

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                <Avatar className="h-9 w-9">
                  <AvatarImage src="/avatars/admin.jpg" alt="Admin" />
                  <AvatarFallback>AD</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">Admin User</p>
                  <p className="text-xs leading-none text-muted-foreground">
                    <EMAIL>
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Mobile Search Bar */}
      {isSearchOpen && (
        <div className="border-t px-4 py-3 md:hidden">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search students, classes, or records..."
              className="pl-10 pr-4"
              autoFocus
            />
          </div>
        </div>
      )}
    </header>
  )
}

// Touch-friendly button component for mobile
export function TouchButton({ 
  children, 
  className, 
  ...props 
}: React.ComponentProps<typeof Button>) {
  return (
    <Button
      className={`min-h-[44px] min-w-[44px] touch-manipulation ${className}`}
      {...props}
    >
      {children}
    </Button>
  )
}

// Responsive card component that adapts to screen size
export function ResponsiveCard({ 
  children, 
  className,
  mobileFullWidth = false,
  ...props 
}: React.ComponentProps<"div"> & { mobileFullWidth?: boolean }) {
  return (
    <div
      className={`
        rounded-lg border bg-card text-card-foreground shadow-sm
        ${mobileFullWidth ? 'mx-0 sm:mx-auto' : ''}
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  )
}

// Responsive grid component
export function ResponsiveGrid({ 
  children, 
  className,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  ...props 
}: React.ComponentProps<"div"> & { 
  cols?: { mobile: number; tablet: number; desktop: number } 
}) {
  const gridClasses = `
    grid gap-4
    grid-cols-${cols.mobile}
    md:grid-cols-${cols.tablet}
    lg:grid-cols-${cols.desktop}
    ${className}
  `

  return (
    <div className={gridClasses} {...props}>
      {children}
    </div>
  )
}

// Loading skeleton for better UX
export function LoadingSkeleton({ className }: { className?: string }) {
  return (
    <div className={`animate-pulse bg-muted rounded ${className}`}>
      <div className="h-4 bg-muted-foreground/20 rounded w-3/4 mb-2"></div>
      <div className="h-4 bg-muted-foreground/20 rounded w-1/2"></div>
    </div>
  )
}

// Error boundary component
export function ErrorBoundary({ 
  children, 
  fallback 
}: { 
  children: React.ReactNode
  fallback?: React.ReactNode 
}) {
  return (
    <div className="min-h-[200px] flex items-center justify-center">
      {fallback || (
        <div className="text-center">
          <div className="text-muted-foreground mb-2">Something went wrong</div>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Retry
          </Button>
        </div>
      )}
    </div>
  )
}
