"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { SF2Report, ReportConfig, TeacherInfo } from "@/lib/types/reports"
import { schoolInfo, mockTeachers } from "@/lib/data/reports-mock-data"
import { generateSF2Report } from "@/lib/utils/report-utils"
import { mockStudents, mockAttendanceRecords } from "@/lib/data/mock-data"
import { 
  FileText, 
  Calendar, 
  User, 
  School, 
  Clock,
  CheckCircle,
  AlertCircle,
  Download,
  Eye,
  Printer
} from "lucide-react"
import { format } from "date-fns"

interface SF2ReportGeneratorProps {
  config: ReportConfig
  onGenerate: (report: SF2Report) => void
  onPreview: (report: SF2Report) => void
  className?: string
}

export function SF2ReportGenerator({ 
  config, 
  onGenerate, 
  onPreview, 
  className 
}: SF2ReportGeneratorProps) {
  const [selectedTeacher, setSelectedTeacher] = useState<TeacherInfo>(mockTeachers[0])
  const [selectedSubject, setSelectedSubject] = useState("")
  const [classSchedule, setClassSchedule] = useState({
    startTime: "08:00",
    endTime: "09:00",
    room: "Room 101"
  })
  const [reportSettings, setReportSettings] = useState({
    includeSignatures: true,
    includeRemarks: true,
    includeAbsenteeReasons: true,
    showStatistics: true
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedReport, setGeneratedReport] = useState<SF2Report | null>(null)

  // Mock subjects for the selected grade/section
  const subjects = [
    "Mathematics",
    "English",
    "Science", 
    "Filipino",
    "Social Studies",
    "Physical Education",
    "Technology and Livelihood Education",
    "Arts",
    "Music"
  ]

  const handleGenerateReport = async () => {
    setIsGenerating(true)
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Generate the SF2 report
      const report = generateSF2Report(
        config,
        mockStudents,
        mockAttendanceRecords as any[]
      )
      
      // Add additional SF2-specific data
      const enhancedReport: SF2Report = {
        ...report,
        subject: selectedSubject,
        teacher: {
          ...selectedTeacher,
          dateChecked: format(new Date(), "yyyy-MM-dd")
        }
      }
      
      setGeneratedReport(enhancedReport)
      onGenerate(enhancedReport)
    } catch (error) {
      console.error("Error generating SF2 report:", error)
    } finally {
      setIsGenerating(false)
    }
  }

  const handlePreviewReport = () => {
    if (generatedReport) {
      onPreview(generatedReport)
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* SF2 Report Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <CardTitle>SF2 Daily Attendance Report</CardTitle>
                <CardDescription>
                  Official DepEd School Form 2 - Daily Attendance Report of Learners
                </CardDescription>
              </div>
            </div>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              DepEd Official
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* School Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <School className="h-5 w-5" />
            School Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">School Name</Label>
              <p className="text-sm text-muted-foreground">{schoolInfo.schoolName}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">School ID</Label>
              <p className="text-sm text-muted-foreground">{schoolInfo.schoolId}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Division</Label>
              <p className="text-sm text-muted-foreground">{schoolInfo.division}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Region</Label>
              <p className="text-sm text-muted-foreground">{schoolInfo.region}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Class Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Class Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="teacher-select">Teacher</Label>
              <Select 
                value={selectedTeacher.id} 
                onValueChange={(value) => {
                  const teacher = mockTeachers.find(t => t.id === value)
                  if (teacher) setSelectedTeacher(teacher)
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select teacher" />
                </SelectTrigger>
                <SelectContent>
                  {mockTeachers.map((teacher) => (
                    <SelectItem key={teacher.id} value={teacher.id}>
                      {teacher.name} - {teacher.position}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="subject-select">Subject</Label>
              <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map((subject) => (
                    <SelectItem key={subject} value={subject}>
                      {subject}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Grade & Section</Label>
              <p className="text-sm text-muted-foreground">
                {config.filters.grades?.join(", ") || "All Grades"} - {config.filters.sections?.join(", ") || "All Sections"}
              </p>
            </div>

            <div className="space-y-2">
              <Label>Date</Label>
              <p className="text-sm text-muted-foreground">
                {format(new Date(config.dateRange.startDate), "MMMM dd, yyyy")}
              </p>
            </div>
          </div>

          <Separator />

          {/* Class Schedule */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Class Schedule</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start-time" className="text-xs">Start Time</Label>
                <Input
                  id="start-time"
                  type="time"
                  value={classSchedule.startTime}
                  onChange={(e) => setClassSchedule({...classSchedule, startTime: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end-time" className="text-xs">End Time</Label>
                <Input
                  id="end-time"
                  type="time"
                  value={classSchedule.endTime}
                  onChange={(e) => setClassSchedule({...classSchedule, endTime: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="room" className="text-xs">Room</Label>
                <Input
                  id="room"
                  value={classSchedule.room}
                  onChange={(e) => setClassSchedule({...classSchedule, room: e.target.value})}
                  placeholder="Room number"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Report Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-signatures"
                checked={reportSettings.includeSignatures}
                onCheckedChange={(checked) => 
                  setReportSettings({...reportSettings, includeSignatures: checked as boolean})
                }
              />
              <Label htmlFor="include-signatures" className="text-sm">
                Include teacher signature fields
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-remarks"
                checked={reportSettings.includeRemarks}
                onCheckedChange={(checked) => 
                  setReportSettings({...reportSettings, includeRemarks: checked as boolean})
                }
              />
              <Label htmlFor="include-remarks" className="text-sm">
                Include remarks column
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-reasons"
                checked={reportSettings.includeAbsenteeReasons}
                onCheckedChange={(checked) => 
                  setReportSettings({...reportSettings, includeAbsenteeReasons: checked as boolean})
                }
              />
              <Label htmlFor="include-reasons" className="text-sm">
                Include absence reason coding
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="show-statistics"
                checked={reportSettings.showStatistics}
                onCheckedChange={(checked) => 
                  setReportSettings({...reportSettings, showStatistics: checked as boolean})
                }
              />
              <Label htmlFor="show-statistics" className="text-sm">
                Show attendance statistics summary
              </Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generation Actions */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h3 className="text-lg font-medium">Generate SF2 Report</h3>
              <p className="text-sm text-muted-foreground">
                Create official DepEd SF2 Daily Attendance Report
              </p>
            </div>
            <div className="flex items-center gap-2">
              {generatedReport && (
                <>
                  <Button variant="outline" onClick={handlePreviewReport}>
                    <Eye className="mr-2 h-4 w-4" />
                    Preview
                  </Button>
                  <Button variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                  <Button variant="outline">
                    <Printer className="mr-2 h-4 w-4" />
                    Print
                  </Button>
                </>
              )}
              <Button 
                onClick={handleGenerateReport} 
                disabled={isGenerating || !selectedSubject}
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <FileText className="mr-2 h-4 w-4" />
                    Generate SF2
                  </>
                )}
              </Button>
            </div>
          </div>

          {!selectedSubject && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-800">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">Please select a subject to generate the SF2 report</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
