import { DefaultSession } from "next-auth"
import { UserRole, SecurityEventType, SecuritySeverity } from "@/lib/generated/prisma"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      role: UserRole
      permissions?: string[]
      isActive: boolean
      mustChangePassword: boolean
    } & DefaultSession["user"]
  }

  interface User {
    id: string
    role: UserRole
    permissions?: string[]
    isActive: boolean
    mustChangePassword: boolean
  }
}

// Authentication types
export interface AuthUser {
  id: string
  username: string
  email: string
  firstName: string
  lastName: string
  middleName?: string
  role: UserRole
  permissions?: string[]
  isActive: boolean
  lastLoginAt?: Date
  mustChangePassword: boolean
  twoFactorEnabled: boolean
  phoneNumber?: string
  department?: string
  position?: string
}

export interface LoginCredentials {
  username: string
  password: string
  twoFactorCode?: string
}

export interface LoginResult {
  success: boolean
  user?: AuthUser
  error?: string
  requiresTwoFactor?: boolean
  mustChangePassword?: boolean
  accountLocked?: boolean
  lockoutUntil?: Date
}

export interface PasswordChangeRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface CreateUserRequest {
  username: string
  email: string
  firstName: string
  lastName: string
  middleName?: string
  role: UserRole
  permissions?: string[]
  phoneNumber?: string
  department?: string
  position?: string
  temporaryPassword?: string
  mustChangePassword?: boolean
}

export interface UpdateUserRequest {
  firstName?: string
  lastName?: string
  middleName?: string
  email?: string
  phoneNumber?: string
  department?: string
  position?: string
  role?: UserRole
  permissions?: string[]
  isActive?: boolean
}

// Permission types
export interface Permission {
  id: string
  name: string
  description: string
  category: string
  resource: string
  action: string
}

export interface RolePermissions {
  role: UserRole
  permissions: Permission[]
}

// Security types
export interface SecurityEvent {
  id: string
  userId?: string
  eventType: SecurityEventType
  severity: SecuritySeverity
  description: string
  ipAddress?: string
  userAgent?: string
  additionalData?: Record<string, any>
  resolved: boolean
  resolvedAt?: Date
  resolvedBy?: string
  resolution?: string
  createdAt: Date
}

export interface LoginAttempt {
  id: string
  userId: string
  loginAt: Date
  ipAddress: string
  userAgent?: string
  location?: string
  success: boolean
  failureReason?: string
  sessionId?: string
  sessionDuration?: number
  logoutAt?: Date
  logoutReason?: string
  deviceType?: string
  browser?: string
  operatingSystem?: string
}

// Session types
export interface SessionInfo {
  id: string
  userId: string
  token: string
  expiresAt: Date
  ipAddress: string
  userAgent?: string
  isActive: boolean
  lastActivity: Date
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Validation types
export interface PasswordValidation {
  isValid: boolean
  errors: string[]
  strength: 'weak' | 'medium' | 'strong'
  score: number
}

export interface UserValidation {
  isValid: boolean
  errors: Record<string, string[]>
}

// Audit types
export interface AuditLogEntry {
  id: string
  userId?: string
  action: string
  entityType: string
  entityId?: string
  oldValues?: Record<string, any>
  newValues?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  createdAt: Date
}

// Rate limiting types
export interface RateLimitInfo {
  limit: number
  remaining: number
  resetTime: Date
  blocked: boolean
}

// Export Prisma enums for convenience
export { UserRole, SecurityEventType, SecuritySeverity }
