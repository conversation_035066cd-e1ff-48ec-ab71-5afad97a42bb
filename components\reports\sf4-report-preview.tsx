"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ead<PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Separator } from "@/components/ui/separator"
import { SF4Report, ExportFormat } from "@/lib/types/reports"
import { ReportExportManager } from "@/lib/utils/export-utils"
import { 
  TrendingUp, 
  Download, 
  Printer, 
  School,
  Calendar,
  Users,
  UserPlus,
  UserMinus,
  UserX,
  BarChart3,
  CheckCircle,
  XCircle
} from "lucide-react"
import { format } from "date-fns"

interface SF4ReportPreviewProps {
  report: SF4Report
  onDownload?: (format: ExportFormat) => void
  onPrint?: () => void
  className?: string
}

export function SF4ReportPreview({
  report,
  onDownload,
  onPrint,
  className
}: SF4ReportPreviewProps) {
  const handleDownload = async (format: ExportFormat) => {
    try {
      const { blob, filename } = await ReportExportManager.exportReport(report, format)
      ReportExportManager.downloadFile(blob, filename)
      onDownload?.(format)
    } catch (error) {
      console.error('Export failed:', error)
    }
  }
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <TrendingUp className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold">SF4 Report Preview</h2>
            <p className="text-sm text-muted-foreground">
              Monthly Learner's Movement - {report.month} {report.year}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => handleDownload('EXCEL')}>
            <Download className="mr-2 h-4 w-4" />
            Excel
          </Button>
          <Button variant="outline" onClick={() => handleDownload('PDF')}>
            <Download className="mr-2 h-4 w-4" />
            PDF
          </Button>
          <Button variant="outline" onClick={() => handleDownload('CSV')}>
            <Download className="mr-2 h-4 w-4" />
            CSV
          </Button>
          <Button onClick={onPrint}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      {/* SF4 Form Preview */}
      <Card className="print:shadow-none print:border-none">
        <CardContent className="p-8 space-y-6">
          {/* Official Header */}
          <div className="text-center space-y-2">
            <h1 className="text-lg font-bold">Republic of the Philippines</h1>
            <h2 className="text-base font-semibold">Department of Education</h2>
            <h3 className="text-base font-semibold">{report.schoolInfo.region}</h3>
            <h4 className="text-base font-semibold">{report.schoolInfo.division}</h4>
            <h5 className="text-base font-semibold">{report.schoolInfo.schoolName}</h5>
            <div className="pt-4">
              <h2 className="text-xl font-bold">SCHOOL FORM 4 (SF4)</h2>
              <h3 className="text-lg font-semibold">MONTHLY REPORT ON LEARNER'S MOVEMENT</h3>
            </div>
          </div>

          <Separator />

          {/* School and Report Information */}
          <div className="grid grid-cols-2 gap-8">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="font-medium">School:</span>
                <span className="underline">{report.schoolInfo.schoolName}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">School ID:</span>
                <span className="underline">{report.schoolInfo.schoolId}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Division:</span>
                <span className="underline">{report.schoolInfo.division}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Region:</span>
                <span className="underline">{report.schoolInfo.region}</span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="font-medium">Grade & Section:</span>
                <span className="underline">Grade {report.grade} {report.section ? `- ${report.section}` : '(All Sections)'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Month:</span>
                <span className="underline">{report.month}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Year:</span>
                <span className="underline">{report.year}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">School Year:</span>
                <span className="underline">{report.schoolInfo.schoolYear}</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Enrollment Summary */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">ENROLLMENT SUMMARY</h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{report.enrollment.beginningOfMonth}</div>
                <div className="text-sm text-muted-foreground">Beginning of Month</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-green-600">{report.enrollment.newAdmissions.length}</div>
                <div className="text-sm text-muted-foreground">New Admissions</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{report.enrollment.transfers.transferredOut.length}</div>
                <div className="text-sm text-muted-foreground">Transfers Out</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{report.enrollment.endOfMonth}</div>
                <div className="text-sm text-muted-foreground">End of Month</div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Movement Details */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-center">LEARNER MOVEMENT DETAILS</h3>

            {/* New Admissions */}
            {report.enrollment.newAdmissions.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-green-600 flex items-center gap-2">
                  <UserPlus className="h-4 w-4" />
                  New Admissions ({report.enrollment.newAdmissions.length})
                </h4>
                <div className="border rounded-lg overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-green-50">
                        <TableHead>Student Name</TableHead>
                        <TableHead>Date of Admission</TableHead>
                        <TableHead>Reason</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {report.enrollment.newAdmissions.map((student, index) => (
                        <TableRow key={index}>
                          <TableCell>{student.name}</TableCell>
                          <TableCell>{format(new Date(student.dateOfAction), "MMM dd, yyyy")}</TableCell>
                          <TableCell>{student.reason}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}

            {/* Transfers Out */}
            {report.enrollment.transfers.transferredOut.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-orange-600 flex items-center gap-2">
                  <UserMinus className="h-4 w-4" />
                  Transfers Out ({report.enrollment.transfers.transferredOut.length})
                </h4>
                <div className="border rounded-lg overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-orange-50">
                        <TableHead>Student Name</TableHead>
                        <TableHead>Date of Transfer</TableHead>
                        <TableHead>Destination</TableHead>
                        <TableHead>Reason</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {report.enrollment.transfers.transferredOut.map((student, index) => (
                        <TableRow key={index}>
                          <TableCell>{student.name}</TableCell>
                          <TableCell>{format(new Date(student.dateOfAction), "MMM dd, yyyy")}</TableCell>
                          <TableCell>{student.destination || 'Not specified'}</TableCell>
                          <TableCell>{student.reason}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}

            {/* Dropouts */}
            {report.enrollment.dropouts.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-red-600 flex items-center gap-2">
                  <UserX className="h-4 w-4" />
                  Dropouts ({report.enrollment.dropouts.length})
                </h4>
                <div className="border rounded-lg overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-red-50">
                        <TableHead>Student Name</TableHead>
                        <TableHead>Date of Dropout</TableHead>
                        <TableHead>Reason</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {report.enrollment.dropouts.map((student, index) => (
                        <TableRow key={index}>
                          <TableCell>{student.name}</TableCell>
                          <TableCell>{format(new Date(student.dateOfAction), "MMM dd, yyyy")}</TableCell>
                          <TableCell>{student.reason}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* Attendance Statistics */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">ATTENDANCE STATISTICS</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{report.attendance.totalSchoolDays}</div>
                <div className="text-sm text-muted-foreground">Total School Days</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-green-600">{report.attendance.averageAttendance}</div>
                <div className="text-sm text-muted-foreground">Average Daily Attendance</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{report.attendance.attendanceRate.toFixed(1)}%</div>
                <div className="text-sm text-muted-foreground">Attendance Rate</div>
              </div>
            </div>

            {/* Attendance Lists */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Perfect Attendance */}
              <div className="space-y-2">
                <h4 className="font-medium text-green-600 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Perfect Attendance ({report.attendance.perfectAttendance.length})
                </h4>
                <div className="p-3 bg-green-50 rounded-lg">
                  {report.attendance.perfectAttendance.length > 0 ? (
                    <ul className="text-sm space-y-1">
                      {report.attendance.perfectAttendance.slice(0, 5).map((studentId, index) => (
                        <li key={index}>• Student ID: {studentId}</li>
                      ))}
                      {report.attendance.perfectAttendance.length > 5 && (
                        <li className="text-muted-foreground">
                          ... and {report.attendance.perfectAttendance.length - 5} more
                        </li>
                      )}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">No students with perfect attendance</p>
                  )}
                </div>
              </div>

              {/* Chronic Absentees */}
              <div className="space-y-2">
                <h4 className="font-medium text-red-600 flex items-center gap-2">
                  <XCircle className="h-4 w-4" />
                  Chronic Absentees ({report.attendance.chronicAbsentees.length})
                </h4>
                <div className="p-3 bg-red-50 rounded-lg">
                  {report.attendance.chronicAbsentees.length > 0 ? (
                    <ul className="text-sm space-y-1">
                      {report.attendance.chronicAbsentees.slice(0, 5).map((studentId, index) => (
                        <li key={index}>• Student ID: {studentId}</li>
                      ))}
                      {report.attendance.chronicAbsentees.length > 5 && (
                        <li className="text-muted-foreground">
                          ... and {report.attendance.chronicAbsentees.length - 5} more
                        </li>
                      )}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">No chronic absentees identified</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Principal Review Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">PRINCIPAL'S REVIEW</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Reviewed By:</Label>
                  <p className="text-sm">{report.principalReview.reviewedBy}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Review Date:</Label>
                  <p className="text-sm">
                    {report.principalReview.reviewDate ? 
                      format(new Date(report.principalReview.reviewDate), "MMMM dd, yyyy") : 
                      'Not reviewed'
                    }
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Status:</Label>
                  <Badge 
                    variant={report.principalReview.approved ? "default" : "secondary"}
                    className="ml-2"
                  >
                    {report.principalReview.approved ? "Approved" : "Pending Review"}
                  </Badge>
                </div>
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium">Remarks:</Label>
                <div className="p-3 bg-gray-50 rounded-lg min-h-[100px]">
                  <p className="text-sm">
                    {report.principalReview.remarks || "No remarks provided"}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Signature Section */}
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="text-center">
                  <div className="border-b border-black w-48 mx-auto mb-2 h-12"></div>
                  <div className="text-sm font-medium">Prepared by</div>
                  <div className="text-xs text-muted-foreground">Class Adviser/Teacher</div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="text-center">
                  <div className="border-b border-black w-48 mx-auto mb-2 h-12"></div>
                  <div className="text-sm font-medium">Date</div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <div className="border-b border-black w-64 mx-auto mb-2 h-12"></div>
              <div className="text-sm font-medium">Principal's Signature</div>
              <div className="text-xs text-muted-foreground">{report.schoolInfo.principalName}</div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center text-xs text-muted-foreground pt-4 border-t">
            <p>SF4 - Monthly Report on Learner's Movement</p>
            <p>Generated on {format(new Date(report.generatedAt), "MMMM dd, yyyy 'at' HH:mm")}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
