"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { Search, Filter, X, SlidersHorizontal } from "lucide-react"
import { StudentFilters } from "@/lib/types/student"
import { cn } from "@/lib/utils"

interface StudentFiltersProps {
  filters: StudentFilters
  onFiltersChange: (filters: StudentFilters) => void
  availableGrades: string[]
  availableSections: string[]
  availableCourses: string[]
  availableYears: string[]
  className?: string
}

export function StudentFiltersComponent({
  filters,
  onFiltersChange,
  availableGrades,
  availableSections,
  availableCourses,
  availableYears,
  className
}: StudentFiltersProps) {
  const [isOpen, setIsOpen] = useState(false)

  const updateFilter = (key: keyof StudentFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const toggleArrayFilter = (key: 'grade' | 'section' | 'status' | 'course' | 'year', value: string) => {
    const currentArray = filters[key] || []
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value]
    
    updateFilter(key, newArray.length > 0 ? newArray : undefined)
  }

  const clearFilters = () => {
    onFiltersChange({})
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.grade?.length) count++
    if (filters.section?.length) count++
    if (filters.status?.length) count++
    if (filters.course?.length) count++
    if (filters.year?.length) count++
    return count
  }

  const hasActiveFilters = getActiveFiltersCount() > 0

  const statusOptions = ['Active', 'Inactive', 'Transferred', 'Graduated']

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search Bar */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search students by name, ID, email, or guardian..."
            value={filters.search || ''}
            onChange={(e) => updateFilter('search', e.target.value || undefined)}
            className="pl-10"
          />
        </div>
        
        {/* Mobile Filter Button */}
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" className="md:hidden">
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Filters
              {hasActiveFilters && (
                <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                  {getActiveFiltersCount()}
                </Badge>
              )}
            </Button>
          </SheetTrigger>
          
          <SheetContent side="right" className="w-80">
            <SheetHeader>
              <SheetTitle>Filter Students</SheetTitle>
              <SheetDescription>
                Apply filters to narrow down the student list
              </SheetDescription>
            </SheetHeader>
            
            <div className="mt-6">
              <MobileFilters
                filters={filters}
                onFiltersChange={onFiltersChange}
                availableGrades={availableGrades}
                availableSections={availableSections}
                availableCourses={availableCourses}
                availableYears={availableYears}
                statusOptions={statusOptions}
                onClearFilters={clearFilters}
              />
            </div>
          </SheetContent>
        </Sheet>

        {/* Desktop Filter Button */}
        <Button 
          variant="outline" 
          className="hidden md:flex"
          onClick={() => setIsOpen(!isOpen)}
        >
          <Filter className="h-4 w-4 mr-2" />
          Filters
          {hasActiveFilters && (
            <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
              {getActiveFiltersCount()}
            </Badge>
          )}
        </Button>

        {hasActiveFilters && (
          <Button variant="ghost" onClick={clearFilters}>
            <X className="h-4 w-4 mr-2" />
            Clear
          </Button>
        )}
      </div>

      {/* Desktop Filters */}
      {isOpen && (
        <Card className="hidden md:block">
          <CardHeader>
            <CardTitle className="text-lg">Filter Options</CardTitle>
            <CardDescription>
              Select multiple options to filter the student list
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DesktopFilters
              filters={filters}
              onFiltersChange={onFiltersChange}
              availableGrades={availableGrades}
              availableSections={availableSections}
              availableCourses={availableCourses}
              availableYears={availableYears}
              statusOptions={statusOptions}
              toggleArrayFilter={toggleArrayFilter}
            />
          </CardContent>
        </Card>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="secondary" className="gap-1">
              Search: {filters.search}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => updateFilter('search', undefined)}
              />
            </Badge>
          )}
          
          {filters.grade?.map(grade => (
            <Badge key={grade} variant="secondary" className="gap-1">
              Grade {grade}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => toggleArrayFilter('grade', grade)}
              />
            </Badge>
          ))}
          
          {filters.section?.map(section => (
            <Badge key={section} variant="secondary" className="gap-1">
              Section {section}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => toggleArrayFilter('section', section)}
              />
            </Badge>
          ))}
          
          {filters.status?.map(status => (
            <Badge key={status} variant="secondary" className="gap-1">
              {status}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => toggleArrayFilter('status', status)}
              />
            </Badge>
          ))}
          
          {filters.course?.map(course => (
            <Badge key={course} variant="secondary" className="gap-1">
              {course}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => toggleArrayFilter('course', course)}
              />
            </Badge>
          ))}
          
          {filters.year?.map(year => (
            <Badge key={year} variant="secondary" className="gap-1">
              {year}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => toggleArrayFilter('year', year)}
              />
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}

// Desktop Filters Component
function DesktopFilters({
  filters,
  onFiltersChange,
  availableGrades,
  availableSections,
  availableCourses,
  availableYears,
  statusOptions,
  toggleArrayFilter
}: {
  filters: StudentFilters
  onFiltersChange: (filters: StudentFilters) => void
  availableGrades: string[]
  availableSections: string[]
  availableCourses: string[]
  availableYears: string[]
  statusOptions: string[]
  toggleArrayFilter: (key: 'grade' | 'section' | 'status' | 'course' | 'year', value: string) => void
}) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Grade Level Filter */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Grade Level</Label>
        <div className="space-y-2">
          {availableGrades.map(grade => (
            <div key={grade} className="flex items-center space-x-2">
              <Checkbox
                id={`grade-${grade}`}
                checked={filters.grade?.includes(grade) || false}
                onCheckedChange={() => toggleArrayFilter('grade', grade)}
              />
              <Label htmlFor={`grade-${grade}`} className="text-sm">
                Grade {grade}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Section Filter */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Section</Label>
        <div className="space-y-2">
          {availableSections.map(section => (
            <div key={section} className="flex items-center space-x-2">
              <Checkbox
                id={`section-${section}`}
                checked={filters.section?.includes(section) || false}
                onCheckedChange={() => toggleArrayFilter('section', section)}
              />
              <Label htmlFor={`section-${section}`} className="text-sm">
                Section {section}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Status Filter */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Status</Label>
        <div className="space-y-2">
          {statusOptions.map(status => (
            <div key={status} className="flex items-center space-x-2">
              <Checkbox
                id={`status-${status}`}
                checked={filters.status?.includes(status) || false}
                onCheckedChange={() => toggleArrayFilter('status', status)}
              />
              <Label htmlFor={`status-${status}`} className="text-sm">
                {status}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Course Filter */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Course/Track</Label>
        <div className="space-y-2">
          {availableCourses.map(course => (
            <div key={course} className="flex items-center space-x-2">
              <Checkbox
                id={`course-${course}`}
                checked={filters.course?.includes(course) || false}
                onCheckedChange={() => toggleArrayFilter('course', course)}
              />
              <Label htmlFor={`course-${course}`} className="text-sm">
                {course}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Year Filter */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Year Level</Label>
        <div className="space-y-2">
          {availableYears.map(year => (
            <div key={year} className="flex items-center space-x-2">
              <Checkbox
                id={`year-${year}`}
                checked={filters.year?.includes(year) || false}
                onCheckedChange={() => toggleArrayFilter('year', year)}
              />
              <Label htmlFor={`year-${year}`} className="text-sm">
                {year}
              </Label>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Mobile Filters Component
function MobileFilters({
  filters,
  onFiltersChange,
  availableGrades,
  availableSections,
  availableCourses,
  availableYears,
  statusOptions,
  onClearFilters
}: {
  filters: StudentFilters
  onFiltersChange: (filters: StudentFilters) => void
  availableGrades: string[]
  availableSections: string[]
  availableCourses: string[]
  availableYears: string[]
  statusOptions: string[]
  onClearFilters: () => void
}) {
  const toggleArrayFilter = (key: 'grade' | 'section' | 'status' | 'course' | 'year', value: string) => {
    const currentArray = filters[key] || []
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value]
    
    onFiltersChange({
      ...filters,
      [key]: newArray.length > 0 ? newArray : undefined
    })
  }

  return (
    <div className="space-y-6">
      <DesktopFilters
        filters={filters}
        onFiltersChange={onFiltersChange}
        availableGrades={availableGrades}
        availableSections={availableSections}
        availableCourses={availableCourses}
        availableYears={availableYears}
        statusOptions={statusOptions}
        toggleArrayFilter={toggleArrayFilter}
      />
      
      <Separator />
      
      <Button variant="outline" onClick={onClearFilters} className="w-full">
        <X className="h-4 w-4 mr-2" />
        Clear All Filters
      </Button>
    </div>
  )
}
