import { auth } from "@/lib/auth/config"
import { NextResponse } from "next/server"
import { UserRole } from "@/lib/generated/prisma"

// Route permissions mapping
const ROUTE_PERMISSIONS: Record<string, { roles: UserRole[], permissions?: string[] }> = {
  // Dashboard routes
  '/dashboard': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER, UserRole.SCANNER] },

  // Student management
  '/students': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER] },
  '/students/create': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL] },
  '/students/edit': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL] },

  // Attendance
  '/attendance': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER] },
  '/scanner': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER, UserRole.SCANNER] },

  // Reports
  '/reports': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER] },
  '/analytics': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL] },

  // User management
  '/users': { roles: [UserRole.ADMIN] },
  '/settings': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL] }
}

// API route permissions
const API_PERMISSIONS: Record<string, { roles: UserRole[], permissions?: string[] }> = {
  // Authentication APIs
  '/api/auth/users': { roles: [UserRole.ADMIN] },
  '/api/auth/profile': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER, UserRole.SCANNER] },

  // Student APIs
  '/api/students': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER] },

  // Attendance APIs
  '/api/attendance': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER] },
  '/api/scanner': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER, UserRole.SCANNER] },

  // Analytics APIs
  '/api/analytics': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL] },

  // Reports APIs
  '/api/reports': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER] }
}

function hasRouteAccess(pathname: string, userRole: UserRole, userPermissions?: string[]): boolean {
  // Check exact route match first
  const exactMatch = ROUTE_PERMISSIONS[pathname] || API_PERMISSIONS[pathname]
  if (exactMatch) {
    return exactMatch.roles.includes(userRole)
  }

  // Check pattern matches
  for (const [route, config] of Object.entries({ ...ROUTE_PERMISSIONS, ...API_PERMISSIONS })) {
    if (pathname.startsWith(route)) {
      return config.roles.includes(userRole)
    }
  }

  // Default: allow access if no specific restrictions
  return true
}

function addSecurityHeaders(response: NextResponse): NextResponse {
  // Security headers
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')

  // CSRF protection for state-changing requests
  if (response.headers.get('content-type')?.includes('text/html')) {
    response.headers.set('X-CSRF-Protection', '1')
  }

  return response
}

export default auth((req) => {
  const { pathname } = req.nextUrl
  const isLoggedIn = !!req.auth
  const user = req.auth?.user

  // Public routes that don't require authentication
  const publicRoutes = ["/login", "/", "/api/auth/signin", "/api/auth/signout"]
  const isPublicRoute = publicRoutes.some(route => pathname === route || pathname.startsWith(route))

  // Static files and API routes that should be excluded
  const excludedPaths = [
    "/_next",
    "/favicon.ico",
    "/api/auth/signin",
    "/api/auth/signout",
    "/api/auth/session",
    "/api/auth/csrf"
  ]
  const isExcludedPath = excludedPaths.some(path => pathname.startsWith(path))

  if (isExcludedPath) {
    return addSecurityHeaders(NextResponse.next())
  }

  // If user is not logged in and trying to access protected route
  if (!isLoggedIn && !isPublicRoute) {
    if (pathname.startsWith('/api/')) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }
    return NextResponse.redirect(new URL("/login", req.url))
  }

  // If user is logged in and trying to access login page
  if (isLoggedIn && pathname === "/login") {
    return NextResponse.redirect(new URL("/dashboard", req.url))
  }

  // Check if user account is active
  if (isLoggedIn && user && !user.isActive) {
    if (pathname.startsWith('/api/')) {
      return NextResponse.json(
        { success: false, error: 'Account is inactive' },
        { status: 403 }
      )
    }
    return NextResponse.redirect(new URL("/login?error=account_inactive", req.url))
  }

  // Check if user must change password
  if (isLoggedIn && user && user.mustChangePassword && pathname !== "/change-password" && !pathname.startsWith('/api/auth/profile')) {
    if (pathname.startsWith('/api/')) {
      return NextResponse.json(
        { success: false, error: 'Password change required' },
        { status: 403 }
      )
    }
    return NextResponse.redirect(new URL("/change-password", req.url))
  }

  // Role-based access control
  if (isLoggedIn && user) {
    const hasAccess = hasRouteAccess(pathname, user.role as UserRole, user.permissions)

    if (!hasAccess) {
      if (pathname.startsWith('/api/')) {
        return NextResponse.json(
          { success: false, error: 'Insufficient permissions' },
          { status: 403 }
        )
      }
      return NextResponse.redirect(new URL("/dashboard?error=access_denied", req.url))
    }
  }

  // Add security headers to response
  return addSecurityHeaders(NextResponse.next())
})

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"]
}
