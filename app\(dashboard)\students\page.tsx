"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Plus, Users, TrendingUp, UserCheck, UserX, Upload, Download, QrCode } from "lucide-react"
import { Student, StudentFilters, StudentSortConfig, PaginationConfig, getFullName } from "@/lib/types/student"
import { StudentFiltersComponent } from "@/components/students/student-filters"
import { StudentsTable } from "@/components/students/students-table"
import { BulkActions } from "@/components/students/bulk-actions"
import { Pagination } from "@/components/students/pagination"
import { StudentRegistrationDialog } from "@/components/students/student-registration-dialog"
import { CSVImportDialog } from "@/components/students/csv-import-dialog"
import { QRBatchGenerator } from "@/components/students/qr-batch-generator"
import {
  mockStudentsData,
  searchStudents,
  getUniqueGrades,
  getUniqueSections,
  getUniqueCourses,
  getUniqueYears
} from "@/lib/data/students-mock-data"
import { toast } from "sonner"

export default function StudentsPage() {
  const [students, setStudents] = useState<Student[]>(mockStudentsData)
  const [selectedStudents, setSelectedStudents] = useState<string[]>([])
  const [filters, setFilters] = useState<StudentFilters>({})
  const [sortConfig, setSortConfig] = useState<StudentSortConfig>({ field: 'name', direction: 'asc' })
  const [pagination, setPagination] = useState<PaginationConfig>({ page: 1, pageSize: 20, total: 0 })
  const [showAddDialog, setShowAddDialog] = useState(false)

  // Filter and sort students
  const filteredAndSortedStudents = useMemo(() => {
    let filtered = students

    // Apply search filter
    if (filters.search) {
      filtered = searchStudents(filters.search)
    }

    // Apply other filters
    if (filters.grade?.length) {
      filtered = filtered.filter(s => filters.grade!.includes(s.grade))
    }
    if (filters.section?.length) {
      filtered = filtered.filter(s => s.section && filters.section!.includes(s.section))
    }
    if (filters.status?.length) {
      filtered = filtered.filter(s => filters.status!.includes(s.status))
    }
    if (filters.course?.length) {
      filtered = filtered.filter(s => filters.course!.includes(s.course))
    }
    if (filters.year?.length) {
      filtered = filtered.filter(s => filters.year!.includes(s.year))
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      let aValue: any
      let bValue: any

      if (sortConfig.field === 'name') {
        aValue = getFullName(a).toLowerCase()
        bValue = getFullName(b).toLowerCase()
      } else {
        aValue = a[sortConfig.field as keyof Student]
        bValue = b[sortConfig.field as keyof Student]
      }

      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1
      return 0
    })

    return sorted
  }, [students, filters, sortConfig])

  // Paginate students
  const paginatedStudents = useMemo(() => {
    const startIndex = (pagination.page - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    return filteredAndSortedStudents.slice(startIndex, endIndex)
  }, [filteredAndSortedStudents, pagination.page, pagination.pageSize])

  // Update pagination total when filtered students change
  useEffect(() => {
    setPagination(prev => ({
      ...prev,
      total: filteredAndSortedStudents.length,
      page: 1 // Reset to first page when filters change
    }))
  }, [filteredAndSortedStudents.length])

  // Statistics
  const stats = useMemo(() => {
    const total = students.length
    const active = students.filter(s => s.status === 'Active').length
    const inactive = students.filter(s => s.status === 'Inactive').length
    const avgAttendance = students
      .filter(s => s.attendanceStats)
      .reduce((sum, s) => sum + (s.attendanceStats?.attendanceRate || 0), 0) /
      students.filter(s => s.attendanceStats).length || 0

    return { total, active, inactive, avgAttendance }
  }, [students])

  const handleStudentCreated = (student: Student) => {
    setStudents(prev => [...prev, student])
    toast.success("Student registered successfully!")
  }

  const handleStudentUpdated = (updatedStudent: Student) => {
    setStudents(prev => prev.map(s => s.id === updatedStudent.id ? updatedStudent : s))
    toast.success("Student updated successfully!")
  }

  const handleStudentDeleted = (studentId: string) => {
    setStudents(prev => prev.filter(s => s.id !== studentId))
    setSelectedStudents(prev => prev.filter(id => id !== studentId))
    toast.success("Student deleted successfully!")
  }

  const handleBulkStudentsUpdated = (updatedStudents: Student[]) => {
    setStudents(updatedStudents)
    setSelectedStudents([])
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Students</h1>
            <p className="text-muted-foreground">
              Manage student records and information
            </p>
          </div>

          {/* Primary Action - Always Visible */}
          <StudentRegistrationDialog
            open={showAddDialog}
            onOpenChange={setShowAddDialog}
            onStudentCreated={handleStudentCreated}
            trigger={
              <Button className="w-full sm:w-auto">
                <Plus className="mr-2 h-4 w-4" />
                Add Student
              </Button>
            }
          />
        </div>

        {/* Secondary Actions - Mobile Responsive */}
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="flex flex-1 gap-2">
            <CSVImportDialog
              onStudentsImported={(importedStudents) => {
                setStudents(prev => [...prev, ...importedStudents])
                toast.success(`Imported ${importedStudents.length} students`)
              }}
              trigger={
                <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
                  <Upload className="mr-2 h-4 w-4" />
                  <span className="hidden sm:inline">Import CSV</span>
                  <span className="sm:hidden">Import</span>
                </Button>
              }
            />

            <QRBatchGenerator
              students={students}
              trigger={
                <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
                  <QrCode className="mr-2 h-4 w-4" />
                  <span className="hidden sm:inline">Generate QR</span>
                  <span className="sm:hidden">QR</span>
                </Button>
              }
            />

            <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
              <Download className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Export All</span>
              <span className="sm:hidden">Export</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium">Total Students</CardTitle>
            <Users className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              <span className="hidden sm:inline">Registered in system</span>
              <span className="sm:hidden">Registered</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium">Active Students</CardTitle>
            <UserCheck className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold text-green-600">{stats.active}</div>
            <p className="text-xs text-muted-foreground">
              <span className="hidden sm:inline">Currently enrolled</span>
              <span className="sm:hidden">Enrolled</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium">Inactive Students</CardTitle>
            <UserX className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold text-yellow-600">{stats.inactive}</div>
            <p className="text-xs text-muted-foreground">
              <span className="hidden sm:inline">Not currently active</span>
              <span className="sm:hidden">Inactive</span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium">Avg. Attendance</CardTitle>
            <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold">{stats.avgAttendance.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              <span className="hidden sm:inline">Overall attendance rate</span>
              <span className="sm:hidden">Attendance</span>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <StudentFiltersComponent
        filters={filters}
        onFiltersChange={setFilters}
        availableGrades={getUniqueGrades()}
        availableSections={getUniqueSections()}
        availableCourses={getUniqueCourses()}
        availableYears={getUniqueYears()}
      />

      {/* Bulk Actions */}
      {selectedStudents.length > 0 && (
        <BulkActions
          selectedStudents={selectedStudents}
          students={students}
          onClearSelection={() => setSelectedStudents([])}
          onStudentsUpdated={handleBulkStudentsUpdated}
        />
      )}

      {/* Students Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Student Directory</span>
            <Badge variant="secondary">
              {filteredAndSortedStudents.length} student{filteredAndSortedStudents.length !== 1 ? 's' : ''}
            </Badge>
          </CardTitle>
          <CardDescription>
            {filters.search || Object.keys(filters).length > 1 ?
              `Filtered results from ${students.length} total students` :
              "Complete list of registered students"
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <StudentsTable
            students={paginatedStudents}
            selectedStudents={selectedStudents}
            onSelectionChange={setSelectedStudents}
            sortConfig={sortConfig}
            onSortChange={setSortConfig}
            onStudentUpdated={handleStudentUpdated}
            onStudentDeleted={handleStudentDeleted}
          />

          {filteredAndSortedStudents.length > pagination.pageSize && (
            <>
              <Separator />
              <div className="p-4">
                <Pagination
                  pagination={pagination}
                  onPaginationChange={setPagination}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
