"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  QrCode, 
  Trash2,
  Phone,
  Mail,
  MapPin,
  GraduationCap,
  User
} from "lucide-react"
import { Student, getFullName } from "@/lib/types/student"
import { StudentProfileDialog } from "./student-profile-dialog"
import { StudentRegistrationDialog } from "./student-registration-dialog"
import { StudentStatusManager } from "./student-status-manager"
import { AttendanceStatisticsCompact } from "./attendance-statistics"
import { cn } from "@/lib/utils"

interface MobileStudentCardProps {
  student: Student
  isSelected: boolean
  onSelectionChange: (selected: boolean) => void
  onStudentUpdated?: (student: Student) => void
  onStudentDeleted?: (studentId: string) => void
  className?: string
}

export function MobileStudentCard({
  student,
  isSelected,
  onSelectionChange,
  onStudentUpdated,
  onStudentDeleted,
  className
}: MobileStudentCardProps) {
  const fullName = getFullName(student)

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-500'
      case 'Inactive':
        return 'bg-yellow-500'
      case 'Transferred':
        return 'bg-blue-500'
      case 'Graduated':
        return 'bg-purple-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <Card className={cn("w-full", isSelected && "ring-2 ring-primary", className)}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Selection Checkbox */}
          <Checkbox
            checked={isSelected}
            onCheckedChange={onSelectionChange}
            className="mt-1"
            aria-label={`Select ${fullName}`}
          />

          {/* Student Avatar */}
          <Avatar className="h-12 w-12 flex-shrink-0">
            <AvatarImage src={student.photo} alt={fullName} />
            <AvatarFallback className="text-sm">
              {getInitials(fullName)}
            </AvatarFallback>
          </Avatar>

          {/* Student Information */}
          <div className="flex-1 min-w-0">
            {/* Header Row */}
            <div className="flex items-start justify-between gap-2 mb-2">
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-base truncate">{fullName}</h3>
                <p className="text-sm text-muted-foreground truncate">{student.email}</p>
              </div>
              
              {/* Actions Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0 flex-shrink-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <StudentProfileDialog
                    student={student}
                    onStudentUpdated={onStudentUpdated}
                    trigger={
                      <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                        <Eye className="h-4 w-4 mr-2" />
                        View Profile
                      </DropdownMenuItem>
                    }
                  />
                  <DropdownMenuItem>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Student
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <QrCode className="h-4 w-4 mr-2" />
                    Generate QR Code
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <StudentStatusManager
                    student={student}
                    onStatusChanged={(updatedStudent) => onStudentUpdated?.(updatedStudent)}
                    trigger={
                      <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                        <Edit className="h-4 w-4 mr-2" />
                        Manage Status
                      </DropdownMenuItem>
                    }
                  />
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    className="text-destructive"
                    onClick={() => onStudentDeleted?.(student.id)}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Student
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Student Details Grid */}
            <div className="grid grid-cols-2 gap-2 text-sm mb-3">
              <div className="flex items-center gap-1">
                <User className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                <span className="font-mono text-xs truncate">{student.id}</span>
              </div>
              
              <div className="flex items-center gap-1">
                <GraduationCap className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                <span className="text-xs truncate">Grade {student.grade}</span>
              </div>
              
              <div className="flex items-center gap-1">
                <Phone className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                <span className="text-xs truncate">{student.guardian.phone}</span>
              </div>
              
              <div className="flex items-center gap-1">
                <Mail className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                <span className="text-xs truncate">{student.guardian.email || 'No email'}</span>
              </div>
            </div>

            {/* Course and Section */}
            <div className="flex items-center gap-2 mb-3">
              <Badge variant="outline" className="text-xs">
                {student.course}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {student.year}
              </Badge>
              {student.section && (
                <Badge variant="secondary" className="text-xs">
                  Section {student.section}
                </Badge>
              )}
            </div>

            {/* Bottom Row - Status and Attendance */}
            <div className="flex items-center justify-between">
              <Badge 
                variant={student.status === 'Active' ? 'default' : 'secondary'}
                className={cn("text-xs", getStatusColor(student.status))}
              >
                {student.status}
              </Badge>
              
              <AttendanceStatisticsCompact student={student} />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Mobile-friendly student list component
interface MobileStudentListProps {
  students: Student[]
  selectedStudents: string[]
  onSelectionChange: (studentIds: string[]) => void
  onStudentUpdated?: (student: Student) => void
  onStudentDeleted?: (studentId: string) => void
  className?: string
}

export function MobileStudentList({
  students,
  selectedStudents,
  onSelectionChange,
  onStudentUpdated,
  onStudentDeleted,
  className
}: MobileStudentListProps) {
  const handleSelectStudent = (studentId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedStudents, studentId])
    } else {
      onSelectionChange(selectedStudents.filter(id => id !== studentId))
    }
  }

  if (students.length === 0) {
    return (
      <div className="text-center py-12">
        <User className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">No students found</h3>
        <p className="text-muted-foreground">
          No students match your current search and filter criteria.
        </p>
      </div>
    )
  }

  return (
    <div className={cn("space-y-3", className)}>
      {students.map((student) => (
        <MobileStudentCard
          key={student.id}
          student={student}
          isSelected={selectedStudents.includes(student.id)}
          onSelectionChange={(checked) => handleSelectStudent(student.id, checked)}
          onStudentUpdated={onStudentUpdated}
          onStudentDeleted={onStudentDeleted}
        />
      ))}
    </div>
  )
}
