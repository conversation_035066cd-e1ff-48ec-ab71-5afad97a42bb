"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { CustomReportBuilder } from "./custom-report-builder"
import { CustomReportPreview } from "./custom-report-preview"
import { CustomReport, ExportFormat } from "@/lib/types/reports"
import { ArrowLeft, Users, Eye, Save } from "lucide-react"
import { toast } from "sonner"

interface CustomReportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CustomReportDialog({ open, onOpenChange }: CustomReportDialogProps) {
  const [activeTab, setActiveTab] = useState("builder")
  const [currentReport, setCurrentReport] = useState<CustomReport | null>(null)
  const [savedReports, setSavedReports] = useState<CustomReport[]>([])

  const handleGenerateReport = (report: CustomReport) => {
    setCurrentReport(report)
    setActiveTab("preview")
    toast.success("Custom report generated successfully", {
      description: "You can now preview, download, or print the report"
    })
  }

  const handlePreviewReport = (report: CustomReport) => {
    setCurrentReport(report)
    setActiveTab("preview")
  }

  const handleSaveReport = (report: CustomReport) => {
    setSavedReports([...savedReports, report])
    toast.success("Report template saved", {
      description: "You can reuse this template for future reports"
    })
  }

  const handleDownload = (format: ExportFormat) => {
    toast.success(`Downloading custom report in ${format} format`, {
      description: "The download will start shortly"
    })
  }

  const handlePrint = () => {
    window.print()
    toast.success("Print dialog opened", {
      description: "Please select your printer and print settings"
    })
  }

  const handleBackToBuilder = () => {
    setActiveTab("builder")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Custom Report Builder
          </DialogTitle>
          <DialogDescription>
            Create flexible reports with custom data sources, filters, and visualizations
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="builder" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Report Builder
            </TabsTrigger>
            <TabsTrigger 
              value="preview" 
              disabled={!currentReport}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              Preview Report
            </TabsTrigger>
            <TabsTrigger value="templates" className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              Saved Templates
            </TabsTrigger>
          </TabsList>

          <TabsContent value="builder" className="mt-6">
            <CustomReportBuilder
              onGenerate={handleGenerateReport}
              onPreview={handlePreviewReport}
              onSave={handleSaveReport}
            />
          </TabsContent>

          <TabsContent value="preview" className="mt-6">
            {currentReport ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Button 
                    variant="outline" 
                    onClick={handleBackToBuilder}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Back to Builder
                  </Button>
                </div>
                
                <CustomReportPreview
                  report={currentReport}
                  onDownload={handleDownload}
                  onPrint={handlePrint}
                />
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No report generated yet</p>
                <p className="text-sm">Use the report builder to create a custom report</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="templates" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Saved Report Templates</h3>
                  <p className="text-sm text-muted-foreground">
                    Reuse previously saved report configurations
                  </p>
                </div>
                <Button 
                  variant="outline"
                  onClick={() => setActiveTab("builder")}
                >
                  <Users className="mr-2 h-4 w-4" />
                  Create New
                </Button>
              </div>

              {savedReports.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <Save className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No saved templates</p>
                  <p className="text-sm">Save report configurations to reuse them later</p>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {savedReports.map((report, index) => (
                    <div key={index} className="p-4 border rounded-lg space-y-3">
                      <div>
                        <h4 className="font-medium">{report.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {report.description}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <span>{report.query.tables.length} data sources</span>
                          <span>•</span>
                          <span>{report.query.fields.length} fields</span>
                          <span>•</span>
                          <span>{report.query.conditions.length} filters</span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => {
                              setCurrentReport(report)
                              setActiveTab("preview")
                            }}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            Preview
                          </Button>
                          <Button 
                            size="sm"
                            onClick={() => handleGenerateReport(report)}
                          >
                            Generate
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
