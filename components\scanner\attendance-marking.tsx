"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Student, AttendanceAction, ScanMode, Subject, TimePeriod } from "@/lib/types/scanner"
import { 
  LogIn, 
  LogOut, 
  CheckCircle, 
  Clock, 
  XCircle, 
  AlertCircle,
  BookOpen,
  Timer,
  User
} from "lucide-react"
import { cn } from "@/lib/utils"

interface AttendanceMarkingProps {
  student: Student
  mode: ScanMode
  onMarkAttendance: (action: AttendanceAction, options?: {
    subject?: string
    period?: string
    reason?: string
  }) => void
  isLoading?: boolean
  subjects?: Subject[]
  periods?: TimePeriod[]
  selectedSubject?: string
  selectedPeriod?: string
  onSubjectChange?: (subject: string) => void
  onPeriodChange?: (period: string) => void
}

export function AttendanceMarking({
  student,
  mode,
  onMarkAttendance,
  isLoading = false,
  subjects = [],
  periods = [],
  selectedSubject,
  selectedPeriod,
  onSubjectChange,
  onPeriodChange
}: AttendanceMarkingProps) {
  const [selectedAction, setSelectedAction] = useState<AttendanceAction | null>(null)

  const handleMarkAttendance = (action: AttendanceAction) => {
    setSelectedAction(action)
    
    const options: any = {}
    if (mode === 'subject') {
      options.subject = selectedSubject
      options.period = selectedPeriod
    }
    
    onMarkAttendance(action, options)
    
    // Reset selection after a brief delay
    setTimeout(() => setSelectedAction(null), 1000)
  }

  const getActionIcon = (action: AttendanceAction) => {
    switch (action) {
      case 'check-in':
        return <LogIn className="h-5 w-5" />
      case 'check-out':
        return <LogOut className="h-5 w-5" />
      case 'present':
        return <CheckCircle className="h-5 w-5" />
      case 'late':
        return <Clock className="h-5 w-5" />
      case 'absent':
        return <XCircle className="h-5 w-5" />
      default:
        return <AlertCircle className="h-5 w-5" />
    }
  }

  const getActionColor = (action: AttendanceAction) => {
    switch (action) {
      case 'check-in':
      case 'present':
        return 'bg-green-500 hover:bg-green-600'
      case 'late':
        return 'bg-yellow-500 hover:bg-yellow-600'
      case 'check-out':
        return 'bg-blue-500 hover:bg-blue-600'
      case 'absent':
        return 'bg-red-500 hover:bg-red-600'
      default:
        return 'bg-gray-500 hover:bg-gray-600'
    }
  }

  const gateActions: AttendanceAction[] = ['check-in', 'check-out']
  const subjectActions: AttendanceAction[] = ['present', 'late', 'absent']

  const actions = mode === 'gate' ? gateActions : subjectActions

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Mark Attendance
        </CardTitle>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            {mode === 'gate' ? 'Gate Mode' : 'Subject Mode'}
          </Badge>
          <Badge variant="secondary" className="text-xs">
            {student.name}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Subject and Period Selection for Subject Mode */}
        {mode === 'subject' && (
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Subject
              </label>
              <Select value={selectedSubject} onValueChange={onSubjectChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id}>
                      <div className="flex flex-col">
                        <span>{subject.name}</span>
                        <span className="text-xs text-muted-foreground">{subject.code}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-2">
                <Timer className="h-4 w-4" />
                Time Period
              </label>
              <Select value={selectedPeriod} onValueChange={onPeriodChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  {periods.map((period) => (
                    <SelectItem key={period.id} value={period.id}>
                      <div className="flex flex-col">
                        <span>{period.name}</span>
                        <span className="text-xs text-muted-foreground">
                          {period.startTime} - {period.endTime}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Separator />
          </div>
        )}

        {/* Attendance Action Buttons */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-muted-foreground">
            {mode === 'gate' ? 'Gate Actions' : 'Attendance Status'}
          </h4>
          
          <div className="grid gap-3">
            {actions.map((action) => (
              <Button
                key={action}
                onClick={() => handleMarkAttendance(action)}
                disabled={
                  isLoading || 
                  (mode === 'subject' && (!selectedSubject || !selectedPeriod))
                }
                className={cn(
                  "h-16 text-white font-semibold text-lg transition-all duration-200",
                  getActionColor(action),
                  selectedAction === action && "scale-95 opacity-75"
                )}
                size="lg"
              >
                <div className="flex items-center gap-3">
                  {getActionIcon(action)}
                  <div className="flex flex-col items-start">
                    <span className="capitalize">{action.replace('-', ' ')}</span>
                    <span className="text-xs opacity-75">
                      {mode === 'gate' 
                        ? action === 'check-in' ? 'Enter building' : 'Exit building'
                        : action === 'present' ? 'Student is present' : 
                          action === 'late' ? 'Student is late' : 'Mark as absent'
                      }
                    </span>
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* Manual Override Section */}
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium text-muted-foreground mb-3">
            Manual Override
          </h4>
          
          <div className="grid grid-cols-2 gap-2">
            {(mode === 'gate' ? subjectActions : gateActions).map((action) => (
              <Button
                key={action}
                variant="outline"
                size="sm"
                onClick={() => handleMarkAttendance(action)}
                disabled={isLoading}
                className="h-12 text-xs"
              >
                <div className="flex flex-col items-center gap-1">
                  {getActionIcon(action)}
                  <span className="capitalize">{action.replace('-', ' ')}</span>
                </div>
              </Button>
            ))}
          </div>
          
          <p className="text-xs text-muted-foreground mt-2">
            Use manual override for special cases or corrections
          </p>
        </div>

        {/* Current Selection Info */}
        {mode === 'subject' && (selectedSubject || selectedPeriod) && (
          <div className="bg-muted/50 p-3 rounded-lg">
            <h5 className="text-xs font-medium text-muted-foreground mb-2">
              Current Selection
            </h5>
            <div className="space-y-1 text-sm">
              {selectedSubject && (
                <div className="flex justify-between">
                  <span>Subject:</span>
                  <span className="font-medium">
                    {subjects.find(s => s.id === selectedSubject)?.name || selectedSubject}
                  </span>
                </div>
              )}
              {selectedPeriod && (
                <div className="flex justify-between">
                  <span>Period:</span>
                  <span className="font-medium">
                    {periods.find(p => p.id === selectedPeriod)?.name || selectedPeriod}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
