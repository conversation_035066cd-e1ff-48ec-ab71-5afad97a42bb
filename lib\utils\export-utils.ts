import { format } from "date-fns"
import { SF2Report, SF4Report, CustomReport, ExportFormat } from '@/lib/types/reports'

// Types for export data
export interface AttendanceRecord {
  id: string
  studentId: string
  studentName: string
  course: string
  grade: string
  section: string
  checkIn?: string
  checkOut?: string
  date: string
  status: "Present" | "Late" | "Absent"
  type: "gate" | "subject"
  subject?: string
  period?: string
  reason?: string
}

export interface ExportOptions {
  format: "csv" | "pdf" | "excel"
  dateRange?: {
    from: Date
    to: Date
  }
  includePhotos?: boolean
  groupBy?: "grade" | "section" | "course" | "none"
}

// CSV Export Functions
export function exportToCSV(data: AttendanceRecord[], filename?: string): void {
  const headers = [
    "Student ID",
    "Student Name", 
    "Grade",
    "Section",
    "Course",
    "Date",
    "Check In",
    "Check Out",
    "Status",
    "Type",
    "Subject",
    "Period",
    "Reason"
  ]

  const csvContent = [
    headers.join(","),
    ...data.map(record => [
      record.studentId,
      `"${record.studentName}"`,
      record.grade,
      `"${record.section}"`,
      `"${record.course}"`,
      record.date,
      record.checkIn || "",
      record.checkOut || "",
      record.status,
      record.type,
      record.subject || "",
      record.period || "",
      record.reason ? `"${record.reason}"` : ""
    ].join(","))
  ].join("\n")

  downloadFile(
    csvContent,
    filename || `attendance-report-${format(new Date(), "yyyy-MM-dd")}.csv`,
    "text/csv"
  )
}

// Generate daily report data
export function generateDailyReport(data: AttendanceRecord[], date: Date) {
  const dateStr = format(date, "yyyy-MM-dd")
  const dayData = data.filter(record => record.date === dateStr)
  
  const summary = {
    date: format(date, "MMMM d, yyyy"),
    totalStudents: dayData.length,
    present: dayData.filter(r => r.status === "Present").length,
    late: dayData.filter(r => r.status === "Late").length,
    absent: dayData.filter(r => r.status === "Absent").length,
    attendanceRate: 0
  }

  if (summary.totalStudents > 0) {
    summary.attendanceRate = ((summary.present + summary.late) / summary.totalStudents) * 100
  }

  const gradeBreakdown = dayData.reduce((acc, record) => {
    const grade = record.grade
    if (!acc[grade]) {
      acc[grade] = { total: 0, present: 0, late: 0, absent: 0 }
    }
    acc[grade].total++
    acc[grade][record.status.toLowerCase() as keyof typeof acc[typeof grade]]++
    return acc
  }, {} as Record<string, { total: number; present: number; late: number; absent: number }>)

  return {
    summary,
    gradeBreakdown,
    records: dayData
  }
}

// Print functionality
export function printDailyReport(data: AttendanceRecord[], date: Date): void {
  const report = generateDailyReport(data, date)
  
  const printContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Daily Attendance Report - ${report.summary.date}</title>
      <style>
        body { 
          font-family: Arial, sans-serif; 
          margin: 20px; 
          color: #333;
        }
        .header { 
          text-align: center; 
          margin-bottom: 30px;
          border-bottom: 2px solid #333;
          padding-bottom: 20px;
        }
        .school-name {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .report-title {
          font-size: 18px;
          color: #666;
        }
        .summary { 
          display: grid; 
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }
        .summary-card {
          border: 1px solid #ddd;
          padding: 15px;
          border-radius: 8px;
          text-align: center;
        }
        .summary-value {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .summary-label {
          color: #666;
          font-size: 14px;
        }
        .present { color: #22c55e; }
        .late { color: #f59e0b; }
        .absent { color: #ef4444; }
        .rate { color: #3b82f6; }
        
        .grade-breakdown {
          margin-bottom: 30px;
        }
        .grade-breakdown h3 {
          margin-bottom: 15px;
          color: #333;
        }
        .grade-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        .grade-table th,
        .grade-table td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: center;
        }
        .grade-table th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        
        .records-table {
          width: 100%;
          border-collapse: collapse;
          font-size: 12px;
        }
        .records-table th,
        .records-table td {
          border: 1px solid #ddd;
          padding: 6px;
          text-align: left;
        }
        .records-table th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        
        .footer {
          margin-top: 40px;
          text-align: center;
          color: #666;
          font-size: 12px;
          border-top: 1px solid #ddd;
          padding-top: 20px;
        }
        
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="school-name">QRSAMS - Tanauan National High School</div>
        <div class="report-title">Daily Attendance Report</div>
        <div style="margin-top: 10px; font-size: 16px;">${report.summary.date}</div>
      </div>
      
      <div class="summary">
        <div class="summary-card">
          <div class="summary-value">${report.summary.totalStudents}</div>
          <div class="summary-label">Total Students</div>
        </div>
        <div class="summary-card">
          <div class="summary-value present">${report.summary.present}</div>
          <div class="summary-label">Present</div>
        </div>
        <div class="summary-card">
          <div class="summary-value late">${report.summary.late}</div>
          <div class="summary-label">Late</div>
        </div>
        <div class="summary-card">
          <div class="summary-value absent">${report.summary.absent}</div>
          <div class="summary-label">Absent</div>
        </div>
        <div class="summary-card">
          <div class="summary-value rate">${report.summary.attendanceRate.toFixed(1)}%</div>
          <div class="summary-label">Attendance Rate</div>
        </div>
      </div>
      
      <div class="grade-breakdown">
        <h3>Grade Level Breakdown</h3>
        <table class="grade-table">
          <thead>
            <tr>
              <th>Grade</th>
              <th>Total</th>
              <th>Present</th>
              <th>Late</th>
              <th>Absent</th>
              <th>Rate</th>
            </tr>
          </thead>
          <tbody>
            ${Object.entries(report.gradeBreakdown).map(([grade, stats]) => `
              <tr>
                <td>Grade ${grade}</td>
                <td>${stats.total}</td>
                <td class="present">${stats.present}</td>
                <td class="late">${stats.late}</td>
                <td class="absent">${stats.absent}</td>
                <td>${((stats.present + stats.late) / stats.total * 100).toFixed(1)}%</td>
              </tr>
            `).join("")}
          </tbody>
        </table>
      </div>
      
      <div>
        <h3>Detailed Records</h3>
        <table class="records-table">
          <thead>
            <tr>
              <th>Student ID</th>
              <th>Name</th>
              <th>Grade</th>
              <th>Section</th>
              <th>Check In</th>
              <th>Check Out</th>
              <th>Status</th>
              <th>Notes</th>
            </tr>
          </thead>
          <tbody>
            ${report.records.map(record => `
              <tr>
                <td>${record.studentId}</td>
                <td>${record.studentName}</td>
                <td>${record.grade}</td>
                <td>${record.section}</td>
                <td>${record.checkIn || "-"}</td>
                <td>${record.checkOut || "-"}</td>
                <td class="${record.status.toLowerCase()}">${record.status}</td>
                <td>${record.reason || "-"}</td>
              </tr>
            `).join("")}
          </tbody>
        </table>
      </div>
      
      <div class="footer">
        Generated on ${format(new Date(), "MMMM d, yyyy 'at' h:mm a")} by QRSAMS
      </div>
    </body>
    </html>
  `

  const printWindow = window.open("", "_blank")
  if (printWindow) {
    printWindow.document.write(printContent)
    printWindow.document.close()
    printWindow.focus()
    printWindow.print()
    printWindow.close()
  }
}

// Utility function to download files
function downloadFile(content: string, filename: string, mimeType: string): void {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  const link = document.createElement("a")
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// Generate summary statistics
export function generateSummaryStats(data: AttendanceRecord[]) {
  const total = data.length
  const present = data.filter(r => r.status === "Present").length
  const late = data.filter(r => r.status === "Late").length
  const absent = data.filter(r => r.status === "Absent").length
  const attendanceRate = total > 0 ? ((present + late) / total) * 100 : 0

  return {
    total,
    present,
    late,
    absent,
    attendanceRate
  }
}

// Enhanced PDF Generation for Reports
export class ReportPDFGenerator {
  static async generateSF2PDF(report: SF2Report): Promise<Blob> {
    const htmlContent = this.generateSF2HTML(report)

    // Simulate PDF generation delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // In a real implementation, this would use a PDF library like jsPDF or Puppeteer
    // For now, we'll create a mock PDF blob
    const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(SF2 Daily Attendance Report) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000206 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF`

    return new Blob([pdfContent], { type: 'application/pdf' })
  }

  static async generateSF4PDF(report: SF4Report): Promise<Blob> {
    const htmlContent = this.generateSF4HTML(report)

    await new Promise(resolve => setTimeout(resolve, 1000))

    const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 792 612]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 50
>>
stream
BT
/F1 12 Tf
72 720 Td
(SF4 Monthly Learner's Movement) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000206 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
305
%%EOF`

    return new Blob([pdfContent], { type: 'application/pdf' })
  }

  static async generateCustomPDF(report: CustomReport): Promise<Blob> {
    const htmlContent = this.generateCustomHTML(report)

    await new Promise(resolve => setTimeout(resolve, 1000))

    const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 35
>>
stream
BT
/F1 12 Tf
72 720 Td
(Custom Report) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000206 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
290
%%EOF`

    return new Blob([pdfContent], { type: 'application/pdf' })
  }

  private static generateSF2HTML(report: SF2Report): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>SF2 Daily Attendance Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .school-info { margin-bottom: 20px; }
        .attendance-table { width: 100%; border-collapse: collapse; }
        .attendance-table th, .attendance-table td { border: 1px solid #000; padding: 8px; text-align: center; }
        .signature-section { margin-top: 40px; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Republic of the Philippines</h1>
        <h2>Department of Education</h2>
        <h3>${report.schoolInfo.region}</h3>
        <h4>${report.schoolInfo.division}</h4>
        <h5>${report.schoolInfo.schoolName}</h5>
        <h2>SCHOOL FORM 2 (SF2)</h2>
        <h3>DAILY ATTENDANCE REPORT OF LEARNERS</h3>
      </div>

      <div class="school-info">
        <p><strong>Grade & Section:</strong> Grade ${report.grade} - ${report.section}</p>
        <p><strong>Subject:</strong> ${report.subject || 'All Subjects'}</p>
        <p><strong>Teacher:</strong> ${report.teacher.name}</p>
        <p><strong>Date:</strong> ${new Date(report.date).toLocaleDateString('en-PH', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
      </div>

      <table class="attendance-table">
        <thead>
          <tr>
            <th>No.</th>
            <th>LEARNER'S NAME</th>
            <th>ATTENDANCE</th>
            <th>REMARKS</th>
          </tr>
        </thead>
        <tbody>
          ${report.students.map((student, index) => `
            <tr>
              <td>${index + 1}</td>
              <td>${student.studentName}</td>
              <td>${student.dailyStatus}</td>
              <td>${student.remarks || ''}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <div class="signature-section">
        <p><strong>Summary:</strong></p>
        <p>Total Students: ${report.summary.totalStudents}</p>
        <p>Present: ${report.summary.presentCount}</p>
        <p>Late: ${report.summary.lateCount}</p>
        <p>Absent: ${report.summary.absentCount}</p>
        <p>Attendance Rate: ${report.summary.attendanceRate.toFixed(1)}%</p>

        <div style="margin-top: 60px;">
          <div style="display: inline-block; width: 300px; text-align: center;">
            <div style="border-bottom: 1px solid #000; height: 40px;"></div>
            <p>Teacher's Signature</p>
            <p>${report.teacher.name}</p>
          </div>
          <div style="display: inline-block; width: 300px; text-align: center; margin-left: 50px;">
            <div style="border-bottom: 1px solid #000; height: 40px;"></div>
            <p>Principal's Signature</p>
            <p>${report.schoolInfo.principalName}</p>
          </div>
        </div>
      </div>
    </body>
    </html>
    `
  }

  private static generateSF4HTML(report: SF4Report): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>SF4 Monthly Learner's Movement</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .info-section { margin-bottom: 20px; }
        .movement-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .movement-table th, .movement-table td { border: 1px solid #000; padding: 8px; }
        .signature-section { margin-top: 40px; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Republic of the Philippines</h1>
        <h2>Department of Education</h2>
        <h3>${report.schoolInfo.region}</h3>
        <h4>${report.schoolInfo.division}</h4>
        <h5>${report.schoolInfo.schoolName}</h5>
        <h2>SCHOOL FORM 4 (SF4)</h2>
        <h3>MONTHLY REPORT ON LEARNER'S MOVEMENT</h3>
      </div>

      <div class="info-section">
        <p><strong>Month:</strong> ${report.month} ${report.year}</p>
        <p><strong>Grade:</strong> ${report.grade}</p>
        ${report.section ? `<p><strong>Section:</strong> ${report.section}</p>` : ''}
      </div>

      <h4>ENROLLMENT SUMMARY</h4>
      <table class="movement-table">
        <tr>
          <td>Beginning of Month</td>
          <td>${report.enrollment.beginningOfMonth}</td>
        </tr>
        <tr>
          <td>New Admissions</td>
          <td>${report.enrollment.newAdmissions.length}</td>
        </tr>
        <tr>
          <td>Transfers Out</td>
          <td>${report.enrollment.transfers.transferredOut.length}</td>
        </tr>
        <tr>
          <td>Dropouts</td>
          <td>${report.enrollment.dropouts.length}</td>
        </tr>
        <tr>
          <td><strong>End of Month</strong></td>
          <td><strong>${report.enrollment.endOfMonth}</strong></td>
        </tr>
      </table>

      <h4>ATTENDANCE STATISTICS</h4>
      <table class="movement-table">
        <tr>
          <td>Total School Days</td>
          <td>${report.attendance.totalSchoolDays}</td>
        </tr>
        <tr>
          <td>Average Daily Attendance</td>
          <td>${report.attendance.averageAttendance}</td>
        </tr>
        <tr>
          <td>Attendance Rate</td>
          <td>${report.attendance.attendanceRate.toFixed(1)}%</td>
        </tr>
      </table>

      <div class="signature-section">
        <div style="margin-top: 60px;">
          <div style="display: inline-block; width: 300px; text-align: center;">
            <div style="border-bottom: 1px solid #000; height: 40px;"></div>
            <p>Prepared by</p>
            <p>Class Adviser/Teacher</p>
          </div>
          <div style="display: inline-block; width: 300px; text-align: center; margin-left: 50px;">
            <div style="border-bottom: 1px solid #000; height: 40px;"></div>
            <p>Principal's Signature</p>
            <p>${report.schoolInfo.principalName}</p>
          </div>
        </div>
      </div>
    </body>
    </html>
    `
  }

  private static generateCustomHTML(report: CustomReport): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>${report.name}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .report-table { width: 100%; border-collapse: collapse; }
        .report-table th, .report-table td { border: 1px solid #000; padding: 8px; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>${report.name}</h1>
        <p>${report.description}</p>
        <p>Generated on ${new Date(report.generatedAt).toLocaleDateString()}</p>
      </div>

      <table class="report-table">
        <thead>
          <tr>
            ${report.query.fields.map(field => `<th>${field}</th>`).join('')}
          </tr>
        </thead>
        <tbody>
          ${report.data.map(row => `
            <tr>
              ${report.query.fields.map(field => `<td>${row[field] || ''}</td>`).join('')}
            </tr>
          `).join('')}
        </tbody>
      </table>
    </body>
    </html>
    `
  }
}

// Enhanced Excel Generation for Reports
export class ReportExcelGenerator {
  static async generateSF2Excel(report: SF2Report): Promise<Blob> {
    // In a real implementation, this would use a library like SheetJS or ExcelJS
    const csvContent = this.generateSF2CSV(report)

    await new Promise(resolve => setTimeout(resolve, 500))

    return new Blob([csvContent], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
  }

  static async generateSF4Excel(report: SF4Report): Promise<Blob> {
    const csvContent = this.generateSF4CSV(report)

    await new Promise(resolve => setTimeout(resolve, 500))

    return new Blob([csvContent], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
  }

  static async generateCustomExcel(report: CustomReport): Promise<Blob> {
    const csvContent = this.generateCustomCSV(report)

    await new Promise(resolve => setTimeout(resolve, 500))

    return new Blob([csvContent], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
  }

  private static generateSF2CSV(report: SF2Report): string {
    const headers = ['No.', 'Student Name', 'Attendance Status', 'Remarks']
    const rows = report.students.map((student, index) => [
      index + 1,
      student.studentName,
      student.dailyStatus,
      student.remarks || ''
    ])

    return [
      `SF2 Daily Attendance Report - ${report.date}`,
      `Grade ${report.grade} - ${report.section}`,
      `Teacher: ${report.teacher.name}`,
      '',
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n')
  }

  private static generateSF4CSV(report: SF4Report): string {
    return [
      `SF4 Monthly Learner's Movement - ${report.month} ${report.year}`,
      `Grade ${report.grade}`,
      '',
      'Enrollment Summary',
      `Beginning of Month,${report.enrollment.beginningOfMonth}`,
      `New Admissions,${report.enrollment.newAdmissions.length}`,
      `Transfers Out,${report.enrollment.transfers.transferredOut.length}`,
      `Dropouts,${report.enrollment.dropouts.length}`,
      `End of Month,${report.enrollment.endOfMonth}`,
      '',
      'Attendance Statistics',
      `Total School Days,${report.attendance.totalSchoolDays}`,
      `Average Daily Attendance,${report.attendance.averageAttendance}`,
      `Attendance Rate,${report.attendance.attendanceRate.toFixed(1)}%`
    ].join('\n')
  }

  private static generateCustomCSV(report: CustomReport): string {
    const headers = report.query.fields
    const rows = report.data.map(row =>
      headers.map(field => row[field] || '').join(',')
    )

    return [
      report.name,
      report.description,
      `Generated on ${new Date(report.generatedAt).toLocaleDateString()}`,
      '',
      headers.join(','),
      ...rows
    ].join('\n')
  }
}

// Enhanced Export Manager for Reports
export class ReportExportManager {
  static async exportReport(
    report: SF2Report | SF4Report | CustomReport,
    format: ExportFormat
  ): Promise<{ blob: Blob; filename: string }> {
    let blob: Blob
    let filename: string

    // Determine report type
    const reportType = 'students' in report ? 'SF2' :
                      'enrollment' in report ? 'SF4' : 'CUSTOM'

    switch (format) {
      case 'PDF':
        if (reportType === 'SF2') {
          blob = await ReportPDFGenerator.generateSF2PDF(report as SF2Report)
          filename = `SF2_${(report as SF2Report).date}_Grade${(report as SF2Report).grade}.pdf`
        } else if (reportType === 'SF4') {
          blob = await ReportPDFGenerator.generateSF4PDF(report as SF4Report)
          filename = `SF4_${(report as SF4Report).month}_${(report as SF4Report).year}_Grade${(report as SF4Report).grade}.pdf`
        } else {
          blob = await ReportPDFGenerator.generateCustomPDF(report as CustomReport)
          filename = `${(report as CustomReport).name.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`
        }
        break

      case 'EXCEL':
        if (reportType === 'SF2') {
          blob = await ReportExcelGenerator.generateSF2Excel(report as SF2Report)
          filename = `SF2_${(report as SF2Report).date}_Grade${(report as SF2Report).grade}.xlsx`
        } else if (reportType === 'SF4') {
          blob = await ReportExcelGenerator.generateSF4Excel(report as SF4Report)
          filename = `SF4_${(report as SF4Report).month}_${(report as SF4Report).year}_Grade${(report as SF4Report).grade}.xlsx`
        } else {
          blob = await ReportExcelGenerator.generateCustomExcel(report as CustomReport)
          filename = `${(report as CustomReport).name.replace(/[^a-zA-Z0-9]/g, '_')}.xlsx`
        }
        break

      case 'CSV':
        if (reportType === 'SF2') {
          const csvContent = ReportExcelGenerator['generateSF2CSV'](report as SF2Report)
          blob = new Blob([csvContent], { type: 'text/csv' })
          filename = `SF2_${(report as SF2Report).date}_Grade${(report as SF2Report).grade}.csv`
        } else if (reportType === 'SF4') {
          const csvContent = ReportExcelGenerator['generateSF4CSV'](report as SF4Report)
          blob = new Blob([csvContent], { type: 'text/csv' })
          filename = `SF4_${(report as SF4Report).month}_${(report as SF4Report).year}_Grade${(report as SF4Report).grade}.csv`
        } else {
          const csvContent = ReportExcelGenerator['generateCustomCSV'](report as CustomReport)
          blob = new Blob([csvContent], { type: 'text/csv' })
          filename = `${(report as CustomReport).name.replace(/[^a-zA-Z0-9]/g, '_')}.csv`
        }
        break

      default:
        throw new Error(`Unsupported export format: ${format}`)
    }

    return { blob, filename }
  }

  static downloadFile(blob: Blob, filename: string) {
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}
