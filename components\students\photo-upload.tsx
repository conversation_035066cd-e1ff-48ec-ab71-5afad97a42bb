"use client"

import { useState, useRef, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Label } from "@/components/ui/label"
import { 
  Upload, 
  X, 
  User, 
  Camera, 
  AlertTriangle, 
  CheckCircle,
  Loader2,
  Image as ImageIcon
} from "lucide-react"
import { photoManager, PhotoValidationResult, PhotoMetadata } from "@/lib/utils/photo-management"
import { toast } from "sonner"
import { cn } from "@/lib/utils"

interface PhotoUploadProps {
  currentPhoto?: string
  onPhotoChange?: (photo: File | string | null) => void
  onPhotoUploaded?: (photoUrl: string) => void
  studentId?: string
  studentName?: string
  disabled?: boolean
  className?: string
}

export function PhotoUpload({
  currentPhoto,
  onPhotoChange,
  onPhotoUploaded,
  studentId,
  studentName,
  disabled = false,
  className
}: PhotoUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentPhoto || null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [validationResult, setValidationResult] = useState<PhotoValidationResult | null>(null)
  const [metadata, setMetadata] = useState<PhotoMetadata | null>(null)
  const [isDragOver, setIsDragOver] = useState(false)
  
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = useCallback(async (file: File) => {
    try {
      const result = await photoManager.processPhotoForUpload(file, {
        maxWidth: 800,
        maxHeight: 800,
        quality: 0.8,
        format: 'jpeg'
      })

      setSelectedFile(result.processedFile)
      setPreviewUrl(result.previewUrl)
      setValidationResult(result.validationResult)
      setMetadata(result.metadata)

      if (onPhotoChange) {
        onPhotoChange(result.processedFile)
      }

      if (result.validationResult.warnings?.length) {
        result.validationResult.warnings.forEach(warning => {
          toast.warning(warning)
        })
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process photo'
      toast.error(errorMessage)
      setValidationResult({ valid: false, error: errorMessage })
    }
  }, [onPhotoChange])

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    setIsDragOver(false)
    
    const file = event.dataTransfer.files[0]
    if (file && file.type.startsWith('image/')) {
      handleFileSelect(file)
    } else {
      toast.error('Please drop a valid image file')
    }
  }

  const handleUpload = async () => {
    if (!selectedFile || !studentId) return

    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 200)

      const photoUrl = await photoManager.uploadPhoto(selectedFile, studentId)
      
      clearInterval(progressInterval)
      setUploadProgress(100)

      if (onPhotoUploaded) {
        onPhotoUploaded(photoUrl)
      }

      toast.success('Photo uploaded successfully!')
      
      // Clean up
      setTimeout(() => {
        setUploadProgress(0)
        setIsUploading(false)
      }, 1000)

    } catch (error) {
      setIsUploading(false)
      setUploadProgress(0)
      toast.error('Failed to upload photo')
    }
  }

  const handleRemove = () => {
    if (previewUrl && previewUrl !== currentPhoto) {
      photoManager.revokePreviewUrl(previewUrl)
    }
    
    setSelectedFile(null)
    setPreviewUrl(currentPhoto || null)
    setValidationResult(null)
    setMetadata(null)
    
    if (onPhotoChange) {
      onPhotoChange(null)
    }
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleRemoveCurrentPhoto = () => {
    setPreviewUrl(null)
    if (onPhotoChange) {
      onPhotoChange(null)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Camera className="h-5 w-5" />
          Student Photo
        </CardTitle>
        <CardDescription>
          Upload a clear photo of the student for identification purposes
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Photo Preview */}
        <div className="flex flex-col items-center gap-4">
          <Avatar className="h-32 w-32 border-4 border-border">
            <AvatarImage src={previewUrl || undefined} alt={studentName || "Student photo"} />
            <AvatarFallback className="text-2xl">
              {studentName ? getInitials(studentName) : <User className="h-12 w-12" />}
            </AvatarFallback>
          </Avatar>

          {/* Photo Status */}
          {validationResult && (
            <div className="text-center">
              {validationResult.valid ? (
                <Badge variant="default" className="gap-1">
                  <CheckCircle className="h-3 w-3" />
                  Valid Photo
                </Badge>
              ) : (
                <Badge variant="destructive" className="gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  Invalid Photo
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Upload Area */}
        <div
          className={cn(
            "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
            isDragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25",
            disabled && "opacity-50 pointer-events-none"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <ImageIcon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <div className="space-y-2">
            <p className="text-lg font-medium">
              {selectedFile ? 'Photo Selected' : 'Drop your photo here'}
            </p>
            <p className="text-sm text-muted-foreground">
              or click to browse files
            </p>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileInputChange}
            className="hidden"
            disabled={disabled}
          />
          
          <Button 
            onClick={() => fileInputRef.current?.click()}
            className="mt-4"
            disabled={disabled}
          >
            <Upload className="mr-2 h-4 w-4" />
            Select Photo
          </Button>
        </div>

        {/* Photo Information */}
        {metadata && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Photo Information</Label>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <span className="text-muted-foreground">File Size:</span>
              <span>{(metadata.size / 1024).toFixed(1)} KB</span>
              
              <span className="text-muted-foreground">Dimensions:</span>
              <span>{metadata.dimensions.width} × {metadata.dimensions.height}</span>
              
              <span className="text-muted-foreground">Format:</span>
              <span>{metadata.type.split('/')[1].toUpperCase()}</span>
            </div>
          </div>
        )}

        {/* Upload Progress */}
        {isUploading && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Uploading photo...</span>
              <span>{uploadProgress}%</span>
            </div>
            <Progress value={uploadProgress} className="w-full" />
          </div>
        )}

        {/* Validation Errors */}
        {validationResult && !validationResult.valid && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {validationResult.error}
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex justify-center gap-2">
          {selectedFile && !isUploading && (
            <>
              {studentId && (
                <Button onClick={handleUpload} disabled={!validationResult?.valid}>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Photo
                </Button>
              )}
              
              <Button variant="outline" onClick={handleRemove}>
                <X className="mr-2 h-4 w-4" />
                Remove
              </Button>
            </>
          )}
          
          {previewUrl && !selectedFile && (
            <Button variant="outline" onClick={handleRemoveCurrentPhoto}>
              <X className="mr-2 h-4 w-4" />
              Remove Current Photo
            </Button>
          )}
          
          {isUploading && (
            <Button disabled>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </Button>
          )}
        </div>

        {/* Guidelines */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Photo Guidelines:</strong>
            <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
              <li>Use a clear, recent photo of the student</li>
              <li>Face should be clearly visible and well-lit</li>
              <li>Avoid sunglasses, hats, or face coverings</li>
              <li>Recommended size: 300×300 pixels or larger</li>
              <li>Supported formats: JPEG, PNG, WebP (max 10MB)</li>
            </ul>
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  )
}
