import { Student, getFullName } from "@/lib/types/student"

// Enhanced mock student data
export const mockStudentsData: Student[] = [
  {
    id: "123456789001",
    firstName: "<PERSON>",
    middleName: "<PERSON>",
    lastName: "<PERSON><PERSON>",
    email: "<EMAIL>",
    course: "Information Technology",
    year: "3rd Year",
    section: "IT-3A",
    grade: "11",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_123456789001_2025",
    dateOfBirth: "2007-05-15",
    gender: "Male",
    guardian: {
      name: "<PERSON>",
      phone: "+63 ************",
      email: "<EMAIL>",
      relationship: "Father",
      address: "123 Main St, Tanauan City"
    },
    emergencyContacts: [
      {
        name: "<PERSON>",
        phone: "+63 ************",
        relationship: "<PERSON>",
        address: "123 Main St, Tanauan City"
      },
      {
        name: "<PERSON>",
        phone: "+63 ************",
        relationship: "Uncle"
      }
    ],
    address: {
      street: "123 Main Street",
      barangay: "Poblacion",
      city: "Tanauan City",
      province: "Batangas",
      zipCode: "4232",
      country: "Philippines"
    },
    enrollmentDate: "2023-08-15",
    lastUpdated: "2025-01-15",
    attendanceStats: {
      totalDays: 120,
      presentDays: 110,
      lateDays: 8,
      absentDays: 2,
      attendanceRate: 91.7,
      lastAttendance: "2025-01-15"
    }
  },
  {
    id: "123456789002",
    firstName: "Jane",
    middleName: "Marie",
    lastName: "Smith",
    email: "<EMAIL>",
    course: "Computer Science",
    year: "2nd Year",
    section: "CS-2B",
    grade: "10",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_123456789002_2025",
    dateOfBirth: "2008-03-22",
    gender: "Female",
    guardian: {
      name: "Patricia Smith",
      phone: "+63 ************",
      email: "<EMAIL>",
      relationship: "Mother",
      address: "456 Oak Ave, Tanauan City"
    },
    emergencyContacts: [
      {
        name: "David Smith",
        phone: "+63 ************",
        relationship: "Father",
        address: "456 Oak Ave, Tanauan City"
      }
    ],
    address: {
      street: "456 Oak Avenue",
      barangay: "San Jose",
      city: "Tanauan City",
      province: "Batangas",
      zipCode: "4232",
      country: "Philippines"
    },
    enrollmentDate: "2023-08-15",
    lastUpdated: "2025-01-14",
    attendanceStats: {
      totalDays: 120,
      presentDays: 115,
      lateDays: 3,
      absentDays: 2,
      attendanceRate: 95.8,
      lastAttendance: "2025-01-14"
    }
  },
  {
    id: "123456789003",
    firstName: "Mike",
    lastName: "Johnson",
    email: "<EMAIL>",
    course: "Information Technology",
    year: "1st Year",
    section: "IT-1C",
    grade: "9",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_123456789003_2025",
    dateOfBirth: "2009-11-08",
    gender: "Male",
    guardian: {
      name: "Linda Johnson",
      phone: "+63 ************",
      email: "<EMAIL>",
      relationship: "Mother",
      address: "789 Pine St, Tanauan City"
    },
    emergencyContacts: [
      {
        name: "Mark Johnson",
        phone: "+63 ************",
        relationship: "Father"
      },
      {
        name: "Susan Johnson",
        phone: "+63 ************",
        relationship: "Grandmother",
        address: "321 Elm St, Tanauan City"
      }
    ],
    address: {
      street: "789 Pine Street",
      barangay: "Natatas",
      city: "Tanauan City",
      province: "Batangas",
      zipCode: "4232",
      country: "Philippines"
    },
    enrollmentDate: "2024-08-15",
    lastUpdated: "2025-01-13",
    attendanceStats: {
      totalDays: 120,
      presentDays: 105,
      lateDays: 12,
      absentDays: 3,
      attendanceRate: 87.5,
      lastAttendance: "2025-01-13"
    }
  },
  {
    id: "123456789004",
    firstName: "Sarah",
    middleName: "Grace",
    lastName: "Wilson",
    email: "<EMAIL>",
    course: "Computer Science",
    year: "4th Year",
    section: "CS-4A",
    grade: "12",
    status: "Active",
    photo: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_123456789004_2025",
    dateOfBirth: "2006-09-12",
    gender: "Female",
    guardian: {
      name: "Thomas Wilson",
      phone: "+63 ************",
      email: "<EMAIL>",
      relationship: "Father",
      address: "654 Maple Dr, Tanauan City"
    },
    emergencyContacts: [
      {
        name: "Helen Wilson",
        phone: "+63 ************",
        relationship: "Mother",
        address: "654 Maple Dr, Tanauan City"
      }
    ],
    address: {
      street: "654 Maple Drive",
      barangay: "Sambat",
      city: "Tanauan City",
      province: "Batangas",
      zipCode: "4232",
      country: "Philippines"
    },
    enrollmentDate: "2021-08-15",
    lastUpdated: "2025-01-12",
    attendanceStats: {
      totalDays: 120,
      presentDays: 118,
      lateDays: 1,
      absentDays: 1,
      attendanceRate: 98.3,
      lastAttendance: "2025-01-12"
    }
  },
  {
    id: "123456789005",
    firstName: "Alex",
    lastName: "Rodriguez",
    email: "<EMAIL>",
    course: "Information Technology",
    year: "2nd Year",
    section: "IT-2A",
    grade: "10",
    status: "Inactive",
    photo: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
    qrCode: "QR_123456789005_2025",
    dateOfBirth: "2008-07-25",
    gender: "Male",
    guardian: {
      name: "Maria Rodriguez",
      phone: "+63 ************",
      email: "<EMAIL>",
      relationship: "Mother",
      address: "987 Cedar Ln, Tanauan City"
    },
    emergencyContacts: [
      {
        name: "Carlos Rodriguez",
        phone: "+63 ************",
        relationship: "Father"
      }
    ],
    address: {
      street: "987 Cedar Lane",
      barangay: "Ulango",
      city: "Tanauan City",
      province: "Batangas",
      zipCode: "4232",
      country: "Philippines"
    },
    enrollmentDate: "2023-08-15",
    lastUpdated: "2024-12-15",
    attendanceStats: {
      totalDays: 120,
      presentDays: 85,
      lateDays: 15,
      absentDays: 20,
      attendanceRate: 70.8,
      lastAttendance: "2024-12-10"
    }
  }
]

// Helper functions
export const findStudentById = (id: string): Student | undefined => {
  return mockStudentsData.find(student => student.id === id)
}

export const findStudentByQRCode = (qrCode: string): Student | undefined => {
  return mockStudentsData.find(student => student.qrCode === qrCode)
}

export const getStudentsByGrade = (grade: string): Student[] => {
  return mockStudentsData.filter(student => student.grade === grade)
}

export const getStudentsBySection = (section: string): Student[] => {
  return mockStudentsData.filter(student => student.section === section)
}

export const getStudentsByStatus = (status: string): Student[] => {
  return mockStudentsData.filter(student => student.status === status)
}

// Get unique values for filters
export const getUniqueGrades = (): string[] => {
  return [...new Set(mockStudentsData.map(s => s.grade))].sort()
}

export const getUniqueSections = (): string[] => {
  return [...new Set(mockStudentsData.map(s => s.section).filter(Boolean))].sort()
}

export const getUniqueCourses = (): string[] => {
  return [...new Set(mockStudentsData.map(s => s.course))].sort()
}

export const getUniqueYears = (): string[] => {
  return [...new Set(mockStudentsData.map(s => s.year))].sort()
}

// Search function
export const searchStudents = (query: string): Student[] => {
  if (!query.trim()) return mockStudentsData
  
  const searchTerm = query.toLowerCase()
  return mockStudentsData.filter(student => {
    const fullName = getFullName(student).toLowerCase()
    return (
      fullName.includes(searchTerm) ||
      student.id.toLowerCase().includes(searchTerm) ||
      student.email.toLowerCase().includes(searchTerm) ||
      student.course.toLowerCase().includes(searchTerm) ||
      student.section?.toLowerCase().includes(searchTerm) ||
      student.guardian.name.toLowerCase().includes(searchTerm)
    )
  })
}
