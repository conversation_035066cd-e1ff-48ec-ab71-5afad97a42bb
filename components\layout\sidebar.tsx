"use client"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { 
  LayoutDashboard, 
  Users, 
  ClipboardCheck, 
  FileText, 
  BarChart3, 
  QrCode,
  GraduationCap
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

const sidebarItems = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard
  },
  {
    title: "Students",
    href: "/students", 
    icon: Users
  },
  {
    title: "Attendance",
    href: "/attendance",
    icon: ClipboardCheck
  },
  {
    title: "Reports",
    href: "/reports",
    icon: FileText
  },
  {
    title: "Analytics",
    href: "/analytics",
    icon: BarChart3
  },
  {
    title: "Scanner",
    href: "/scanner",
    icon: QrCode
  }
]

export function AppSidebar() {
  const pathname = usePathname()

  return (
    <div className="flex h-full w-64 flex-col bg-background border-r">
      {/* Logo Section */}
      <div className="flex items-center gap-3 p-6 border-b">
        <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
          <GraduationCap className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h2 className="text-lg font-semibold">QRSAMS</h2>
          <p className="text-xs text-muted-foreground">Tanauan School</p>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {sidebarItems.map((item) => {
          const Icon = item.icon
          const isActive = pathname === item.href
          
          return (
            <Button
              key={item.href}
              variant={isActive ? "secondary" : "ghost"}
              className={cn(
                "w-full justify-start gap-3 h-11",
                isActive && "bg-secondary"
              )}
              asChild
            >
              <Link href={item.href}>
                <Icon className="h-4 w-4" />
                {item.title}
              </Link>
            </Button>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t">
        <div className="text-center">
          <p className="text-xs text-muted-foreground">
            Tanauan School of Arts and Trade
          </p>
          <p className="text-xs text-muted-foreground">
            Brgy. Cabuynan, Tanauan, Leyte
          </p>
        </div>
      </div>
    </div>
  )
}
