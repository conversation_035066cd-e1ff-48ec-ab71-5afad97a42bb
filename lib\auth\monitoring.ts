import { prisma } from '@/lib/prisma'
import { SecurityEventType, SecuritySeverity, UserRole } from '@/lib/generated/prisma'
import { logSecurityEvent } from './security'

// Security monitoring configuration
const MONITORING_CONFIG = {
  // Failed login thresholds
  failedLoginThreshold: {
    perUser: 5,      // Max failed attempts per user
    perIP: 10,       // Max failed attempts per IP
    timeWindow: 15 * 60 * 1000 // 15 minutes
  },
  
  // Suspicious activity patterns
  suspiciousPatterns: {
    rapidLoginAttempts: 5,     // Attempts within 1 minute
    multipleUserAgents: 3,     // Different user agents from same IP
    offHoursAccess: true,      // Access outside business hours
    unusualLocations: true     // Access from new locations
  },
  
  // Business hours (24-hour format)
  businessHours: {
    start: 6,  // 6 AM
    end: 18    // 6 PM
  }
}

/**
 * Monitor and analyze security events
 */
export async function analyzeSecurityEvents(): Promise<{
  alerts: SecurityAlert[]
  summary: SecuritySummary
}> {
  try {
    const now = new Date()
    const timeWindow = new Date(now.getTime() - MONITORING_CONFIG.failedLoginThreshold.timeWindow)

    // Get recent security events
    const recentEvents = await prisma.securityEvent.findMany({
      where: {
        createdAt: { gte: timeWindow }
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // Get recent login attempts
    const recentLogins = await prisma.loginHistory.findMany({
      where: {
        loginAt: { gte: timeWindow }
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: { loginAt: 'desc' }
    })

    const alerts: SecurityAlert[] = []

    // Analyze failed login patterns
    alerts.push(...await analyzeFailedLogins(recentLogins))

    // Analyze suspicious IP activity
    alerts.push(...await analyzeSuspiciousIPs(recentLogins))

    // Analyze off-hours access
    alerts.push(...await analyzeOffHoursAccess(recentLogins))

    // Analyze privilege escalation attempts
    alerts.push(...await analyzePrivilegeEscalation(recentEvents))

    // Generate summary
    const summary = generateSecuritySummary(recentEvents, recentLogins)

    return { alerts, summary }
  } catch (error) {
    console.error('Security analysis error:', error)
    return {
      alerts: [],
      summary: {
        totalEvents: 0,
        criticalEvents: 0,
        highSeverityEvents: 0,
        failedLogins: 0,
        successfulLogins: 0,
        uniqueIPs: 0,
        suspiciousActivity: 0
      }
    }
  }
}

/**
 * Analyze failed login patterns
 */
async function analyzeFailedLogins(logins: any[]): Promise<SecurityAlert[]> {
  const alerts: SecurityAlert[] = []
  const failedLogins = logins.filter(login => !login.success)

  // Group by user
  const userFailures = failedLogins.reduce((acc, login) => {
    const userId = login.userId
    if (!acc[userId]) acc[userId] = []
    acc[userId].push(login)
    return acc
  }, {} as Record<string, any[]>)

  // Check for excessive failures per user
  for (const [userId, failures] of Object.entries(userFailures)) {
    if (failures.length >= MONITORING_CONFIG.failedLoginThreshold.perUser) {
      const user = failures[0].user
      alerts.push({
        id: `failed-login-user-${userId}`,
        type: 'EXCESSIVE_FAILED_LOGINS',
        severity: SecuritySeverity.HIGH,
        title: 'Excessive Failed Login Attempts',
        description: `User ${user?.username || 'Unknown'} has ${failures.length} failed login attempts`,
        userId,
        ipAddresses: [...new Set(failures.map(f => f.ipAddress))],
        timestamp: new Date(),
        count: failures.length,
        recommendation: 'Consider locking the account and investigating potential brute force attack'
      })
    }
  }

  // Group by IP
  const ipFailures = failedLogins.reduce((acc, login) => {
    const ip = login.ipAddress
    if (!acc[ip]) acc[ip] = []
    acc[ip].push(login)
    return acc
  }, {} as Record<string, any[]>)

  // Check for excessive failures per IP
  for (const [ip, failures] of Object.entries(ipFailures)) {
    if (failures.length >= MONITORING_CONFIG.failedLoginThreshold.perIP) {
      const uniqueUsers = new Set(failures.map(f => f.userId)).size
      alerts.push({
        id: `failed-login-ip-${ip}`,
        type: 'SUSPICIOUS_IP_ACTIVITY',
        severity: SecuritySeverity.CRITICAL,
        title: 'Suspicious IP Activity',
        description: `IP ${ip} has ${failures.length} failed login attempts across ${uniqueUsers} users`,
        ipAddresses: [ip],
        timestamp: new Date(),
        count: failures.length,
        recommendation: 'Consider blocking this IP address and investigating potential attack'
      })
    }
  }

  return alerts
}

/**
 * Analyze suspicious IP patterns
 */
async function analyzeSuspiciousIPs(logins: any[]): Promise<SecurityAlert[]> {
  const alerts: SecurityAlert[] = []

  // Group logins by IP
  const ipActivity = logins.reduce((acc, login) => {
    const ip = login.ipAddress
    if (!acc[ip]) {
      acc[ip] = {
        logins: [],
        userAgents: new Set(),
        users: new Set()
      }
    }
    acc[ip].logins.push(login)
    if (login.userAgent) acc[ip].userAgents.add(login.userAgent)
    acc[ip].users.add(login.userId)
    return acc
  }, {} as Record<string, any>)

  // Check for multiple user agents from same IP
  for (const [ip, activity] of Object.entries(ipActivity)) {
    if (activity.userAgents.size >= MONITORING_CONFIG.suspiciousPatterns.multipleUserAgents) {
      alerts.push({
        id: `multiple-ua-${ip}`,
        type: 'MULTIPLE_USER_AGENTS',
        severity: SecuritySeverity.MEDIUM,
        title: 'Multiple User Agents from Same IP',
        description: `IP ${ip} used ${activity.userAgents.size} different user agents`,
        ipAddresses: [ip],
        timestamp: new Date(),
        count: activity.userAgents.size,
        recommendation: 'Investigate potential bot activity or compromised network'
      })
    }

    // Check for rapid login attempts
    const rapidAttempts = activity.logins.filter((login: any, index: number) => {
      if (index === 0) return false
      const prevLogin = activity.logins[index - 1]
      return login.loginAt.getTime() - prevLogin.loginAt.getTime() < 60000 // 1 minute
    })

    if (rapidAttempts.length >= MONITORING_CONFIG.suspiciousPatterns.rapidLoginAttempts) {
      alerts.push({
        id: `rapid-attempts-${ip}`,
        type: 'RAPID_LOGIN_ATTEMPTS',
        severity: SecuritySeverity.HIGH,
        title: 'Rapid Login Attempts',
        description: `IP ${ip} made ${rapidAttempts.length} rapid login attempts`,
        ipAddresses: [ip],
        timestamp: new Date(),
        count: rapidAttempts.length,
        recommendation: 'Implement rate limiting and investigate automated attack'
      })
    }
  }

  return alerts
}

/**
 * Analyze off-hours access
 */
async function analyzeOffHoursAccess(logins: any[]): Promise<SecurityAlert[]> {
  const alerts: SecurityAlert[] = []

  if (!MONITORING_CONFIG.suspiciousPatterns.offHoursAccess) {
    return alerts
  }

  const offHoursLogins = logins.filter(login => {
    const hour = login.loginAt.getHours()
    return hour < MONITORING_CONFIG.businessHours.start || hour >= MONITORING_CONFIG.businessHours.end
  })

  // Group by user
  const userOffHours = offHoursLogins.reduce((acc, login) => {
    const userId = login.userId
    if (!acc[userId]) acc[userId] = []
    acc[userId].push(login)
    return acc
  }, {} as Record<string, any[]>)

  for (const [userId, logins] of Object.entries(userOffHours)) {
    if (logins.length >= 3) { // 3 or more off-hours logins
      const user = logins[0].user
      alerts.push({
        id: `off-hours-${userId}`,
        type: 'OFF_HOURS_ACCESS',
        severity: SecuritySeverity.MEDIUM,
        title: 'Off-Hours Access Pattern',
        description: `User ${user?.username || 'Unknown'} accessed system ${logins.length} times outside business hours`,
        userId,
        timestamp: new Date(),
        count: logins.length,
        recommendation: 'Verify if off-hours access is authorized for this user'
      })
    }
  }

  return alerts
}

/**
 * Analyze privilege escalation attempts
 */
async function analyzePrivilegeEscalation(events: any[]): Promise<SecurityAlert[]> {
  const alerts: SecurityAlert[] = []

  const privilegeEvents = events.filter(event => 
    event.eventType === SecurityEventType.PERMISSION_DENIED ||
    event.description.toLowerCase().includes('permission') ||
    event.description.toLowerCase().includes('access denied')
  )

  // Group by user
  const userDenials = privilegeEvents.reduce((acc, event) => {
    const userId = event.userId
    if (!userId) return acc
    if (!acc[userId]) acc[userId] = []
    acc[userId].push(event)
    return acc
  }, {} as Record<string, any[]>)

  for (const [userId, denials] of Object.entries(userDenials)) {
    if (denials.length >= 5) { // 5 or more permission denials
      const user = denials[0].user
      alerts.push({
        id: `privilege-escalation-${userId}`,
        type: 'PRIVILEGE_ESCALATION_ATTEMPT',
        severity: SecuritySeverity.HIGH,
        title: 'Potential Privilege Escalation',
        description: `User ${user?.username || 'Unknown'} had ${denials.length} permission denials`,
        userId,
        timestamp: new Date(),
        count: denials.length,
        recommendation: 'Review user permissions and investigate unauthorized access attempts'
      })
    }
  }

  return alerts
}

/**
 * Generate security summary
 */
function generateSecuritySummary(events: any[], logins: any[]): SecuritySummary {
  const criticalEvents = events.filter(e => e.severity === SecuritySeverity.CRITICAL).length
  const highSeverityEvents = events.filter(e => e.severity === SecuritySeverity.HIGH).length
  const failedLogins = logins.filter(l => !l.success).length
  const successfulLogins = logins.filter(l => l.success).length
  const uniqueIPs = new Set(logins.map(l => l.ipAddress)).size

  const suspiciousActivity = events.filter(e => 
    e.eventType === SecurityEventType.SUSPICIOUS_ACTIVITY ||
    e.eventType === SecurityEventType.PERMISSION_DENIED
  ).length

  return {
    totalEvents: events.length,
    criticalEvents,
    highSeverityEvents,
    failedLogins,
    successfulLogins,
    uniqueIPs,
    suspiciousActivity
  }
}

/**
 * Create security alert
 */
export async function createSecurityAlert(alert: Omit<SecurityAlert, 'id' | 'timestamp'>): Promise<void> {
  try {
    await logSecurityEvent({
      userId: alert.userId,
      eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
      severity: alert.severity,
      description: `${alert.title}: ${alert.description}`,
      ipAddress: alert.ipAddresses?.[0],
      additionalData: {
        alertType: alert.type,
        count: alert.count,
        recommendation: alert.recommendation
      }
    })
  } catch (error) {
    console.error('Failed to create security alert:', error)
  }
}

// Type definitions
interface SecurityAlert {
  id: string
  type: string
  severity: SecuritySeverity
  title: string
  description: string
  userId?: string
  ipAddresses?: string[]
  timestamp: Date
  count?: number
  recommendation?: string
}

interface SecuritySummary {
  totalEvents: number
  criticalEvents: number
  highSeverityEvents: number
  failedLogins: number
  successfulLogins: number
  uniqueIPs: number
  suspiciousActivity: number
}

/**
 * Get security dashboard data
 */
export async function getSecurityDashboard(): Promise<{
  alerts: SecurityAlert[]
  summary: SecuritySummary
  recentEvents: any[]
  topRiskyIPs: Array<{ ip: string; riskScore: number; events: number }>
}> {
  try {
    const { alerts, summary } = await analyzeSecurityEvents()

    // Get recent high-severity events
    const recentEvents = await prisma.securityEvent.findMany({
      where: {
        severity: {
          in: [SecuritySeverity.HIGH, SecuritySeverity.CRITICAL]
        }
      },
      include: {
        user: {
          select: {
            username: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    })

    // Calculate risky IPs
    const ipEvents = await prisma.securityEvent.groupBy({
      by: ['ipAddress'],
      where: {
        ipAddress: { not: null },
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      },
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      },
      take: 5
    })

    const topRiskyIPs = ipEvents.map(item => ({
      ip: item.ipAddress || 'Unknown',
      riskScore: Math.min(item._count.id * 10, 100), // Simple risk scoring
      events: item._count.id
    }))

    return {
      alerts,
      summary,
      recentEvents,
      topRiskyIPs
    }
  } catch (error) {
    console.error('Failed to get security dashboard:', error)
    return {
      alerts: [],
      summary: {
        totalEvents: 0,
        criticalEvents: 0,
        highSeverityEvents: 0,
        failedLogins: 0,
        successfulLogins: 0,
        uniqueIPs: 0,
        suspiciousActivity: 0
      },
      recentEvents: [],
      topRiskyIPs: []
    }
  }
}
