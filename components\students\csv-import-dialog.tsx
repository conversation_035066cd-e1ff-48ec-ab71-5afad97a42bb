"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { 
  Upload, 
  Download, 
  FileText, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Users,
  FileSpreadsheet
} from "lucide-react"
import { Student } from "@/lib/types/student"
import { CSVImportResult } from "@/lib/types/student"
import { toast } from "sonner"

interface CSVImportDialogProps {
  trigger?: React.ReactNode
  onStudentsImported?: (students: Student[]) => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

interface ImportStep {
  id: string
  title: string
  description: string
  completed: boolean
}

export function CSVImportDialog({
  trigger,
  onStudentsImported,
  open,
  onOpenChange
}: CSVImportDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [isProcessing, setIsProcessing] = useState(false)
  const [importResult, setImportResult] = useState<CSVImportResult | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewData, setPreviewData] = useState<any[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const steps: ImportStep[] = [
    {
      id: 'upload',
      title: 'Upload CSV File',
      description: 'Select and upload your student data CSV file',
      completed: false
    },
    {
      id: 'preview',
      title: 'Preview Data',
      description: 'Review the data before importing',
      completed: false
    },
    {
      id: 'import',
      title: 'Import Students',
      description: 'Process and import student records',
      completed: false
    },
    {
      id: 'complete',
      title: 'Complete',
      description: 'Import completed successfully',
      completed: false
    }
  ]

  const handleOpenChange = (newOpen: boolean) => {
    if (onOpenChange) {
      onOpenChange(newOpen)
    } else {
      setIsOpen(newOpen)
    }
    
    if (!newOpen) {
      // Reset state when dialog closes
      setCurrentStep(0)
      setSelectedFile(null)
      setPreviewData([])
      setImportResult(null)
      setIsProcessing(false)
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type === 'text/csv') {
      setSelectedFile(file)
      parseCSVPreview(file)
    } else {
      toast.error('Please select a valid CSV file')
    }
  }

  const parseCSVPreview = async (file: File) => {
    try {
      const text = await file.text()
      const lines = text.split('\n').filter(line => line.trim())
      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
      
      // Parse first 5 rows for preview
      const preview = lines.slice(1, 6).map(line => {
        const values = line.split(',').map(v => v.trim().replace(/"/g, ''))
        const row: any = {}
        headers.forEach((header, index) => {
          row[header] = values[index] || ''
        })
        return row
      })
      
      setPreviewData(preview)
      setCurrentStep(1)
    } catch (error) {
      toast.error('Failed to parse CSV file')
    }
  }

  const handleImport = async () => {
    if (!selectedFile) return

    setIsProcessing(true)
    setCurrentStep(2)

    try {
      // Simulate import process
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Mock import result
      const mockResult: CSVImportResult = {
        success: true,
        imported: previewData.length,
        failed: 0,
        errors: []
      }

      // Create mock students from preview data
      const importedStudents: Student[] = previewData.map((row, index) => ({
        id: row.id || `IMP${Date.now()}${index}`,
        firstName: row.firstName || row['First Name'] || 'Unknown',
        middleName: row.middleName || row['Middle Name'],
        lastName: row.lastName || row['Last Name'] || 'Student',
        email: row.email || row['Email'] || `student${index}@tanauan.edu.ph`,
        course: row.course || row['Course'] || 'General',
        year: row.year || row['Year'] || '1st Year',
        section: row.section || row['Section'],
        grade: (row.grade || row['Grade'] || '7') as any,
        status: 'Active' as any,
        guardian: {
          name: row.guardianName || row['Guardian Name'] || 'Unknown Guardian',
          phone: row.guardianPhone || row['Guardian Phone'] || '09000000000',
          email: row.guardianEmail || row['Guardian Email'],
          relationship: (row.guardianRelationship || row['Guardian Relationship'] || 'Guardian') as any,
          address: row.guardianAddress || row['Guardian Address']
        },
        emergencyContacts: [],
        address: {
          street: row.street || row['Street'] || 'Unknown Street',
          barangay: row.barangay || row['Barangay'] || 'Unknown Barangay',
          city: row.city || row['City'] || 'Tanauan City',
          province: row.province || row['Province'] || 'Batangas',
          zipCode: row.zipCode || row['ZIP Code'] || '4232',
          country: 'Philippines'
        },
        enrollmentDate: new Date().toISOString().split('T')[0],
        lastUpdated: new Date().toISOString(),
        qrCode: `QR_${row.id || `IMP${Date.now()}${index}`}_2025`
      }))

      setImportResult(mockResult)
      setCurrentStep(3)

      if (onStudentsImported) {
        onStudentsImported(importedStudents)
      }

      toast.success(`Successfully imported ${mockResult.imported} students`)
    } catch (error) {
      toast.error('Failed to import students')
      setImportResult({
        success: false,
        imported: 0,
        failed: previewData.length,
        errors: [{ row: 1, field: 'general', message: 'Import failed' }]
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const downloadTemplate = () => {
    const template = `id,firstName,middleName,lastName,email,grade,section,course,year,guardianName,guardianPhone,guardianEmail,guardianRelationship,street,barangay,city,province,zipCode
123456789001,John,Michael,Doe,<EMAIL>,11,A,Information Technology,3rd Year,Robert Doe,09123456789,<EMAIL>,Father,123 Main St,Poblacion,Tanauan City,Batangas,4232
123456789002,Jane,Marie,Smith,<EMAIL>,10,B,Computer Science,2nd Year,Patricia Smith,09123456790,<EMAIL>,Mother,456 Oak Ave,San Jose,Tanauan City,Batangas,4232`

    const blob = new Blob([template], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'student_import_template.csv'
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const dialogOpen = open !== undefined ? open : isOpen

  return (
    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      
      {!trigger && (
        <DialogTrigger asChild>
          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Import CSV
          </Button>
        </DialogTrigger>
      )}

      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Import Students from CSV
          </DialogTitle>
          <DialogDescription>
            Upload a CSV file to import multiple student records at once
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Steps */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors ${
                  index <= currentStep 
                    ? 'border-primary bg-primary text-primary-foreground' 
                    : 'border-muted-foreground bg-background'
                }`}>
                  {index < currentStep ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <span className="text-sm">{index + 1}</span>
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-12 h-0.5 mx-2 transition-colors ${
                    index < currentStep ? 'bg-primary' : 'bg-muted'
                  }`} />
                )}
              </div>
            ))}
          </div>

          {/* Step Content */}
          {currentStep === 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Upload CSV File</CardTitle>
                <CardDescription>
                  Select a CSV file containing student data to import
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <div className="space-y-2">
                    <p className="text-lg font-medium">Drop your CSV file here</p>
                    <p className="text-sm text-muted-foreground">or click to browse</p>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".csv"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  <Button 
                    onClick={() => fileInputRef.current?.click()}
                    className="mt-4"
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Select CSV File
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Need a template? Download our sample CSV file
                  </div>
                  <Button variant="outline" size="sm" onClick={downloadTemplate}>
                    <Download className="mr-2 h-4 w-4" />
                    Download Template
                  </Button>
                </div>

                {selectedFile && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      File selected: {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}

          {currentStep === 1 && previewData.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Preview Data</CardTitle>
                <CardDescription>
                  Review the first few rows of your data before importing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-border">
                    <thead>
                      <tr className="bg-muted">
                        {Object.keys(previewData[0]).map(header => (
                          <th key={header} className="border border-border p-2 text-left text-sm font-medium">
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {previewData.map((row, index) => (
                        <tr key={index}>
                          {Object.values(row).map((value: any, cellIndex) => (
                            <td key={cellIndex} className="border border-border p-2 text-sm">
                              {value || '-'}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                <div className="flex justify-between mt-4">
                  <Button variant="outline" onClick={() => setCurrentStep(0)}>
                    Back
                  </Button>
                  <Button onClick={handleImport}>
                    <Users className="mr-2 h-4 w-4" />
                    Import {previewData.length} Students
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {currentStep === 2 && (
            <Card>
              <CardHeader>
                <CardTitle>Importing Students</CardTitle>
                <CardDescription>
                  Please wait while we process your student data
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-lg font-medium">Processing student records...</p>
                  <p className="text-sm text-muted-foreground">This may take a few moments</p>
                </div>
                <Progress value={isProcessing ? 50 : 100} className="w-full" />
              </CardContent>
            </Card>
          )}

          {currentStep === 3 && importResult && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {importResult.success ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )}
                  Import {importResult.success ? 'Completed' : 'Failed'}
                </CardTitle>
                <CardDescription>
                  {importResult.success 
                    ? 'Your student data has been successfully imported'
                    : 'There were errors during the import process'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{importResult.imported}</div>
                    <div className="text-sm text-muted-foreground">Imported</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{importResult.failed}</div>
                    <div className="text-sm text-muted-foreground">Failed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{importResult.imported + importResult.failed}</div>
                    <div className="text-sm text-muted-foreground">Total</div>
                  </div>
                </div>

                {importResult.errors.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-destructive">Errors:</h4>
                    {importResult.errors.map((error, index) => (
                      <Alert key={index} variant="destructive">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          Row {error.row}, Field "{error.field}": {error.message}
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                )}

                <div className="flex justify-end">
                  <Button onClick={() => handleOpenChange(false)}>
                    Close
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
