"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  QrCode,
  Download,
  Printer,
  Grid3X3,
  FileImage,
  CheckCircle,
  Users,
  Settings
} from "lucide-react"
import { Student, getFullName } from "@/lib/types/student"
import { toast } from "sonner"

interface QRBatchGeneratorProps {
  students: Student[]
  selectedStudents?: string[]
  trigger?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

interface QRGenerationOptions {
  format: 'individual' | 'sheet'
  size: 'small' | 'medium' | 'large'
  includeStudentInfo: boolean
  includeSchoolLogo: boolean
  codesPerSheet: number
  paperSize: 'A4' | 'Letter'
}

export function QRBatchGenerator({
  students,
  selectedStudents = [],
  trigger,
  open,
  onOpenChange
}: QRBatchGeneratorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState<'select' | 'options' | 'generate' | 'complete'>('select')
  const [selectedForGeneration, setSelectedForGeneration] = useState<string[]>(selectedStudents)
  const [options, setOptions] = useState<QRGenerationOptions>({
    format: 'sheet',
    size: 'medium',
    includeStudentInfo: true,
    includeSchoolLogo: true,
    codesPerSheet: 12,
    paperSize: 'A4'
  })

  const handleOpenChange = (newOpen: boolean) => {
    if (onOpenChange) {
      onOpenChange(newOpen)
    } else {
      setIsOpen(newOpen)
    }
    
    if (!newOpen) {
      // Reset state when dialog closes
      setCurrentStep('select')
      setIsGenerating(false)
      setGenerationProgress(0)
    }
  }

  const studentsToGenerate = students.filter(s => selectedForGeneration.includes(s.id))

  const handleStudentToggle = (studentId: string, checked: boolean) => {
    if (checked) {
      setSelectedForGeneration(prev => [...prev, studentId])
    } else {
      setSelectedForGeneration(prev => prev.filter(id => id !== studentId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedForGeneration(students.map(s => s.id))
    } else {
      setSelectedForGeneration([])
    }
  }

  const handleGenerate = async () => {
    if (studentsToGenerate.length === 0) {
      toast.error('Please select at least one student')
      return
    }

    setIsGenerating(true)
    setCurrentStep('generate')
    setGenerationProgress(0)

    try {
      // Simulate QR code generation with progress
      for (let i = 0; i <= 100; i += 10) {
        setGenerationProgress(i)
        await new Promise(resolve => setTimeout(resolve, 200))
      }

      setCurrentStep('complete')
      toast.success(`Generated QR codes for ${studentsToGenerate.length} students`)
    } catch (error) {
      toast.error('Failed to generate QR codes')
      setCurrentStep('options')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleDownload = () => {
    // Simulate download
    toast.success('QR codes downloaded successfully')
    console.log('Downloading QR codes for:', studentsToGenerate.map(s => s.id))
  }

  const handlePrint = () => {
    // Simulate print
    toast.success('QR codes sent to printer')
    console.log('Printing QR codes for:', studentsToGenerate.map(s => s.id))
  }

  const dialogOpen = open !== undefined ? open : isOpen

  return (
    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      
      {!trigger && (
        <DialogTrigger asChild>
          <Button variant="outline">
            <QrCode className="mr-2 h-4 w-4" />
            Generate QR Codes
          </Button>
        </DialogTrigger>
      )}

      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            Batch QR Code Generator
          </DialogTitle>
          <DialogDescription>
            Generate QR codes for multiple students at once
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Step 1: Select Students */}
          {currentStep === 'select' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Select Students</span>
                  <Badge variant="secondary">{selectedForGeneration.length} selected</Badge>
                </CardTitle>
                <CardDescription>
                  Choose which students to generate QR codes for
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="select-all"
                    checked={selectedForGeneration.length === students.length}
                    onCheckedChange={handleSelectAll}
                  />
                  <Label htmlFor="select-all" className="font-medium">
                    Select All Students ({students.length})
                  </Label>
                </div>

                <Separator />

                <div className="max-h-60 overflow-y-auto space-y-2">
                  {students.map(student => {
                    const fullName = getFullName(student)
                    const isSelected = selectedForGeneration.includes(student.id)
                    
                    return (
                      <div key={student.id} className="flex items-center space-x-2 p-2 rounded hover:bg-muted">
                        <Checkbox
                          id={`student-${student.id}`}
                          checked={isSelected}
                          onCheckedChange={(checked) => handleStudentToggle(student.id, checked as boolean)}
                        />
                        <div className="flex-1">
                          <Label htmlFor={`student-${student.id}`} className="font-medium cursor-pointer">
                            {fullName}
                          </Label>
                          <div className="text-sm text-muted-foreground">
                            {student.id} • Grade {student.grade} • {student.course}
                          </div>
                        </div>
                        <Badge variant={student.status === 'Active' ? 'default' : 'secondary'}>
                          {student.status}
                        </Badge>
                      </div>
                    )
                  })}
                </div>

                <div className="flex justify-end">
                  <Button 
                    onClick={() => setCurrentStep('options')}
                    disabled={selectedForGeneration.length === 0}
                  >
                    Next: Configure Options
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Generation Options */}
          {currentStep === 'options' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Generation Options
                </CardTitle>
                <CardDescription>
                  Configure how the QR codes should be generated
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Format Options */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Output Format</Label>
                    <Select value={options.format} onValueChange={(value: any) => setOptions(prev => ({ ...prev, format: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="individual">
                          <div className="flex items-center gap-2">
                            <FileImage className="h-4 w-4" />
                            Individual Files
                          </div>
                        </SelectItem>
                        <SelectItem value="sheet">
                          <div className="flex items-center gap-2">
                            <Grid3X3 className="h-4 w-4" />
                            Print Sheet
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Size Options */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">QR Code Size</Label>
                    <Select value={options.size} onValueChange={(value: any) => setOptions(prev => ({ ...prev, size: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Small (1 inch)</SelectItem>
                        <SelectItem value="medium">Medium (1.5 inches)</SelectItem>
                        <SelectItem value="large">Large (2 inches)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Paper Size (for sheet format) */}
                  {options.format === 'sheet' && (
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Paper Size</Label>
                      <Select value={options.paperSize} onValueChange={(value: any) => setOptions(prev => ({ ...prev, paperSize: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="A4">A4 (210 × 297 mm)</SelectItem>
                          <SelectItem value="Letter">Letter (8.5 × 11 in)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Codes per sheet */}
                  {options.format === 'sheet' && (
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Codes per Sheet</Label>
                      <Select value={options.codesPerSheet.toString()} onValueChange={(value) => setOptions(prev => ({ ...prev, codesPerSheet: parseInt(value) }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="6">6 codes per sheet</SelectItem>
                          <SelectItem value="12">12 codes per sheet</SelectItem>
                          <SelectItem value="20">20 codes per sheet</SelectItem>
                          <SelectItem value="30">30 codes per sheet</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                {/* Additional Options */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Additional Options</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-info"
                        checked={options.includeStudentInfo}
                        onCheckedChange={(checked) => setOptions(prev => ({ ...prev, includeStudentInfo: checked as boolean }))}
                      />
                      <Label htmlFor="include-info">Include student name and ID</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-logo"
                        checked={options.includeSchoolLogo}
                        onCheckedChange={(checked) => setOptions(prev => ({ ...prev, includeSchoolLogo: checked as boolean }))}
                      />
                      <Label htmlFor="include-logo">Include school logo</Label>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setCurrentStep('select')}>
                    Back
                  </Button>
                  <Button onClick={handleGenerate}>
                    <QrCode className="mr-2 h-4 w-4" />
                    Generate {studentsToGenerate.length} QR Codes
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Generating */}
          {currentStep === 'generate' && (
            <Card>
              <CardHeader>
                <CardTitle>Generating QR Codes</CardTitle>
                <CardDescription>
                  Please wait while we generate QR codes for {studentsToGenerate.length} students
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-lg font-medium">Generating QR codes...</p>
                  <p className="text-sm text-muted-foreground">
                    Processing {studentsToGenerate.length} student records
                  </p>
                </div>
                <Progress value={generationProgress} className="w-full" />
                <div className="text-center text-sm text-muted-foreground">
                  {generationProgress}% complete
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 4: Complete */}
          {currentStep === 'complete' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  QR Codes Generated Successfully
                </CardTitle>
                <CardDescription>
                  {studentsToGenerate.length} QR codes have been generated and are ready for download or printing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-green-600">{studentsToGenerate.length}</div>
                    <div className="text-sm text-muted-foreground">QR Codes Generated</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{options.format === 'sheet' ? Math.ceil(studentsToGenerate.length / options.codesPerSheet) : studentsToGenerate.length}</div>
                    <div className="text-sm text-muted-foreground">{options.format === 'sheet' ? 'Print Sheets' : 'Individual Files'}</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{options.size}</div>
                    <div className="text-sm text-muted-foreground">Size</div>
                  </div>
                </div>

                <Separator />

                <div className="flex justify-center gap-4">
                  <Button onClick={handleDownload}>
                    <Download className="mr-2 h-4 w-4" />
                    Download Files
                  </Button>
                  <Button variant="outline" onClick={handlePrint}>
                    <Printer className="mr-2 h-4 w-4" />
                    Print Now
                  </Button>
                </div>

                <div className="text-center">
                  <Button variant="ghost" onClick={() => handleOpenChange(false)}>
                    Close
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
