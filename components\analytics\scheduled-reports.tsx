"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { 
  Calendar, 
  Clock, 
  Mail, 
  Plus, 
  Edit, 
  Trash2, 
  Play, 
  Pause,
  FileText,
  Users,
  Settings,
  CheckCircle,
  AlertCircle
} from "lucide-react"

interface ScheduledReport {
  id: string
  name: string
  description: string
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  format: 'pdf' | 'excel' | 'csv'
  recipients: string[]
  filters: {
    grades: string[]
    riskLevels: string[]
    includeCharts: boolean
    includeInsights: boolean
  }
  active: boolean
  nextRun: string
  lastRun?: string
  createdAt: string
  createdBy: string
}

interface ScheduledReportsProps {
  reports?: ScheduledReport[]
  onCreateReport?: (report: Omit<ScheduledReport, 'id' | 'createdAt' | 'createdBy'>) => void
  onUpdateReport?: (id: string, updates: Partial<ScheduledReport>) => void
  onDeleteReport?: (id: string) => void
}

export function ScheduledReports({ 
  reports = [], 
  onCreateReport, 
  onUpdateReport, 
  onDeleteReport 
}: ScheduledReportsProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingReport, setEditingReport] = useState<ScheduledReport | null>(null)
  const [newReport, setNewReport] = useState({
    name: '',
    description: '',
    frequency: 'weekly' as const,
    format: 'pdf' as const,
    recipients: [''],
    filters: {
      grades: [],
      riskLevels: [],
      includeCharts: true,
      includeInsights: true
    },
    active: true,
    nextRun: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
  })

  // Mock data if no reports provided
  const mockReports: ScheduledReport[] = [
    {
      id: 'RPT001',
      name: 'Weekly Attendance Summary',
      description: 'Comprehensive weekly attendance report for all grades',
      frequency: 'weekly',
      format: 'pdf',
      recipients: ['<EMAIL>', '<EMAIL>'],
      filters: {
        grades: [],
        riskLevels: [],
        includeCharts: true,
        includeInsights: true
      },
      active: true,
      nextRun: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      lastRun: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      createdBy: '<EMAIL>'
    },
    {
      id: 'RPT002',
      name: 'Monthly Risk Assessment',
      description: 'Monthly report focusing on at-risk students and interventions',
      frequency: 'monthly',
      format: 'excel',
      recipients: ['<EMAIL>', '<EMAIL>'],
      filters: {
        grades: [],
        riskLevels: ['high', 'critical'],
        includeCharts: true,
        includeInsights: true
      },
      active: true,
      nextRun: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
      lastRun: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
      createdBy: '<EMAIL>'
    },
    {
      id: 'RPT003',
      name: 'Daily Grade 12 Report',
      description: 'Daily attendance tracking for graduating students',
      frequency: 'daily',
      format: 'csv',
      recipients: ['<EMAIL>'],
      filters: {
        grades: ['12'],
        riskLevels: [],
        includeCharts: false,
        includeInsights: false
      },
      active: false,
      nextRun: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
      createdBy: '<EMAIL>'
    }
  ]

  const displayReports = reports.length > 0 ? reports : mockReports

  const handleCreateReport = () => {
    onCreateReport?.(newReport)
    setIsCreateDialogOpen(false)
    setNewReport({
      name: '',
      description: '',
      frequency: 'weekly',
      format: 'pdf',
      recipients: [''],
      filters: {
        grades: [],
        riskLevels: [],
        includeCharts: true,
        includeInsights: true
      },
      active: true,
      nextRun: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    })
  }

  const handleToggleActive = (id: string, active: boolean) => {
    onUpdateReport?.(id, { active })
  }

  const addRecipient = () => {
    setNewReport(prev => ({
      ...prev,
      recipients: [...prev.recipients, '']
    }))
  }

  const updateRecipient = (index: number, email: string) => {
    setNewReport(prev => ({
      ...prev,
      recipients: prev.recipients.map((r, i) => i === index ? email : r)
    }))
  }

  const removeRecipient = (index: number) => {
    setNewReport(prev => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index)
    }))
  }

  const getFrequencyBadge = (frequency: string) => {
    const colors = {
      daily: 'bg-blue-100 text-blue-800',
      weekly: 'bg-green-100 text-green-800',
      monthly: 'bg-orange-100 text-orange-800',
      quarterly: 'bg-purple-100 text-purple-800'
    }
    return colors[frequency as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf': return <FileText className="h-4 w-4" />
      case 'excel': return <FileText className="h-4 w-4" />
      case 'csv': return <FileText className="h-4 w-4" />
      default: return <FileText className="h-4 w-4" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold flex items-center gap-2">
            <Calendar className="h-6 w-6" />
            Scheduled Reports
          </h3>
          <p className="text-muted-foreground">
            Automate report generation and delivery
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Report
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Scheduled Report</DialogTitle>
              <DialogDescription>
                Set up automated report generation and delivery
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="report-name">Report Name</Label>
                  <Input
                    id="report-name"
                    value={newReport.name}
                    onChange={(e) => setNewReport(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Weekly Attendance Report"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="frequency">Frequency</Label>
                  <Select value={newReport.frequency} onValueChange={(value: any) => setNewReport(prev => ({ ...prev, frequency: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="quarterly">Quarterly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newReport.description}
                  onChange={(e) => setNewReport(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Brief description of the report content and purpose"
                />
              </div>

              <div className="space-y-2">
                <Label>Recipients</Label>
                {newReport.recipients.map((recipient, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={recipient}
                      onChange={(e) => updateRecipient(index, e.target.value)}
                      placeholder="<EMAIL>"
                    />
                    {newReport.recipients.length > 1 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeRecipient(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button variant="outline" size="sm" onClick={addRecipient}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Recipient
                </Button>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Format</Label>
                  <Select value={newReport.format} onValueChange={(value: any) => setNewReport(prev => ({ ...prev, format: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF Report</SelectItem>
                      <SelectItem value="excel">Excel Workbook</SelectItem>
                      <SelectItem value="csv">CSV Data</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Grades Filter</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="All grades" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Grades</SelectItem>
                      <SelectItem value="7">Grade 7</SelectItem>
                      <SelectItem value="8">Grade 8</SelectItem>
                      <SelectItem value="9">Grade 9</SelectItem>
                      <SelectItem value="10">Grade 10</SelectItem>
                      <SelectItem value="11">Grade 11</SelectItem>
                      <SelectItem value="12">Grade 12</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-3">
                <Label>Content Options</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="include-charts"
                      checked={newReport.filters.includeCharts}
                      onCheckedChange={(checked) => setNewReport(prev => ({
                        ...prev,
                        filters: { ...prev.filters, includeCharts: checked }
                      }))}
                    />
                    <Label htmlFor="include-charts">Include Charts</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="include-insights"
                      checked={newReport.filters.includeInsights}
                      onCheckedChange={(checked) => setNewReport(prev => ({
                        ...prev,
                        filters: { ...prev.filters, includeInsights: checked }
                      }))}
                    />
                    <Label htmlFor="include-insights">Include AI Insights</Label>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateReport}>
                Create Report
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Reports List */}
      <div className="space-y-4">
        {displayReports.map((report) => (
          <Card key={report.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    {getFormatIcon(report.format)}
                    {report.name}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {report.description}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getFrequencyBadge(report.frequency)}>
                    {report.frequency}
                  </Badge>
                  <Switch
                    checked={report.active}
                    onCheckedChange={(checked) => handleToggleActive(report.id, checked)}
                  />
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <div className="space-y-4">
                {/* Report Details */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <div className="space-y-1">
                    <div className="text-sm font-medium">Recipients</div>
                    <div className="text-sm text-muted-foreground">
                      {report.recipients.length} recipient{report.recipients.length !== 1 ? 's' : ''}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium">Next Run</div>
                    <div className="text-sm text-muted-foreground">
                      {formatDate(report.nextRun)}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium">Last Run</div>
                    <div className="text-sm text-muted-foreground">
                      {report.lastRun ? formatDate(report.lastRun) : 'Never'}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium">Status</div>
                    <div className="flex items-center gap-2">
                      {report.active ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-gray-500" />
                      )}
                      <span className="text-sm text-muted-foreground">
                        {report.active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Filters */}
                <div className="space-y-2">
                  <div className="text-sm font-medium">Filters & Content</div>
                  <div className="flex flex-wrap gap-2">
                    {report.filters.grades.length > 0 && (
                      <Badge variant="outline">
                        Grades: {report.filters.grades.join(', ')}
                      </Badge>
                    )}
                    {report.filters.riskLevels.length > 0 && (
                      <Badge variant="outline">
                        Risk: {report.filters.riskLevels.join(', ')}
                      </Badge>
                    )}
                    {report.filters.includeCharts && (
                      <Badge variant="outline">Charts</Badge>
                    )}
                    {report.filters.includeInsights && (
                      <Badge variant="outline">AI Insights</Badge>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="text-xs text-muted-foreground">
                    Created by {report.createdBy} on {formatDate(report.createdAt)}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Play className="h-4 w-4 mr-2" />
                      Run Now
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onDeleteReport?.(report.id)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {displayReports.length === 0 && (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground mb-4">No scheduled reports yet</p>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Report
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
