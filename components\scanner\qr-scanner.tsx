"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { BrowserMultiFormatReader, NotFoundException } from "@zxing/library"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Camera, CameraOff, RotateCcw, Settings } from "lucide-react"
import { CameraDevice, ScanResult } from "@/lib/types/scanner"

interface QRScannerProps {
  onScanResult: (result: ScanResult) => void
  isActive: boolean
  onCameraError?: (error: string) => void
}

export function QRScanner({ onScanResult, isActive, onCameraError }: QRScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const readerRef = useRef<BrowserMultiFormatReader | null>(null)
  const [isScanning, setIsScanning] = useState(false)
  const [availableCameras, setAvailableCameras] = useState<CameraDevice[]>([])
  const [selectedCamera, setSelectedCamera] = useState<string>("")
  const [error, setError] = useState<string>("")
  const [hasPermission, setHasPermission] = useState<boolean | null>(null)

  // Initialize camera reader
  useEffect(() => {
    readerRef.current = new BrowserMultiFormatReader()
    
    return () => {
      if (readerRef.current) {
        readerRef.current.reset()
      }
    }
  }, [])

  // Get available cameras
  const getAvailableCameras = useCallback(async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      const videoDevices = devices
        .filter(device => device.kind === 'videoinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `Camera ${device.deviceId.slice(0, 8)}`,
          kind: 'videoinput' as const
        }))
      
      setAvailableCameras(videoDevices)
      
      // Select rear camera by default if available
      const rearCamera = videoDevices.find(camera => 
        camera.label.toLowerCase().includes('back') || 
        camera.label.toLowerCase().includes('rear')
      )
      setSelectedCamera(rearCamera?.deviceId || videoDevices[0]?.deviceId || "")
    } catch (err) {
      console.error("Error getting cameras:", err)
      setError("Failed to access cameras")
      onCameraError?.("Failed to access cameras")
    }
  }, [onCameraError])

  // Request camera permission
  const requestCameraPermission = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true })
      stream.getTracks().forEach(track => track.stop()) // Stop the test stream
      setHasPermission(true)
      await getAvailableCameras()
    } catch (err) {
      console.error("Camera permission denied:", err)
      setHasPermission(false)
      setError("Camera permission denied")
      onCameraError?.("Camera permission denied")
    }
  }, [getAvailableCameras, onCameraError])

  // Start scanning
  const startScanning = useCallback(async () => {
    if (!readerRef.current || !selectedCamera || !videoRef.current) return

    try {
      setIsScanning(true)
      setError("")

      await readerRef.current.decodeFromVideoDevice(
        selectedCamera,
        videoRef.current,
        (result, error) => {
          if (result) {
            const scanResult: ScanResult = {
              success: true,
              data: result.getText(),
              timestamp: new Date()
            }
            onScanResult(scanResult)
            
            // Brief pause between scans to prevent rapid duplicate scans
            setTimeout(() => {
              if (isActive && isScanning) {
                // Continue scanning
              }
            }, 1000)
          }
          
          if (error && !(error instanceof NotFoundException)) {
            console.error("Scan error:", error)
          }
        }
      )
    } catch (err) {
      console.error("Failed to start scanning:", err)
      setError("Failed to start camera")
      setIsScanning(false)
      onCameraError?.("Failed to start camera")
    }
  }, [selectedCamera, onScanResult, isActive, isScanning, onCameraError])

  // Stop scanning
  const stopScanning = useCallback(() => {
    if (readerRef.current) {
      readerRef.current.reset()
    }
    setIsScanning(false)
  }, [])

  // Handle camera switch
  const switchCamera = useCallback(() => {
    const currentIndex = availableCameras.findIndex(cam => cam.deviceId === selectedCamera)
    const nextIndex = (currentIndex + 1) % availableCameras.length
    setSelectedCamera(availableCameras[nextIndex]?.deviceId || "")
  }, [availableCameras, selectedCamera])

  // Effect to start/stop scanning based on isActive prop
  useEffect(() => {
    if (isActive && hasPermission && selectedCamera && !isScanning) {
      startScanning()
    } else if (!isActive && isScanning) {
      stopScanning()
    }
  }, [isActive, hasPermission, selectedCamera, isScanning, startScanning, stopScanning])

  // Effect to restart scanning when camera changes
  useEffect(() => {
    if (isScanning && selectedCamera) {
      stopScanning()
      setTimeout(() => {
        if (isActive) {
          startScanning()
        }
      }, 100)
    }
  }, [selectedCamera, isScanning, isActive, startScanning, stopScanning])

  // Initial permission request
  useEffect(() => {
    if (hasPermission === null) {
      requestCameraPermission()
    }
  }, [hasPermission, requestCameraPermission])

  if (hasPermission === false) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <CameraOff className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-semibold mb-2">Camera Permission Required</h3>
          <p className="text-muted-foreground mb-4">
            Please allow camera access to use the QR scanner
          </p>
          <Button onClick={requestCameraPermission}>
            <Camera className="mr-2 h-4 w-4" />
            Grant Camera Access
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Camera Controls */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={switchCamera}
            disabled={availableCameras.length <= 1}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Switch Camera
          </Button>
        </div>
        
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${isScanning ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm text-muted-foreground">
            {isScanning ? 'Scanning' : 'Stopped'}
          </span>
        </div>
      </div>

      {/* Scanner Interface */}
      <Card className="relative overflow-hidden">
        <CardContent className="p-0">
          <div className="relative aspect-square max-w-2xl mx-auto bg-black">
            {/* Video Element */}
            <video
              ref={videoRef}
              className="w-full h-full object-cover"
              playsInline
              muted
            />
            
            {/* Scanning Overlay */}
            <div className="absolute inset-0 pointer-events-none">
              {/* Scanning Reticle */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative w-64 h-64 border-2 border-white/50 rounded-lg">
                  {/* Corner indicators */}
                  <div className="absolute top-0 left-0 w-8 h-8 border-t-4 border-l-4 border-primary rounded-tl-lg" />
                  <div className="absolute top-0 right-0 w-8 h-8 border-t-4 border-r-4 border-primary rounded-tr-lg" />
                  <div className="absolute bottom-0 left-0 w-8 h-8 border-b-4 border-l-4 border-primary rounded-bl-lg" />
                  <div className="absolute bottom-0 right-0 w-8 h-8 border-b-4 border-r-4 border-primary rounded-br-lg" />
                  
                  {/* Scanning line animation */}
                  {isScanning && (
                    <div className="absolute inset-x-0 top-0 h-1 bg-primary animate-pulse" 
                         style={{
                           animation: 'scan 2s linear infinite',
                         }} />
                  )}
                </div>
              </div>
              
              {/* Instructions */}
              <div className="absolute bottom-4 left-0 right-0 text-center">
                <p className="text-white text-lg font-medium bg-black/50 px-4 py-2 rounded-lg inline-block">
                  Position QR code within the frame
                </p>
              </div>
            </div>
            
            {/* Error Overlay */}
            {error && (
              <div className="absolute inset-0 bg-black/80 flex items-center justify-center">
                <div className="text-center text-white">
                  <CameraOff className="h-16 w-16 mx-auto mb-4" />
                  <p className="text-lg font-medium mb-2">Camera Error</p>
                  <p className="text-sm opacity-75 mb-4">{error}</p>
                  <Button variant="secondary" onClick={requestCameraPermission}>
                    <Camera className="mr-2 h-4 w-4" />
                    Retry
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Hidden canvas for image processing */}
      <canvas ref={canvasRef} className="hidden" />
      
      <style jsx>{`
        @keyframes scan {
          0% { top: 0; }
          50% { top: calc(100% - 4px); }
          100% { top: 0; }
        }
      `}</style>
    </div>
  )
}
