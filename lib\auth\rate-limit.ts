import { NextRequest, NextResponse } from 'next/server'
import { RateLimitInfo } from './types'

// In-memory store for rate limiting (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Rate limit configurations
export const RATE_LIMITS = {
  // Authentication endpoints
  login: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    message: 'Too many login attempts. Please try again later.'
  },
  
  // Password reset
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
    message: 'Too many password reset attempts. Please try again later.'
  },
  
  // General API endpoints
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    message: 'Too many requests. Please slow down.'
  },
  
  // User creation
  userCreation: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10,
    message: 'Too many user creation attempts. Please try again later.'
  },
  
  // File uploads
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10,
    message: 'Too many upload attempts. Please try again later.'
  }
}

/**
 * Create a rate limiter middleware
 */
export function createRateLimit(config: {
  windowMs: number
  maxRequests: number
  message?: string
  keyGenerator?: (request: NextRequest) => string
}) {
  return async (request: NextRequest): Promise<RateLimitInfo | null> => {
    const key = config.keyGenerator ? config.keyGenerator(request) : getDefaultKey(request)
    const now = Date.now()
    const windowStart = now - config.windowMs

    // Clean up expired entries
    cleanupExpiredEntries(windowStart)

    // Get current count for this key
    const current = rateLimitStore.get(key)
    
    if (!current || current.resetTime <= now) {
      // First request in window or window has expired
      rateLimitStore.set(key, {
        count: 1,
        resetTime: now + config.windowMs
      })
      
      return {
        limit: config.maxRequests,
        remaining: config.maxRequests - 1,
        resetTime: new Date(now + config.windowMs),
        blocked: false
      }
    }

    // Increment count
    current.count++
    rateLimitStore.set(key, current)

    const remaining = Math.max(0, config.maxRequests - current.count)
    const blocked = current.count > config.maxRequests

    return {
      limit: config.maxRequests,
      remaining,
      resetTime: new Date(current.resetTime),
      blocked
    }
  }
}

/**
 * Rate limit middleware for login attempts
 */
export const loginRateLimit = createRateLimit({
  ...RATE_LIMITS.login,
  keyGenerator: (request) => {
    const ip = getClientIP(request)
    return `login:${ip}`
  }
})

/**
 * Rate limit middleware for API requests
 */
export const apiRateLimit = createRateLimit({
  ...RATE_LIMITS.api,
  keyGenerator: (request) => {
    const ip = getClientIP(request)
    return `api:${ip}`
  }
})

/**
 * Rate limit middleware for password reset
 */
export const passwordResetRateLimit = createRateLimit({
  ...RATE_LIMITS.passwordReset,
  keyGenerator: (request) => {
    const ip = getClientIP(request)
    return `password_reset:${ip}`
  }
})

/**
 * Rate limit middleware for user creation
 */
export const userCreationRateLimit = createRateLimit({
  ...RATE_LIMITS.userCreation,
  keyGenerator: (request) => {
    const ip = getClientIP(request)
    return `user_creation:${ip}`
  }
})

/**
 * Apply rate limiting to a request
 */
export async function applyRateLimit(
  request: NextRequest,
  rateLimiter: (request: NextRequest) => Promise<RateLimitInfo | null>
): Promise<NextResponse | null> {
  const rateLimitInfo = await rateLimiter(request)
  
  if (!rateLimitInfo) {
    return null
  }

  // Add rate limit headers
  const headers = new Headers()
  headers.set('X-RateLimit-Limit', rateLimitInfo.limit.toString())
  headers.set('X-RateLimit-Remaining', rateLimitInfo.remaining.toString())
  headers.set('X-RateLimit-Reset', rateLimitInfo.resetTime.toISOString())

  if (rateLimitInfo.blocked) {
    headers.set('Retry-After', Math.ceil((rateLimitInfo.resetTime.getTime() - Date.now()) / 1000).toString())
    
    return NextResponse.json(
      {
        success: false,
        error: 'Rate limit exceeded',
        retryAfter: rateLimitInfo.resetTime
      },
      {
        status: 429,
        headers
      }
    )
  }

  return null
}

/**
 * Get client IP address from request
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  return realIP || remoteAddr || 'unknown'
}

/**
 * Generate default rate limit key
 */
function getDefaultKey(request: NextRequest): string {
  const ip = getClientIP(request)
  const pathname = new URL(request.url).pathname
  return `${pathname}:${ip}`
}

/**
 * Clean up expired entries from rate limit store
 */
function cleanupExpiredEntries(windowStart: number): void {
  for (const [key, value] of rateLimitStore.entries()) {
    if (value.resetTime <= windowStart) {
      rateLimitStore.delete(key)
    }
  }
}

/**
 * Clear all rate limit data (useful for testing)
 */
export function clearRateLimitStore(): void {
  rateLimitStore.clear()
}

/**
 * Get current rate limit status for a key
 */
export function getRateLimitStatus(key: string): RateLimitInfo | null {
  const current = rateLimitStore.get(key)
  
  if (!current) {
    return null
  }

  const now = Date.now()
  if (current.resetTime <= now) {
    rateLimitStore.delete(key)
    return null
  }

  return {
    limit: 0, // This would need to be passed in or stored
    remaining: Math.max(0, current.count),
    resetTime: new Date(current.resetTime),
    blocked: false
  }
}

/**
 * Manually block an IP address
 */
export function blockIP(ip: string, durationMs: number = 60 * 60 * 1000): void {
  const key = `blocked:${ip}`
  rateLimitStore.set(key, {
    count: 999999, // High count to ensure blocking
    resetTime: Date.now() + durationMs
  })
}

/**
 * Check if an IP is blocked
 */
export function isIPBlocked(ip: string): boolean {
  const key = `blocked:${ip}`
  const blocked = rateLimitStore.get(key)
  
  if (!blocked) {
    return false
  }

  if (blocked.resetTime <= Date.now()) {
    rateLimitStore.delete(key)
    return false
  }

  return true
}

/**
 * Unblock an IP address
 */
export function unblockIP(ip: string): void {
  const key = `blocked:${ip}`
  rateLimitStore.delete(key)
}
