"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Student, AttendanceRecord } from "@/lib/types/scanner"
import { User, GraduationCap, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface StudentInfoCardProps {
  student: Student
  attendanceRecord?: AttendanceRecord
  className?: string
  showAttendanceHistory?: boolean
}

export function StudentInfoCard({ 
  student, 
  attendanceRecord, 
  className,
  showAttendanceHistory = false 
}: StudentInfoCardProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Present':
        return <CheckCircle className="h-4 w-4" />
      case 'Late':
        return <AlertCircle className="h-4 w-4" />
      case 'Absent':
        return <XCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Present':
        return 'bg-green-500'
      case 'Late':
        return 'bg-yellow-500'
      case 'Absent':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          <User className="h-5 w-5" />
          Student Information
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Student Photo and Basic Info */}
        <div className="flex items-center gap-4">
          <Avatar className="h-20 w-20 border-2 border-border">
            <AvatarImage 
              src={student.photo} 
              alt={student.name}
              className="object-cover"
            />
            <AvatarFallback className="text-lg font-semibold bg-primary/10">
              {getInitials(student.name)}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 space-y-1">
            <h3 className="text-xl font-bold leading-tight">{student.name}</h3>
            <p className="text-sm text-muted-foreground">{student.email}</p>
            <Badge 
              variant={student.status === 'Active' ? 'default' : 'secondary'}
              className="text-xs"
            >
              {student.status}
            </Badge>
          </div>
        </div>

        {/* Academic Information */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Student ID
            </p>
            <p className="text-sm font-mono font-semibold">{student.id}</p>
          </div>
          
          <div className="space-y-1">
            <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Year Level
            </p>
            <p className="text-sm font-semibold">{student.year}</p>
          </div>
          
          <div className="space-y-1 col-span-2">
            <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Course
            </p>
            <div className="flex items-center gap-2">
              <GraduationCap className="h-4 w-4 text-muted-foreground" />
              <p className="text-sm font-semibold">{student.course}</p>
            </div>
          </div>

          {student.section && (
            <div className="space-y-1">
              <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Section
              </p>
              <p className="text-sm font-semibold">{student.section}</p>
            </div>
          )}

          {student.grade && (
            <div className="space-y-1">
              <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Grade
              </p>
              <p className="text-sm font-semibold">{student.grade}</p>
            </div>
          )}
        </div>

        {/* Current Attendance Status */}
        {attendanceRecord && (
          <div className="border-t pt-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-semibold">Today's Attendance</h4>
              <div className="flex items-center gap-2">
                <div className={cn("w-2 h-2 rounded-full", getStatusColor(attendanceRecord.status))} />
                <span className="text-xs text-muted-foreground">
                  {new Date(attendanceRecord.timestamp).toLocaleTimeString()}
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              {attendanceRecord.checkIn && (
                <div className="space-y-1">
                  <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                    Check In
                  </p>
                  <p className="font-semibold">{attendanceRecord.checkIn}</p>
                </div>
              )}
              
              {attendanceRecord.checkOut && (
                <div className="space-y-1">
                  <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                    Check Out
                  </p>
                  <p className="font-semibold">{attendanceRecord.checkOut}</p>
                </div>
              )}
              
              <div className="space-y-1 col-span-2">
                <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                  Status
                </p>
                <div className="flex items-center gap-2">
                  {getStatusIcon(attendanceRecord.status)}
                  <Badge 
                    variant={
                      attendanceRecord.status === 'Present' ? 'default' :
                      attendanceRecord.status === 'Late' ? 'secondary' : 'destructive'
                    }
                  >
                    {attendanceRecord.status}
                  </Badge>
                </div>
              </div>

              {attendanceRecord.subject && (
                <div className="space-y-1 col-span-2">
                  <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                    Subject
                  </p>
                  <p className="text-sm font-semibold">{attendanceRecord.subject}</p>
                </div>
              )}

              {attendanceRecord.period && (
                <div className="space-y-1">
                  <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                    Period
                  </p>
                  <p className="text-sm font-semibold">{attendanceRecord.period}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* QR Code Info */}
        {student.qrCode && (
          <div className="border-t pt-4">
            <div className="space-y-1">
              <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                QR Code
              </p>
              <p className="text-xs font-mono bg-muted px-2 py-1 rounded">
                {student.qrCode}
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
