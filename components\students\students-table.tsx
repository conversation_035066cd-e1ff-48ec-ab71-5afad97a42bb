"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { 
  ChevronUp, 
  ChevronDown, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  QrCode, 
  Trash2,
  Phone,
  Mail,
  MapPin
} from "lucide-react"
import { Student, StudentSortConfig, getFullName } from "@/lib/types/student"
import { StudentProfileDialog } from "./student-profile-dialog"
import { StudentRegistrationDialog } from "./student-registration-dialog"
import { StudentStatusManager } from "./student-status-manager"
import { AttendanceStatisticsCompact } from "./attendance-statistics"
import { MobileStudentList } from "./mobile-student-card"
import { cn } from "@/lib/utils"

interface StudentsTableProps {
  students: Student[]
  selectedStudents: string[]
  onSelectionChange: (studentIds: string[]) => void
  sortConfig: StudentSortConfig
  onSortChange: (config: StudentSortConfig) => void
  onStudentUpdated?: (student: Student) => void
  onStudentDeleted?: (studentId: string) => void
  className?: string
}

export function StudentsTable({
  students,
  selectedStudents,
  onSelectionChange,
  sortConfig,
  onSortChange,
  onStudentUpdated,
  onStudentDeleted,
  className
}: StudentsTableProps) {
  const [showProfileDialog, setShowProfileDialog] = useState<Student | null>(null)
  const [showEditDialog, setShowEditDialog] = useState<Student | null>(null)

  const handleSort = (field: keyof Student | 'name') => {
    const direction = sortConfig.field === field && sortConfig.direction === 'asc' ? 'desc' : 'asc'
    onSortChange({ field, direction })
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(students.map(s => s.id))
    } else {
      onSelectionChange([])
    }
  }

  const handleSelectStudent = (studentId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedStudents, studentId])
    } else {
      onSelectionChange(selectedStudents.filter(id => id !== studentId))
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-500'
      case 'Inactive':
        return 'bg-yellow-500'
      case 'Transferred':
        return 'bg-blue-500'
      case 'Graduated':
        return 'bg-purple-500'
      default:
        return 'bg-gray-500'
    }
  }

  const SortButton = ({ field, children }: { field: keyof Student | 'name', children: React.ReactNode }) => {
    const isActive = sortConfig.field === field
    const direction = sortConfig.direction

    return (
      <Button
        variant="ghost"
        size="sm"
        className="h-auto p-0 font-medium hover:bg-transparent"
        onClick={() => handleSort(field)}
      >
        <span className="flex items-center gap-1">
          {children}
          {isActive ? (
            direction === 'asc' ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )
          ) : (
            <div className="h-4 w-4" />
          )}
        </span>
      </Button>
    )
  }

  const isAllSelected = students.length > 0 && selectedStudents.length === students.length
  const isIndeterminate = selectedStudents.length > 0 && selectedStudents.length < students.length

  return (
    <div className={cn("space-y-4", className)}>
      {/* Desktop Table View */}
      <div className="hidden lg:block">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={isAllSelected}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all students"
                    className={cn(isIndeterminate && "data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground opacity-50")}
                  />
                </TableHead>
                <TableHead className="w-16">Photo</TableHead>
                <TableHead>
                  <SortButton field="name">Name</SortButton>
                </TableHead>
                <TableHead>
                  <SortButton field="id">Student ID</SortButton>
                </TableHead>
                <TableHead>
                  <SortButton field="grade">Grade</SortButton>
                </TableHead>
                <TableHead>Section</TableHead>
                <TableHead>
                  <SortButton field="course">Course</SortButton>
                </TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>
                  <SortButton field="status">Status</SortButton>
                </TableHead>
                <TableHead>Attendance</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
            {students.length === 0 ? (
              <TableRow>
                <TableCell colSpan={11} className="text-center py-8 text-muted-foreground">
                  No students found matching your criteria
                </TableCell>
              </TableRow>
            ) : (
              students.map((student) => {
                const fullName = getFullName(student)
                const isSelected = selectedStudents.includes(student.id)
                
                return (
                  <TableRow key={student.id} className={cn(isSelected && "bg-muted/50")}>
                    <TableCell>
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => handleSelectStudent(student.id, checked as boolean)}
                        aria-label={`Select ${fullName}`}
                      />
                    </TableCell>
                    
                    <TableCell>
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={student.photo} alt={fullName} />
                        <AvatarFallback className="text-xs">
                          {getInitials(fullName)}
                        </AvatarFallback>
                      </Avatar>
                    </TableCell>
                    
                    <TableCell>
                      <div>
                        <div className="font-medium">{fullName}</div>
                        <div className="text-sm text-muted-foreground">{student.email}</div>
                      </div>
                    </TableCell>
                    
                    <TableCell className="font-mono text-sm">{student.id}</TableCell>
                    
                    <TableCell>
                      <Badge variant="outline">Grade {student.grade}</Badge>
                    </TableCell>
                    
                    <TableCell>
                      {student.section ? (
                        <Badge variant="secondary">{student.section}</Badge>
                      ) : (
                        <span className="text-muted-foreground text-sm">No section</span>
                      )}
                    </TableCell>
                    
                    <TableCell>
                      <div className="text-sm">
                        <div>{student.course}</div>
                        <div className="text-muted-foreground">{student.year}</div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Phone className="h-3 w-3" />
                          <span>{student.guardian.phone}</span>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Mail className="h-3 w-3" />
                          <span className="truncate max-w-[120px]">{student.guardian.email || 'No email'}</span>
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Badge 
                        variant={student.status === 'Active' ? 'default' : 'secondary'}
                        className={cn("text-xs", getStatusColor(student.status))}
                      >
                        {student.status}
                      </Badge>
                    </TableCell>
                    
                    <TableCell>
                      <AttendanceStatisticsCompact student={student} />
                    </TableCell>
                    
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <StudentProfileDialog
                          student={student}
                          onStudentUpdated={onStudentUpdated}
                          trigger={
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          }
                        />
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => setShowEditDialog(student)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Student
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <QrCode className="h-4 w-4 mr-2" />
                              Generate QR Code
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <StudentStatusManager
                              student={student}
                              onStatusChanged={(updatedStudent) => onStudentUpdated?.(updatedStudent)}
                              trigger={
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Manage Status
                                </DropdownMenuItem>
                              }
                            />
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-destructive"
                              onClick={() => onStudentDeleted?.(student.id)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete Student
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                )
              })
            )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden">
        <MobileStudentList
          students={students}
          selectedStudents={selectedStudents}
          onSelectionChange={onSelectionChange}
          onStudentUpdated={onStudentUpdated}
          onStudentDeleted={onStudentDeleted}
        />
      </div>

      {/* Edit Dialog */}
      {showEditDialog && (
        <StudentRegistrationDialog
          mode="edit"
          open={!!showEditDialog}
          onOpenChange={(open) => !open && setShowEditDialog(null)}
          initialData={{
            id: showEditDialog.id,
            firstName: showEditDialog.firstName,
            middleName: showEditDialog.middleName,
            lastName: showEditDialog.lastName,
            email: showEditDialog.email,
            dateOfBirth: showEditDialog.dateOfBirth,
            gender: showEditDialog.gender,
            course: showEditDialog.course,
            year: showEditDialog.year,
            section: showEditDialog.section,
            grade: showEditDialog.grade,
            guardianName: showEditDialog.guardian.name,
            guardianPhone: showEditDialog.guardian.phone,
            guardianEmail: showEditDialog.guardian.email,
            guardianRelationship: showEditDialog.guardian.relationship,
            guardianAddress: showEditDialog.guardian.address,
            emergencyContacts: showEditDialog.emergencyContacts,
            street: showEditDialog.address.street,
            barangay: showEditDialog.address.barangay,
            city: showEditDialog.address.city,
            province: showEditDialog.address.province,
            zipCode: showEditDialog.address.zipCode,
            country: showEditDialog.address.country,
            photo: showEditDialog.photo
          }}
          onStudentCreated={(updatedStudent) => {
            onStudentUpdated?.(updatedStudent)
            setShowEditDialog(null)
          }}
        />
      )}
    </div>
  )
}
