"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { 
  Edit, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  User,
  Calendar,
  MessageSquare
} from "lucide-react"
import { format } from "date-fns"

interface AttendanceRecord {
  id: string
  studentId: string
  studentName: string
  course: string
  grade: string
  section: string
  checkIn?: string
  checkOut?: string
  date: string
  status: "Present" | "Late" | "Absent"
  type: "gate" | "subject"
  subject?: string
  period?: string
  reason?: string
  photo?: string
}

interface AttendanceGridProps {
  records: AttendanceRecord[]
  onUpdateRecord: (recordId: string, updates: Partial<AttendanceRecord>) => void
  className?: string
}

export function AttendanceGrid({ records, onUpdateRecord, className }: AttendanceGridProps) {
  const [editingRecord, setEditingRecord] = useState<AttendanceRecord | null>(null)
  const [editForm, setEditForm] = useState({
    status: "",
    checkIn: "",
    checkOut: "",
    reason: ""
  })

  const handleEditRecord = (record: AttendanceRecord) => {
    setEditingRecord(record)
    setEditForm({
      status: record.status,
      checkIn: record.checkIn || "",
      checkOut: record.checkOut || "",
      reason: record.reason || ""
    })
  }

  const handleSaveEdit = () => {
    if (!editingRecord) return

    onUpdateRecord(editingRecord.id, {
      status: editForm.status as "Present" | "Late" | "Absent",
      checkIn: editForm.checkIn || undefined,
      checkOut: editForm.checkOut || undefined,
      reason: editForm.reason || undefined
    })

    setEditingRecord(null)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Present":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "Late":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "Absent":
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variant = status === "Present" ? "default" : 
                   status === "Late" ? "secondary" : "destructive"
    return <Badge variant={variant}>{status}</Badge>
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Attendance Records</span>
          <Badge variant="outline">{records.length} records</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Grade/Section</TableHead>
                <TableHead>Check In</TableHead>
                <TableHead>Check Out</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Subject/Period</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {records.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={record.photo} alt={record.studentName} />
                        <AvatarFallback>
                          <User className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{record.studentName}</div>
                        <div className="text-sm text-muted-foreground">{record.studentId}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">Grade {record.grade}</div>
                      <div className="text-sm text-muted-foreground">{record.section}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {record.checkIn ? (
                        <>
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>{record.checkIn}</span>
                        </>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {record.checkOut ? (
                        <>
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>{record.checkOut}</span>
                        </>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(record.status)}
                      {getStatusBadge(record.status)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {record.subject ? (
                      <div>
                        <div className="font-medium">{record.subject}</div>
                        <div className="text-sm text-muted-foreground">{record.period}</div>
                      </div>
                    ) : (
                      <Badge variant="outline">Gate Entry</Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleEditRecord(record)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Edit Attendance Record</DialogTitle>
                          <DialogDescription>
                            Update attendance information for {record.studentName}
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Status</Label>
                              <Select 
                                value={editForm.status} 
                                onValueChange={(value) => setEditForm({...editForm, status: value})}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Present">Present</SelectItem>
                                  <SelectItem value="Late">Late</SelectItem>
                                  <SelectItem value="Absent">Absent</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Check In Time</Label>
                              <Input
                                type="time"
                                value={editForm.checkIn}
                                onChange={(e) => setEditForm({...editForm, checkIn: e.target.value})}
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label>Check Out Time</Label>
                            <Input
                              type="time"
                              value={editForm.checkOut}
                              onChange={(e) => setEditForm({...editForm, checkOut: e.target.value})}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Reason/Notes</Label>
                            <Textarea
                              placeholder="Enter reason for absence or late arrival..."
                              value={editForm.reason}
                              onChange={(e) => setEditForm({...editForm, reason: e.target.value})}
                            />
                          </div>
                          <div className="flex justify-end space-x-2">
                            <Button variant="outline" onClick={() => setEditingRecord(null)}>
                              Cancel
                            </Button>
                            <Button onClick={handleSaveEdit}>
                              Save Changes
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
