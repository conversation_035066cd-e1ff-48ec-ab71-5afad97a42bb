"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { GeneratedReport, ReportStatus, ReportType, ExportFormat } from "@/lib/types/reports"
import { 
  Download, 
  Eye, 
  MoreHorizontal, 
  Search, 
  Filter,
  FileText,
  Calendar,
  Clock,
  Users,
  Trash2,
  Share,
  Archive,
  RefreshCw
} from "lucide-react"
import { format } from "date-fns"

interface ReportsLibraryProps {
  reports: GeneratedReport[]
  onDownload: (reportId: string, format: ExportFormat) => void
  onPreview: (reportId: string) => void
  onDelete: (reportId: string) => void
  onArchive: (reportId: string) => void
  onShare: (reportId: string) => void
  onRegenerate: (reportId: string) => void
  className?: string
}

export function ReportsLibrary({
  reports,
  onDownload,
  onPreview,
  onDelete,
  onArchive,
  onShare,
  onRegenerate,
  className
}: ReportsLibraryProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<ReportStatus | "ALL">("ALL")
  const [typeFilter, setTypeFilter] = useState<ReportType | "ALL">("ALL")
  const [sortBy, setSortBy] = useState<"date" | "name" | "type" | "downloads">("date")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")

  // Filter and sort reports
  const filteredReports = reports
    .filter(report => {
      const matchesSearch = report.config.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           report.config.description.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesStatus = statusFilter === "ALL" || report.status === statusFilter
      const matchesType = typeFilter === "ALL" || report.config.type === typeFilter
      return matchesSearch && matchesStatus && matchesType
    })
    .sort((a, b) => {
      let comparison = 0
      switch (sortBy) {
        case "date":
          comparison = new Date(a.generatedAt).getTime() - new Date(b.generatedAt).getTime()
          break
        case "name":
          comparison = a.config.name.localeCompare(b.config.name)
          break
        case "type":
          comparison = a.config.type.localeCompare(b.config.type)
          break
        case "downloads":
          comparison = a.downloadCount - b.downloadCount
          break
      }
      return sortOrder === "desc" ? -comparison : comparison
    })

  const getStatusBadge = (status: ReportStatus) => {
    const variants = {
      READY: "default",
      GENERATING: "secondary",
      DRAFT: "outline",
      FAILED: "destructive",
      ARCHIVED: "secondary"
    } as const

    const colors = {
      READY: "text-green-600",
      GENERATING: "text-blue-600",
      DRAFT: "text-gray-600",
      FAILED: "text-red-600",
      ARCHIVED: "text-gray-500"
    } as const

    return (
      <Badge variant={variants[status]} className={colors[status]}>
        {status === "GENERATING" && <RefreshCw className="h-3 w-3 mr-1 animate-spin" />}
        {status}
      </Badge>
    )
  }

  const getTypeBadge = (type: ReportType) => {
    const colors = {
      SF2: "bg-blue-100 text-blue-800",
      SF4: "bg-green-100 text-green-800",
      DAILY: "bg-purple-100 text-purple-800",
      WEEKLY: "bg-orange-100 text-orange-800",
      MONTHLY: "bg-pink-100 text-pink-800",
      ANNUAL: "bg-indigo-100 text-indigo-800",
      CUSTOM: "bg-gray-100 text-gray-800"
    } as const

    return (
      <Badge variant="secondary" className={colors[type]}>
        {type}
      </Badge>
    )
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Generated Reports
              <Badge variant="secondary">{filteredReports.length}</Badge>
            </CardTitle>
            <CardDescription>
              View, download, and manage your generated reports
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search reports..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex gap-2">
            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as ReportStatus | "ALL")}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Status</SelectItem>
                <SelectItem value="READY">Ready</SelectItem>
                <SelectItem value="GENERATING">Generating</SelectItem>
                <SelectItem value="DRAFT">Draft</SelectItem>
                <SelectItem value="FAILED">Failed</SelectItem>
                <SelectItem value="ARCHIVED">Archived</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as ReportType | "ALL")}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Types</SelectItem>
                <SelectItem value="SF2">SF2</SelectItem>
                <SelectItem value="SF4">SF4</SelectItem>
                <SelectItem value="DAILY">Daily</SelectItem>
                <SelectItem value="WEEKLY">Weekly</SelectItem>
                <SelectItem value="MONTHLY">Monthly</SelectItem>
                <SelectItem value="CUSTOM">Custom</SelectItem>
              </SelectContent>
            </Select>

            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [field, order] = value.split('-')
              setSortBy(field as typeof sortBy)
              setSortOrder(order as typeof sortOrder)
            }}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date-desc">Newest First</SelectItem>
                <SelectItem value="date-asc">Oldest First</SelectItem>
                <SelectItem value="name-asc">Name A-Z</SelectItem>
                <SelectItem value="name-desc">Name Z-A</SelectItem>
                <SelectItem value="downloads-desc">Most Downloaded</SelectItem>
                <SelectItem value="downloads-asc">Least Downloaded</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Reports Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Report</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Generated</TableHead>
                <TableHead>Size</TableHead>
                <TableHead>Downloads</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReports.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    No reports found matching your criteria
                  </TableCell>
                </TableRow>
              ) : (
                filteredReports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{report.config.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {report.config.description}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {format(new Date(report.config.dateRange.startDate), "MMM dd")} - {format(new Date(report.config.dateRange.endDate), "MMM dd, yyyy")}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getTypeBadge(report.config.type)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(report.status)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {format(new Date(report.generatedAt), "MMM dd, HH:mm")}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {report.fileSize ? formatFileSize(report.fileSize) : '-'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm">
                        <Download className="h-3 w-3" />
                        {report.downloadCount}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        {report.status === "READY" && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onPreview(report.id)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <Download className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => onDownload(report.id, "PDF")}>
                                  Download PDF
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => onDownload(report.id, "EXCEL")}>
                                  Download Excel
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => onDownload(report.id, "CSV")}>
                                  Download CSV
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </>
                        )}
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onShare(report.id)}>
                              <Share className="h-4 w-4 mr-2" />
                              Share
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onRegenerate(report.id)}>
                              <RefreshCw className="h-4 w-4 mr-2" />
                              Regenerate
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onArchive(report.id)}>
                              <Archive className="h-4 w-4 mr-2" />
                              Archive
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => onDelete(report.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
