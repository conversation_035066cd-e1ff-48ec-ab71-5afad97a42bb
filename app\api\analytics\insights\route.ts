import { NextRequest, NextResponse } from "next/server"
import { 
  mockAIInsights, 
  getAIInsightsByType 
} from "@/lib/data/analytics-mock-data"
import { AIInsightsGenerator, PatternDetectionEngine } from "@/lib/utils/ai-insights"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const severity = searchParams.get('severity')
    const limit = parseInt(searchParams.get('limit') || '10')
    const includeExpired = searchParams.get('includeExpired') === 'true'
    
    let insights = mockAIInsights
    
    // Filter by type
    if (type) {
      insights = getAIInsightsByType(type)
    }
    
    // Filter by severity
    if (severity) {
      insights = insights.filter(insight => insight.severity === severity)
    }
    
    // Filter out expired insights unless requested
    if (!includeExpired) {
      const now = new Date().toISOString()
      insights = insights.filter(insight => !insight.expiresAt || insight.expiresAt > now)
    }
    
    // Sort by creation date (newest first) and limit
    insights = insights
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit)
    
    // Calculate insight statistics
    const stats = {
      total: insights.length,
      byType: insights.reduce((acc, insight) => {
        acc[insight.type] = (acc[insight.type] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      bySeverity: insights.reduce((acc, insight) => {
        acc[insight.severity] = (acc[insight.severity] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      actionRequired: insights.filter(insight => insight.actionRequired).length,
      averageConfidence: insights.reduce((sum, insight) => sum + insight.confidence, 0) / insights.length
    }
    
    return NextResponse.json({
      success: true,
      data: {
        insights,
        stats,
        lastUpdated: new Date().toISOString()
      },
      message: "AI insights retrieved successfully"
    })
    
  } catch (error) {
    console.error("AI insights retrieval error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to retrieve AI insights"
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, insightId, feedback } = body
    
    if (action === 'generate') {
      // Generate new insights based on current data
      // In a real implementation, this would:
      // 1. Fetch latest attendance data
      // 2. Run AI analysis algorithms
      // 3. Generate new insights
      // 4. Save to database
      
      const newInsights = [
        {
          id: `INS_${Date.now()}_NEW`,
          type: 'pattern' as const,
          severity: 'info' as const,
          title: 'New Pattern Detected',
          description: 'AI has identified a new attendance pattern that requires attention',
          confidence: 0.82,
          actionRequired: false,
          suggestedActions: ['Monitor pattern development', 'Analyze contributing factors'],
          createdAt: new Date().toISOString()
        }
      ]
      
      return NextResponse.json({
        success: true,
        data: newInsights,
        message: "New insights generated successfully"
      })
    }
    
    if (action === 'feedback' && insightId && feedback) {
      // Handle user feedback on insights
      // This helps improve AI model accuracy
      
      return NextResponse.json({
        success: true,
        message: "Feedback recorded successfully"
      })
    }
    
    if (action === 'dismiss' && insightId) {
      // Mark insight as dismissed
      
      return NextResponse.json({
        success: true,
        message: "Insight dismissed successfully"
      })
    }
    
    return NextResponse.json({
      success: false,
      error: "Invalid action specified"
    }, { status: 400 })
    
  } catch (error) {
    console.error("AI insights action error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to process AI insights action"
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { insightId, status, notes } = body
    
    if (!insightId) {
      return NextResponse.json({
        success: false,
        error: "Insight ID is required"
      }, { status: 400 })
    }
    
    // Update insight status or add notes
    // In a real implementation, this would update the database
    
    return NextResponse.json({
      success: true,
      message: "Insight updated successfully"
    })
    
  } catch (error) {
    console.error("AI insights update error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to update insight"
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const insightId = searchParams.get('insightId')
    
    if (!insightId) {
      return NextResponse.json({
        success: false,
        error: "Insight ID is required"
      }, { status: 400 })
    }
    
    // Delete insight
    // In a real implementation, this would remove from database
    
    return NextResponse.json({
      success: true,
      message: "Insight deleted successfully"
    })
    
  } catch (error) {
    console.error("AI insights deletion error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to delete insight"
    }, { status: 500 })
  }
}
