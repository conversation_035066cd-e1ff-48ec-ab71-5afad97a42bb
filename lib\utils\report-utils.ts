import { 
  ReportConfig, 
  ReportFilters, 
  SF2Report, 
  SF4Report, 
  ReportMetadata,
  SF2StudentRecord,
  SF2Summary,
  SF4Enrollment,
  SF4Movement,
  SF4Attendance
} from '@/lib/types/reports'
import { Student, DetailedAttendanceRecord } from '@/lib/types/scanner'
import { schoolInfo, mockTeachers } from '@/lib/data/reports-mock-data'

// Date utility functions
export function formatDateForDepEd(date: string | Date): string {
  const d = new Date(date)
  return d.toLocaleDateString('en-PH', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function getSchoolYear(date: Date = new Date()): string {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  
  // School year starts in June (month 6)
  if (month >= 6) {
    return `${year}-${year + 1}`
  } else {
    return `${year - 1}-${year}`
  }
}

export function getQuarter(date: Date = new Date()): string {
  const month = date.getMonth() + 1
  
  if (month >= 6 && month <= 8) return "1st Quarter"
  if (month >= 9 && month <= 11) return "2nd Quarter"
  if (month >= 12 || month <= 2) return "3rd Quarter"
  return "4th Quarter"
}

export function getSemester(date: Date = new Date()): string {
  const month = date.getMonth() + 1
  
  if (month >= 6 && month <= 10) return "1st Semester"
  return "2nd Semester"
}

// Filter utility functions
export function applyReportFilters(
  students: Student[], 
  attendanceRecords: DetailedAttendanceRecord[], 
  filters: ReportFilters
): { students: Student[], records: DetailedAttendanceRecord[] } {
  let filteredStudents = [...students]
  let filteredRecords = [...attendanceRecords]

  // Filter by grades
  if (filters.grades && filters.grades.length > 0) {
    filteredStudents = filteredStudents.filter(student => 
      filters.grades!.includes(student.grade)
    )
  }

  // Filter by sections
  if (filters.sections && filters.sections.length > 0) {
    filteredStudents = filteredStudents.filter(student => 
      student.section && filters.sections!.includes(student.section)
    )
  }

  // Filter by courses
  if (filters.courses && filters.courses.length > 0) {
    filteredStudents = filteredStudents.filter(student => 
      filters.courses!.includes(student.course)
    )
  }

  // Filter by specific students
  if (filters.students && filters.students.length > 0) {
    filteredStudents = filteredStudents.filter(student => 
      filters.students!.includes(student.id)
    )
  }

  // Filter by attendance status
  if (filters.attendanceStatus && filters.attendanceStatus.length > 0) {
    filteredRecords = filteredRecords.filter(record => 
      filters.attendanceStatus!.includes(record.status)
    )
  }

  // Filter by student status
  if (!filters.includeTransferred) {
    filteredStudents = filteredStudents.filter(student => 
      student.status !== 'Transferred'
    )
  }

  if (!filters.includeInactive) {
    filteredStudents = filteredStudents.filter(student => 
      student.status === 'Active'
    )
  }

  // Filter records to match filtered students
  const studentIds = new Set(filteredStudents.map(s => s.id))
  filteredRecords = filteredRecords.filter(record => 
    studentIds.has(record.studentId)
  )

  return { students: filteredStudents, records: filteredRecords }
}

// Statistics calculation functions
export function calculateAttendanceStatistics(records: DetailedAttendanceRecord[]) {
  const total = records.length
  const present = records.filter(r => r.status === 'Present').length
  const late = records.filter(r => r.status === 'Late').length
  const absent = records.filter(r => r.status === 'Absent').length
  
  return {
    totalRecords: total,
    presentCount: present,
    lateCount: late,
    absentCount: absent,
    attendanceRate: total > 0 ? ((present + late) / total) * 100 : 0
  }
}

export function calculateStudentAttendanceRate(
  studentId: string, 
  records: DetailedAttendanceRecord[]
): number {
  const studentRecords = records.filter(r => r.studentId === studentId)
  const total = studentRecords.length
  const present = studentRecords.filter(r => r.status === 'Present' || r.status === 'Late').length
  
  return total > 0 ? (present / total) * 100 : 0
}

// SF2 Report generation functions
export function generateSF2Report(
  config: ReportConfig,
  students: Student[],
  attendanceRecords: DetailedAttendanceRecord[]
): SF2Report {
  const { students: filteredStudents, records: filteredRecords } = 
    applyReportFilters(students, attendanceRecords, config.filters)

  // Get the first teacher (in real implementation, this would be based on subject/section)
  const teacher = mockTeachers[0]

  // Create student records for SF2
  const sf2Students: SF2StudentRecord[] = filteredStudents.map(student => {
    const studentRecords = filteredRecords.filter(r => r.studentId === student.id)
    const dailyRecord = studentRecords.find(r => r.date === config.dateRange.startDate)
    
    return {
      studentId: student.id,
      studentName: `${student.firstName} ${student.middleName || ''} ${student.lastName}`.trim(),
      attendance: {
        'Morning': dailyRecord?.status === 'Present' ? 'P' : 
                  dailyRecord?.status === 'Late' ? 'L' : 
                  dailyRecord?.status === 'Absent' ? 'A' : 'A'
      },
      dailyStatus: dailyRecord?.status === 'Present' ? 'P' : 
                  dailyRecord?.status === 'Late' ? 'L' : 
                  dailyRecord?.status === 'Absent' ? 'A' : 'A',
      remarks: dailyRecord?.remarks
    }
  })

  // Calculate summary
  const summary: SF2Summary = {
    totalStudents: sf2Students.length,
    presentCount: sf2Students.filter(s => s.dailyStatus === 'P').length,
    lateCount: sf2Students.filter(s => s.dailyStatus === 'L').length,
    absentCount: sf2Students.filter(s => s.dailyStatus === 'A').length,
    excusedCount: sf2Students.filter(s => s.dailyStatus === 'E').length,
    attendanceRate: 0
  }
  
  summary.attendanceRate = summary.totalStudents > 0 ? 
    ((summary.presentCount + summary.lateCount) / summary.totalStudents) * 100 : 0

  return {
    id: `SF2_${Date.now()}`,
    date: config.dateRange.startDate,
    grade: config.filters.grades?.[0] || 'All',
    section: config.filters.sections?.[0] || 'All',
    teacher,
    students: sf2Students,
    summary,
    schoolInfo,
    generatedAt: new Date().toISOString()
  }
}

// SF4 Report generation functions
export function generateSF4Report(
  config: ReportConfig,
  students: Student[],
  attendanceRecords: DetailedAttendanceRecord[]
): SF4Report {
  const { students: filteredStudents, records: filteredRecords } = 
    applyReportFilters(students, attendanceRecords, config.filters)

  const startDate = new Date(config.dateRange.startDate)
  const endDate = new Date(config.dateRange.endDate)
  
  // Mock enrollment data (in real implementation, this would come from enrollment records)
  const enrollment: SF4Enrollment = {
    beginningOfMonth: filteredStudents.length,
    newAdmissions: [],
    transfers: {
      transferredIn: [],
      transferredOut: filteredStudents.filter(s => s.status === 'Transferred').map(s => ({
        studentId: s.id,
        name: `${s.firstName} ${s.lastName}`,
        dateOfAction: config.dateRange.endDate,
        reason: 'Transfer to another school'
      }))
    },
    dropouts: [],
    endOfMonth: filteredStudents.filter(s => s.status === 'Active').length
  }

  // Calculate movement statistics
  const movement: SF4Movement = {
    totalEnrolled: enrollment.beginningOfMonth,
    maleCount: filteredStudents.filter(s => s.gender === 'Male').length,
    femaleCount: filteredStudents.filter(s => s.gender === 'Female').length,
    newAdmissions: enrollment.newAdmissions.length,
    transfersIn: enrollment.transfers.transferredIn.length,
    transfersOut: enrollment.transfers.transferredOut.length,
    dropouts: enrollment.dropouts.length
  }

  // Calculate attendance statistics
  const stats = calculateAttendanceStatistics(filteredRecords)
  const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  
  const attendance: SF4Attendance = {
    totalSchoolDays: totalDays,
    averageAttendance: stats.presentCount + stats.lateCount,
    attendanceRate: stats.attendanceRate,
    chronicAbsentees: filteredStudents
      .filter(s => calculateStudentAttendanceRate(s.id, filteredRecords) < 80)
      .map(s => s.id),
    perfectAttendance: filteredStudents
      .filter(s => calculateStudentAttendanceRate(s.id, filteredRecords) === 100)
      .map(s => s.id)
  }

  return {
    id: `SF4_${Date.now()}`,
    month: startDate.toLocaleDateString('en-US', { month: 'long' }),
    year: startDate.getFullYear().toString(),
    grade: config.filters.grades?.[0] || 'All',
    section: config.filters.sections?.[0],
    enrollment,
    movement,
    attendance,
    schoolInfo,
    principalReview: {
      reviewedBy: schoolInfo.principalName,
      reviewDate: new Date().toISOString().split('T')[0],
      approved: false
    },
    generatedAt: new Date().toISOString()
  }
}

// Report metadata generation
export function generateReportMetadata(
  config: ReportConfig,
  students: Student[],
  attendanceRecords: DetailedAttendanceRecord[]
): ReportMetadata {
  const { students: filteredStudents, records: filteredRecords } = 
    applyReportFilters(students, attendanceRecords, config.filters)

  const stats = calculateAttendanceStatistics(filteredRecords)

  return {
    totalStudents: filteredStudents.length,
    totalRecords: filteredRecords.length,
    dateRange: config.dateRange,
    statistics: {
      presentCount: stats.presentCount,
      lateCount: stats.lateCount,
      absentCount: stats.absentCount,
      attendanceRate: stats.attendanceRate
    },
    schoolInfo
  }
}
