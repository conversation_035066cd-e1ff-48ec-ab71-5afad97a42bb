import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { prisma } from '@/lib/prisma'
import { hashPassword, generateTemporaryPassword, validatePassword } from '@/lib/auth/password'
import { logAuditEntry, hasPermission, getClientIP, getUserAgent } from '@/lib/auth/security'
import { UserRole } from '@/lib/generated/prisma'
import { ApiResponse, CreateUserRequest, PaginatedResponse, AuthUser } from '@/lib/auth/types'

// GET /api/auth/users - Get all users (admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'users.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const role = searchParams.get('role') as UserRole | null
    const isActive = searchParams.get('isActive')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { username: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (role) {
      where.role = role
    }

    if (isActive !== null) {
      where.isActive = isActive === 'true'
    }

    // Get users with pagination
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          username: true,
          email: true,
          firstName: true,
          lastName: true,
          middleName: true,
          role: true,
          permissions: true,
          isActive: true,
          lastLoginAt: true,
          mustChangePassword: true,
          twoFactorEnabled: true,
          phoneNumber: true,
          department: true,
          position: true,
          failedLoginAttempts: true,
          lockedUntil: true,
          createdAt: true,
          updatedAt: true
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ])

    // Transform users data
    const transformedUsers: AuthUser[] = users.map(user => {
      let permissions: string[] = []
      if (user.permissions) {
        try {
          permissions = JSON.parse(user.permissions)
        } catch (error) {
          console.error('Failed to parse user permissions:', error)
        }
      }

      return {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        middleName: user.middleName,
        role: user.role,
        permissions,
        isActive: user.isActive,
        lastLoginAt: user.lastLoginAt,
        mustChangePassword: user.mustChangePassword,
        twoFactorEnabled: user.twoFactorEnabled,
        phoneNumber: user.phoneNumber,
        department: user.department,
        position: user.position
      }
    })

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: transformedUsers,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    } as PaginatedResponse<AuthUser>, { status: 200 })

  } catch (error) {
    console.error('Get users error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// POST /api/auth/users - Create new user (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'users.create')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const body = await request.json()
    const userData: CreateUserRequest = body

    // Validate required fields
    if (!userData.username || !userData.email || !userData.firstName || !userData.lastName || !userData.role) {
      return NextResponse.json({
        success: false,
        error: 'Username, email, first name, last name, and role are required'
      } as ApiResponse, { status: 400 })
    }

    // Check if username already exists
    const existingUsername = await prisma.user.findUnique({
      where: { username: userData.username }
    })

    if (existingUsername) {
      return NextResponse.json({
        success: false,
        error: 'Username already exists'
      } as ApiResponse, { status: 400 })
    }

    // Check if email already exists
    const existingEmail = await prisma.user.findUnique({
      where: { email: userData.email }
    })

    if (existingEmail) {
      return NextResponse.json({
        success: false,
        error: 'Email already exists'
      } as ApiResponse, { status: 400 })
    }

    // Generate temporary password if not provided
    const temporaryPassword = userData.temporaryPassword || generateTemporaryPassword()

    // Validate password
    const passwordValidation = validatePassword(temporaryPassword, {
      username: userData.username,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName
    })

    if (!passwordValidation.isValid) {
      return NextResponse.json({
        success: false,
        error: 'Generated password does not meet security requirements',
        data: passwordValidation.errors
      } as ApiResponse, { status: 400 })
    }

    // Hash password
    const passwordHash = await hashPassword(temporaryPassword)

    // Create user
    const newUser = await prisma.user.create({
      data: {
        username: userData.username,
        email: userData.email,
        passwordHash,
        firstName: userData.firstName,
        lastName: userData.lastName,
        middleName: userData.middleName,
        role: userData.role,
        permissions: userData.permissions ? JSON.stringify(userData.permissions) : null,
        phoneNumber: userData.phoneNumber,
        department: userData.department,
        position: userData.position,
        mustChangePassword: userData.mustChangePassword ?? true,
        createdBy: session.user.id
      }
    })

    // Log audit entry
    await logAuditEntry({
      userId: session.user.id,
      action: 'CREATE_USER',
      entityType: 'User',
      entityId: newUser.id,
      newValues: {
        username: userData.username,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role
      },
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request)
    })

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      data: {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email,
        temporaryPassword: temporaryPassword // Return for admin to share with user
      }
    } as ApiResponse, { status: 201 })

  } catch (error) {
    console.error('Create user error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
