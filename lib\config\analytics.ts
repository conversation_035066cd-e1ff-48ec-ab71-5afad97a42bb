// Analytics configuration and constants for QRSAMS

import { AnalyticsKPI, DashboardWidget, AlertConfiguration } from "@/lib/types/analytics"

// Risk Assessment Thresholds
export const RISK_THRESHOLDS = {
  ATTENDANCE_RATE: {
    LOW: 0.95,      // Above 95% - Low risk
    MEDIUM: 0.85,   // 85-95% - Medium risk  
    HIGH: 0.75,     // 75-85% - High risk
    CRITICAL: 0.65  // Below 65% - Critical risk
  },
  PUNCTUALITY_RATE: {
    LOW: 0.90,
    MEDIUM: 0.80,
    HIGH: 0.70,
    CRITICAL: 0.60
  },
  CONSECUTIVE_ABSENCES: {
    MEDIUM: 3,
    HIGH: 5,
    CRITICAL: 7
  }
} as const

// AI Model Configuration
export const AI_MODEL_CONFIG = {
  DROPOUT_PREDICTION: {
    FEATURES: [
      'attendance_rate',
      'punctuality_rate', 
      'grade_level',
      'consecutive_absences',
      'late_frequency',
      'parent_engagement',
      'academic_performance',
      'behavioral_incidents'
    ],
    CONFIDENCE_THRESHOLD: 0.7,
    RET<PERSON>IN_INTERVAL: 30 // days
  },
  PATTERN_DETECTION: {
    ANOMALY_THRESHOLD: 2, // standard deviations
    MIN_DATA_POINTS: 14,  // minimum days of data
    SEASONALITY_WINDOW: 30 // days to check for patterns
  },
  RISK_ASSESSMENT: {
    WEIGHTS: {
      ATTENDANCE: 0.4,
      PUNCTUALITY: 0.25,
      TREND: 0.2,
      ENGAGEMENT: 0.15
    }
  }
} as const

// Dashboard Widget Configurations
export const DEFAULT_WIDGETS: DashboardWidget[] = [
  {
    id: 'attendance-kpi',
    type: 'kpi',
    title: 'Overall Attendance Rate',
    size: 'small',
    position: { x: 0, y: 0 },
    config: {
      metric: 'attendance_rate',
      format: 'percentage',
      showTrend: true
    },
    refreshInterval: 300 // 5 minutes
  },
  {
    id: 'risk-distribution',
    type: 'chart',
    title: 'Student Risk Distribution',
    size: 'medium',
    position: { x: 1, y: 0 },
    config: {
      chartType: 'pie',
      dataSource: 'risk_levels'
    },
    refreshInterval: 600 // 10 minutes
  },
  {
    id: 'attendance-heatmap',
    type: 'chart',
    title: 'Attendance Heatmap',
    size: 'large',
    position: { x: 0, y: 1 },
    config: {
      chartType: 'heatmap',
      timeRange: '30d'
    },
    refreshInterval: 900 // 15 minutes
  },
  {
    id: 'ai-insights',
    type: 'insight',
    title: 'AI Insights',
    size: 'medium',
    position: { x: 2, y: 0 },
    config: {
      maxInsights: 5,
      severityFilter: ['warning', 'critical']
    },
    refreshInterval: 1800 // 30 minutes
  }
]

// Default KPIs for Analytics Dashboard
export const DEFAULT_KPIS: AnalyticsKPI[] = [
  {
    id: 'overall-attendance',
    name: 'Overall Attendance Rate',
    value: 92.3,
    unit: '%',
    trend: {
      direction: 'up',
      percentage: 2.5,
      period: 'vs last month'
    },
    target: 95,
    status: 'warning',
    description: 'School-wide attendance rate across all grades'
  },
  {
    id: 'punctuality-rate',
    name: 'Punctuality Rate',
    value: 87.1,
    unit: '%',
    trend: {
      direction: 'down',
      percentage: 1.2,
      period: 'vs last month'
    },
    target: 90,
    status: 'warning',
    description: 'Percentage of students arriving on time'
  },
  {
    id: 'at-risk-students',
    name: 'At-Risk Students',
    value: 23,
    unit: 'students',
    trend: {
      direction: 'up',
      percentage: 15.0,
      period: 'vs last month'
    },
    target: 15,
    status: 'critical',
    description: 'Students identified as high or critical risk'
  },
  {
    id: 'intervention-success',
    name: 'Intervention Success Rate',
    value: 78.5,
    unit: '%',
    trend: {
      direction: 'up',
      percentage: 5.2,
      period: 'vs last quarter'
    },
    target: 80,
    status: 'good',
    description: 'Percentage of successful interventions'
  }
]

// Alert Configurations
export const DEFAULT_ALERTS: AlertConfiguration[] = [
  {
    id: 'attendance-drop',
    name: 'Significant Attendance Drop',
    type: 'attendance_drop',
    conditions: {
      threshold: 10,
      operator: 'greater_than',
      period: '7d'
    },
    recipients: ['<EMAIL>', '<EMAIL>'],
    channels: ['email', 'dashboard'],
    active: true
  },
  {
    id: 'high-risk-student',
    name: 'New High-Risk Student Identified',
    type: 'risk_increase',
    conditions: {
      threshold: 75,
      operator: 'greater_than',
      period: '1d'
    },
    recipients: ['<EMAIL>'],
    channels: ['email', 'sms', 'dashboard'],
    active: true
  },
  {
    id: 'pattern-anomaly',
    name: 'Unusual Attendance Pattern',
    type: 'anomaly_detected',
    conditions: {
      threshold: 2,
      operator: 'greater_than',
      period: '3d'
    },
    recipients: ['<EMAIL>'],
    channels: ['dashboard'],
    active: true
  }
]

// Chart Color Schemes
export const CHART_COLORS = {
  RISK_LEVELS: {
    low: '#10B981',      // Green
    medium: '#F59E0B',   // Amber
    high: '#EF4444',     // Red
    critical: '#7C2D12'  // Dark Red
  },
  ATTENDANCE_STATUS: {
    present: '#10B981',  // Green
    late: '#F59E0B',     // Amber
    absent: '#EF4444',   // Red
    excused: '#6B7280'   // Gray
  },
  TRENDS: {
    increasing: '#10B981',
    stable: '#6B7280',
    decreasing: '#EF4444',
    volatile: '#8B5CF6'
  },
  GRADES: [
    '#3B82F6', // Blue
    '#10B981', // Green
    '#F59E0B', // Amber
    '#EF4444', // Red
    '#8B5CF6', // Purple
    '#EC4899'  // Pink
  ]
} as const

// Export Configurations
export const EXPORT_CONFIGS = {
  PDF: {
    format: 'A4',
    orientation: 'portrait',
    margins: { top: 20, right: 20, bottom: 20, left: 20 },
    includeCharts: true,
    includeHeader: true,
    includeFooter: true
  },
  EXCEL: {
    sheetNames: ['Summary', 'Detailed Data', 'Charts', 'Insights'],
    includeFormulas: true,
    includeCharts: true,
    autoFilter: true
  },
  CSV: {
    delimiter: ',',
    includeHeaders: true,
    dateFormat: 'YYYY-MM-DD',
    encoding: 'utf-8'
  }
} as const

// Refresh Intervals (in seconds)
export const REFRESH_INTERVALS = {
  REAL_TIME: 30,      // 30 seconds
  FREQUENT: 300,      // 5 minutes
  NORMAL: 900,        // 15 minutes
  SLOW: 1800,         // 30 minutes
  HOURLY: 3600,       // 1 hour
  DAILY: 86400        // 24 hours
} as const

// Data Retention Policies
export const DATA_RETENTION = {
  RAW_ATTENDANCE: 365,     // days
  AGGREGATED_DAILY: 1095,  // 3 years
  ANALYTICS_INSIGHTS: 180, // 6 months
  RISK_ASSESSMENTS: 730,   // 2 years
  REPORTS: 1095           // 3 years
} as const

// Performance Thresholds
export const PERFORMANCE_THRESHOLDS = {
  QUERY_TIMEOUT: 30000,    // 30 seconds
  CHART_RENDER_TIME: 5000, // 5 seconds
  EXPORT_TIMEOUT: 120000,  // 2 minutes
  MAX_DATA_POINTS: 10000,  // Maximum points per chart
  BATCH_SIZE: 1000         // Records per batch processing
} as const

// Feature Flags for Analytics
export const FEATURE_FLAGS = {
  ENABLE_AI_INSIGHTS: true,
  ENABLE_PREDICTIVE_ANALYTICS: true,
  ENABLE_REAL_TIME_UPDATES: true,
  ENABLE_ADVANCED_EXPORTS: true,
  ENABLE_CUSTOM_DASHBOARDS: true,
  ENABLE_AUTOMATED_ALERTS: true,
  ENABLE_PARENT_PORTAL_ANALYTICS: false // Future feature
} as const

// API Endpoints for Analytics
export const ANALYTICS_ENDPOINTS = {
  DASHBOARD_DATA: '/api/analytics/dashboard',
  RISK_ASSESSMENT: '/api/analytics/risk-assessment',
  ATTENDANCE_PATTERNS: '/api/analytics/patterns',
  AI_INSIGHTS: '/api/analytics/insights',
  EXPORT_REPORT: '/api/analytics/export',
  ALERT_CONFIG: '/api/analytics/alerts',
  PREDICTIVE_MODEL: '/api/analytics/predictions'
} as const
