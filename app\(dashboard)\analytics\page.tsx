"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  Calendar,
  Users,
  Brain,
  AlertTriangle,
  Download,
  RefreshCw,
  Filter,
  Search,
  Settings
} from "lucide-react"

// Import our new analytics components
import { AttendanceHeatmap } from "@/components/analytics/attendance-heatmap"
import { PredictiveCharts } from "@/components/analytics/predictive-charts"
import { GradeCompar<PERSON><PERSON><PERSON> } from "@/components/analytics/grade-comparison-chart"
import { RiskDashboard } from "@/components/analytics/risk-dashboard"
import { AIInsightsPanel } from "@/components/analytics/ai-insights-panel"
import { ExportDialog } from "@/components/analytics/export-dialog"

// Import loading and error states
import {
  KPICardsLoading,
  ChartLoading,
  HeatmapLoading,
  AIInsightsLoading
} from "@/components/analytics/loading-states"
import {
  ErrorState,
  NetworkErrorState,
  DataLoadingError
} from "@/components/analytics/error-states"

// Import mock data
import {
  mockAnalyticsKPIs,
  mockAIInsights,
  mockHeatmapData,
  mockTrendAnalysis,
  mockGradeComparisons,
  mockRiskAssessments
} from "@/lib/data/analytics-mock-data"

export default function AnalyticsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedTimeRange, setSelectedTimeRange] = useState("30d")
  const [selectedGrades, setSelectedGrades] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [dashboardData, setDashboardData] = useState({
    kpis: mockAnalyticsKPIs,
    insights: mockAIInsights,
    heatmapData: mockHeatmapData,
    trendAnalysis: mockTrendAnalysis,
    gradeComparisons: mockGradeComparisons,
    riskAssessments: mockRiskAssessments
  })

  // Simulate data fetching
  const fetchDashboardData = async () => {
    setIsLoading(true)
    setError(null)
    try {
      // In a real implementation, this would call the API
      const response = await fetch(`/api/analytics/dashboard?timeRange=${selectedTimeRange}&grades=${selectedGrades.join(',')}`)
      if (response.ok) {
        const data = await response.json()
        setDashboardData(data.data)
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
      setError(error instanceof Error ? error.message : 'Failed to load analytics data')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [selectedTimeRange, selectedGrades])

  const handleExport = (format: string) => {
    console.log(`Exporting analytics in ${format} format`)
    // In a real implementation, this would trigger the export
  }

  const handleRefresh = () => {
    fetchDashboardData()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Brain className="h-8 w-8" />
            Advanced Analytics
          </h1>
          <p className="text-muted-foreground">
            AI-powered insights and comprehensive attendance analysis
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={() => setShowExportDialog(true)}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters & Controls
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">Time Range:</label>
              <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">Grades:</label>
              <Select>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="All grades" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Grades</SelectItem>
                  <SelectItem value="7">Grade 7</SelectItem>
                  <SelectItem value="8">Grade 8</SelectItem>
                  <SelectItem value="9">Grade 9</SelectItem>
                  <SelectItem value="10">Grade 10</SelectItem>
                  <SelectItem value="11">Grade 11</SelectItem>
                  <SelectItem value="12">Grade 12</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search students, classes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-[200px]"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error State */}
      {error && (
        <DataLoadingError
          dataType="analytics data"
          onRetry={fetchDashboardData}
          errorCode="FETCH_ERROR"
        />
      )}

      {/* Key Metrics */}
      {isLoading ? (
        <KPICardsLoading />
      ) : !error ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {dashboardData.kpis.map((kpi) => (
            <Card key={kpi.id}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{kpi.name}</CardTitle>
                {kpi.trend.direction === 'up' ? (
                  <TrendingUp className="h-4 w-4 text-green-600" />
                ) : kpi.trend.direction === 'down' ? (
                  <TrendingDown className="h-4 w-4 text-red-600" />
                ) : (
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                )}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {kpi.value}{kpi.unit}
                </div>
                <p className={`text-xs ${
                  kpi.trend.direction === 'up' ? 'text-green-600' :
                  kpi.trend.direction === 'down' ? 'text-red-600' :
                  'text-muted-foreground'
                }`}>
                  {kpi.trend.direction === 'up' ? '+' : kpi.trend.direction === 'down' ? '' : ''}
                  {kpi.trend.percentage}% {kpi.trend.period}
                </p>
                {kpi.target && (
                  <div className="mt-2">
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Target: {kpi.target}{kpi.unit}</span>
                      <Badge variant={kpi.status === 'good' ? 'default' : kpi.status === 'warning' ? 'secondary' : 'destructive'}>
                        {kpi.status}
                      </Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      ) : null}

      {/* AI Insights Alert */}
      {dashboardData.insights.filter(insight => insight.severity === 'critical' || insight.severity === 'warning').length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-800">
              <AlertTriangle className="h-5 w-5" />
              AI Insights Alert
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {dashboardData.insights
                .filter(insight => insight.severity === 'critical' || insight.severity === 'warning')
                .slice(0, 3)
                .map((insight) => (
                  <div key={insight.id} className="flex items-start gap-2">
                    <Badge variant={insight.severity === 'critical' ? 'destructive' : 'secondary'}>
                      {insight.severity}
                    </Badge>
                    <div>
                      <p className="text-sm font-medium text-orange-800">{insight.title}</p>
                      <p className="text-xs text-orange-700">{insight.description}</p>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="heatmap">Heatmap</TabsTrigger>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
          <TabsTrigger value="grades">Grade Analysis</TabsTrigger>
          <TabsTrigger value="risk">Risk Assessment</TabsTrigger>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Weekly Attendance Pattern</CardTitle>
                <CardDescription>Daily attendance breakdown for current week</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { day: "Monday", rate: 94, trend: "up", present: 423, total: 450 },
                    { day: "Tuesday", rate: 91, trend: "down", present: 410, total: 450 },
                    { day: "Wednesday", rate: 89, trend: "down", present: 401, total: 450 },
                    { day: "Thursday", rate: 93, trend: "up", present: 419, total: 450 },
                    { day: "Friday", rate: 88, trend: "down", present: 396, total: 450 },
                  ].map((day) => (
                    <div key={day.day} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-sm font-medium w-20">{day.day}</span>
                        <div className="w-32 bg-secondary rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full"
                            style={{ width: `${day.rate}%` }}
                          />
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{day.rate}%</span>
                        <span className="text-xs text-muted-foreground">({day.present}/{day.total})</span>
                        {day.trend === "up" ? (
                          <TrendingUp className="h-3 w-3 text-green-600" />
                        ) : (
                          <TrendingDown className="h-3 w-3 text-red-600" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Performing Courses</CardTitle>
                <CardDescription>Courses with highest attendance rates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { course: "Information Technology", rate: 95.2, students: 156, change: +2.1 },
                    { course: "Computer Science", rate: 92.8, students: 89, change: +1.5 },
                    { course: "Business Administration", rate: 89.5, students: 134, change: -0.8 },
                    { course: "Engineering", rate: 91.3, students: 71, change: +3.2 },
                  ].map((course) => (
                    <div key={course.course} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{course.course}</span>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{course.rate}%</Badge>
                          <Badge variant={course.change > 0 ? "default" : "secondary"} className="text-xs">
                            {course.change > 0 ? '+' : ''}{course.change}%
                          </Badge>
                        </div>
                      </div>
                      <div className="w-full bg-secondary rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full"
                          style={{ width: `${course.rate}%` }}
                        />
                      </div>
                      <p className="text-xs text-muted-foreground">{course.students} students enrolled</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="heatmap">
          {isLoading ? (
            <HeatmapLoading />
          ) : (
            <AttendanceHeatmap
              data={dashboardData.heatmapData}
              title="Attendance Heatmap Analysis"
              description="Visual representation of attendance patterns by day and time"
            />
          )}
        </TabsContent>

        <TabsContent value="predictions">
          {isLoading ? (
            <ChartLoading title="Predictive Analytics" height={400} />
          ) : (
            <PredictiveCharts
              trendData={dashboardData.trendAnalysis}
            />
          )}
        </TabsContent>

        <TabsContent value="grades">
          {isLoading ? (
            <ChartLoading title="Grade Comparison" height={400} />
          ) : (
            <GradeComparisonChart
              gradeData={dashboardData.gradeComparisons}
            />
          )}
        </TabsContent>

        <TabsContent value="risk">
          {isLoading ? (
            <ChartLoading title="Risk Assessment Dashboard" height={600} />
          ) : (
            <RiskDashboard
              assessments={dashboardData.riskAssessments}
              onRefresh={handleRefresh}
              onExport={handleExport}
            />
          )}
        </TabsContent>

        <TabsContent value="insights">
          {isLoading ? (
            <AIInsightsLoading />
          ) : (
            <AIInsightsPanel
              insights={dashboardData.insights}
              onInsightAction={(insightId, action) => {
                console.log(`Action ${action} on insight ${insightId}`)
              }}
              onGenerateInsights={() => {
                console.log('Generating new insights...')
              }}
              onFeedback={(insightId, feedback, comment) => {
                console.log(`Feedback ${feedback} on insight ${insightId}:`, comment)
              }}
            />
          )}
        </TabsContent>
      </Tabs>

      {/* Export Dialog */}
      <ExportDialog
        open={showExportDialog}
        onOpenChange={setShowExportDialog}
        onExport={(config) => {
          console.log('Exporting with config:', config)
          handleExport(config.format)
        }}
        filters={{
          dateRange: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            end: new Date().toISOString().split('T')[0]
          },
          grades: selectedGrades,
          sections: [],
          courses: [],
          subjects: [],
          riskLevels: []
        }}
      />
    </div>
  )
}
