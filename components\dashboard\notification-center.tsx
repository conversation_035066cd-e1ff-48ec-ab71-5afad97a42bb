"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Bell, 
  BellRing, 
  X, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  User,
  MessageSquare,
  Wifi,
  WifiOff
} from "lucide-react"
import { cn } from "@/lib/utils"
import { format } from "date-fns"

interface Notification {
  id: string
  type: "success" | "warning" | "error" | "info"
  title: string
  message: string
  timestamp: Date
  read: boolean
  action?: {
    label: string
    onClick: () => void
  }
}

interface NotificationCenterProps {
  className?: string
}

// Static initial notifications to prevent hydration issues
const getInitialNotifications = (): Notification[] => [
  {
    id: "1",
    type: "warning",
    title: "High Absence Rate",
    message: "Grade 8-A has 15% absence rate today",
    timestamp: new Date("2024-01-01T10:00:00Z"),
    read: false
  },
  {
    id: "2",
    type: "success",
    title: "Attendance Goal Reached",
    message: "Overall attendance rate reached 90% target",
    timestamp: new Date("2024-01-01T09:45:00Z"),
    read: false
  },
  {
    id: "3",
    type: "info",
    title: "New Student Registered",
    message: "Maria Santos has been added to Grade 7-A",
    timestamp: new Date("2024-01-01T09:30:00Z"),
    read: true
  },
  {
    id: "4",
    type: "error",
    title: "Scanner Offline",
    message: "Gate scanner #2 is not responding",
    timestamp: new Date("2024-01-01T09:15:00Z"),
    read: false
  }
]

export function NotificationCenter({ className }: NotificationCenterProps) {
  const [notifications, setNotifications] = useState<Notification[]>(getInitialNotifications())

  const [isOpen, setIsOpen] = useState(false)

  const unreadCount = notifications.filter(n => !n.read).length

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true }
          : notification
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    )
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case "info":
        return <MessageSquare className="h-4 w-4 text-blue-500" />
      default:
        return <Bell className="h-4 w-4 text-gray-500" />
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case "success":
        return "border-l-green-500"
      case "warning":
        return "border-l-yellow-500"
      case "error":
        return "border-l-red-500"
      case "info":
        return "border-l-blue-500"
      default:
        return "border-l-gray-500"
    }
  }

  // Simulate real-time notifications
  useEffect(() => {
    // Initialize with current timestamps on client side only
    setNotifications(prev => prev.map(notification => ({
      ...notification,
      timestamp: new Date(Date.now() - (parseInt(notification.id) * 15 * 60 * 1000))
    })))

    const interval = setInterval(() => {
      // Randomly add new notifications
      if (Math.random() < 0.1) { // 10% chance every 5 seconds
        const newNotification: Notification = {
          id: Date.now().toString(),
          type: ["success", "warning", "info"][Math.floor(Math.random() * 3)] as any,
          title: "New Activity",
          message: "Student check-in detected",
          timestamp: new Date(),
          read: false
        }
        setNotifications(prev => [newNotification, ...prev.slice(0, 9)]) // Keep only 10 notifications
      }
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className={cn("relative", className)}>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="relative"
      >
        {unreadCount > 0 ? (
          <BellRing className="h-5 w-5" />
        ) : (
          <Bell className="h-5 w-5" />
        )}
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs"
          >
            {unreadCount > 9 ? "9+" : unreadCount}
          </Badge>
        )}
      </Button>

      {isOpen && (
        <Card className="absolute right-0 top-full mt-2 w-80 z-50 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg">Notifications</CardTitle>
            <div className="flex items-center space-x-2">
              {unreadCount > 0 && (
                <Button variant="ghost" size="sm" onClick={markAllAsRead}>
                  Mark all read
                </Button>
              )}
              <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className="h-96">
              {notifications.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  No notifications
                </div>
              ) : (
                <div className="space-y-1">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={cn(
                        "p-3 border-l-4 hover:bg-muted/50 cursor-pointer",
                        getNotificationColor(notification.type),
                        !notification.read && "bg-muted/30"
                      )}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-2 flex-1">
                          {getNotificationIcon(notification.type)}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium truncate">
                                {notification.title}
                              </p>
                              {!notification.read && (
                                <div className="w-2 h-2 bg-blue-500 rounded-full ml-2" />
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground mt-1">
                              {notification.message}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {format(notification.timestamp, "MMM d, HH:mm")}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            removeNotification(notification.id)
                          }}
                          className="h-6 w-6 p-0 ml-2"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      {notification.action && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={(e) => {
                            e.stopPropagation()
                            notification.action!.onClick()
                          }}
                        >
                          {notification.action.label}
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// System Health Indicator Component
export function SystemHealthIndicator() {
  const [systemStatus, setSystemStatus] = useState({
    scanner: "online",
    database: "online",
    sms: "pending",
    network: "online"
  })

  useEffect(() => {
    // Simulate system status updates
    const interval = setInterval(() => {
      setSystemStatus(prev => ({
        ...prev,
        network: Math.random() > 0.1 ? "online" : "offline"
      }))
    }, 10000)

    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "online":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "offline":
        return <WifiOff className="h-4 w-4 text-red-500" />
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variant = status === "online" ? "default" : 
                   status === "pending" ? "secondary" : "destructive"
    return <Badge variant={variant} className="text-xs">{status}</Badge>
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center">
          <Wifi className="mr-2 h-5 w-5" />
          System Health
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon(systemStatus.scanner)}
            <span className="text-sm">Scanner</span>
          </div>
          {getStatusBadge(systemStatus.scanner)}
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon(systemStatus.database)}
            <span className="text-sm">Database</span>
          </div>
          {getStatusBadge(systemStatus.database)}
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon(systemStatus.sms)}
            <span className="text-sm">SMS Gateway</span>
          </div>
          {getStatusBadge(systemStatus.sms)}
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon(systemStatus.network)}
            <span className="text-sm">Network</span>
          </div>
          {getStatusBadge(systemStatus.network)}
        </div>
      </CardContent>
    </Card>
  )
}
