"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { AttendanceStats, ProgressRing, RealTimeCounter } from "@/components/dashboard/attendance-stats"
import { WeatherWidget, CompactWeatherWidget } from "@/components/dashboard/weather-widget"
import {
  WeeklyTrendChart,
  GradeBreakdownChart,
  AttendanceRatePieChart,
  MonthlyTrendChart,
  AttendanceHeatMap
} from "@/components/dashboard/attendance-charts"
import { mockDashboardStats, mockRecentActivity } from "@/lib/data/mock-data"
import {
  QrCode,
  Bell,
  Settings,
  RefreshCw,
  Download,
  Printer,
  Activity,
  AlertTriangle
} from "lucide-react"
import { format } from "date-fns"

export default function DashboardPage() {
  const [currentTime, setCurrentTime] = useState<Date | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Initialize time on client side only to prevent hydration mismatch
  useEffect(() => {
    setCurrentTime(new Date())
  }, [])

  // Update time every second
  useEffect(() => {
    if (currentTime === null) return

    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [currentTime])

  const handleRefresh = async () => {
    setIsRefreshing(true)
    // Simulate data refresh
    await new Promise(resolve => setTimeout(resolve, 1000))
    setIsRefreshing(false)
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome to QRSAMS - Real-time Student Attendance Monitoring
          </p>
        </div>
        <div className="flex items-center space-x-4">
          {/* Current Time Display */}
          <div className="text-right">
            <div className="text-2xl font-bold tabular-nums">
              {currentTime ? format(currentTime, "HH:mm:ss") : "--:--:--"}
            </div>
            <div className="text-sm text-muted-foreground">
              {currentTime ? format(currentTime, "EEEE, MMMM d, yyyy") : "Loading..."}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isRefreshing}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button size="sm">
              <QrCode className="mr-2 h-4 w-4" />
              Open Scanner
            </Button>
          </div>
        </div>
      </div>

      {/* Main Statistics */}
      <AttendanceStats
        totalStudents={mockDashboardStats.totalStudents}
        presentToday={mockDashboardStats.presentToday}
        lateToday={mockDashboardStats.lateToday}
        absentToday={mockDashboardStats.absentToday}
        attendanceRate={mockDashboardStats.attendanceRate}
      />

      {/* Main Content Grid */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Left Column - Charts and Analytics */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
              <TabsTrigger value="grades">Grades</TabsTrigger>
              <TabsTrigger value="patterns">Patterns</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <AttendanceRatePieChart
                  present={mockDashboardStats.presentToday}
                  late={mockDashboardStats.lateToday}
                  absent={mockDashboardStats.absentToday}
                />
                <Card>
                  <CardHeader>
                    <CardTitle>Real-time Counters</CardTitle>
                    <CardDescription>Live attendance tracking</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <RealTimeCounter
                        value={mockDashboardStats.presentToday}
                        label="Present"
                        trend="up"
                      />
                      <RealTimeCounter
                        value={mockDashboardStats.lateToday}
                        label="Late"
                        trend="down"
                      />
                    </div>
                    <div className="flex justify-center">
                      <ProgressRing
                        value={mockDashboardStats.presentToday + mockDashboardStats.lateToday}
                        max={mockDashboardStats.totalStudents}
                        size={120}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="trends">
              <WeeklyTrendChart data={mockDashboardStats.weeklyTrend} />
            </TabsContent>

            <TabsContent value="grades">
              <GradeBreakdownChart data={mockDashboardStats.gradeBreakdown} />
            </TabsContent>

            <TabsContent value="patterns">
              <AttendanceHeatMap data={[
                { day: "Mon", hour: 7, count: 45 },
                { day: "Mon", hour: 8, count: 120 },
                { day: "Tue", hour: 7, count: 38 },
                { day: "Tue", hour: 8, count: 95 },
                { day: "Wed", hour: 7, count: 52 },
                { day: "Wed", hour: 8, count: 110 },
                { day: "Thu", hour: 7, count: 41 },
                { day: "Thu", hour: 8, count: 88 },
                { day: "Fri", hour: 7, count: 35 },
                { day: "Fri", hour: 8, count: 75 }
              ]} />
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column - Sidebar Content */}
        <div className="space-y-6">
          {/* Weather Widget */}
          <WeatherWidget />

          {/* Recent Activity Feed */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-lg">Recent Activity</CardTitle>
                <CardDescription>Latest scans and alerts</CardDescription>
              </div>
              <Button variant="ghost" size="sm">
                <Bell className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {mockRecentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${
                    activity.status === 'success' ? 'bg-green-500' :
                    activity.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {activity.studentName}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {activity.action} • {activity.time}
                    </p>
                  </div>
                  <Badge
                    variant={
                      activity.status === 'success' ? 'default' :
                      activity.status === 'warning' ? 'secondary' : 'destructive'
                    }
                    className="text-xs"
                  >
                    {activity.type === 'scan' ? <Activity className="h-3 w-3" /> :
                     <AlertTriangle className="h-3 w-3" />}
                  </Badge>
                </div>
              ))}
              <Button variant="outline" size="sm" className="w-full">
                View All Activity
              </Button>
            </CardContent>
          </Card>

          {/* System Health */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">System Health</CardTitle>
              <CardDescription>Real-time system status</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Scanner Status</span>
                <Badge variant="default">Online</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Database</span>
                <Badge variant="default">Connected</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">SMS Gateway</span>
                <Badge variant="secondary">Pending</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Last Sync</span>
                <span className="text-xs text-muted-foreground">2 min ago</span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions Panel */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full justify-start" variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Export Today&apos;s Data
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Printer className="mr-2 h-4 w-4" />
                Print Daily Report
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                System Settings
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
