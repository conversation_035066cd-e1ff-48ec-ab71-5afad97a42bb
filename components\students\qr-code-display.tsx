"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  QrCode,
  Download,
  Printer,
  RefreshCw,
  Copy,
  CheckCircle,
  AlertTriangle
} from "lucide-react"
import { Student, getFullName } from "@/lib/types/student"
import { qrGenerator, qrManager } from "@/lib/utils/qr-code"
import { toast } from "sonner"
import { cn } from "@/lib/utils"

interface QRCodeDisplayProps {
  student: Student
  onQRGenerated?: (qrId: string) => void
  onQRRegenerated?: (qrId: string) => void
  className?: string
}

export function QRCodeDisplay({
  student,
  onQRGenerated,
  onQRRegenerated,
  className
}: QRCodeDisplayProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [qrData, setQrData] = useState<string | null>(null)
  const [qrId, setQrId] = useState<string | null>(student.qrCode || null)

  useEffect(() => {
    if (student.qrCode) {
      // Generate QR data string for display
      const qrString = qrGenerator.generateQRString(student)
      setQrData(qrString)
    }
  }, [student])

  const handleGenerate = async () => {
    setIsGenerating(true)
    try {
      const newQrId = await qrManager.generateForStudent(student)
      const qrString = qrGenerator.generateQRString(student)
      
      setQrId(newQrId)
      setQrData(qrString)
      
      if (onQRGenerated) {
        onQRGenerated(newQrId)
      }
      
      toast.success("QR code generated successfully!")
    } catch (error) {
      toast.error("Failed to generate QR code")
    } finally {
      setIsGenerating(false)
    }
  }

  const handleRegenerate = async () => {
    setIsGenerating(true)
    try {
      const newQrId = await qrManager.regenerateForStudent(student)
      const qrString = qrGenerator.generateQRString(student)
      
      setQrId(newQrId)
      setQrData(qrString)
      
      if (onQRRegenerated) {
        onQRRegenerated(newQrId)
      }
      
      toast.success("QR code regenerated successfully!")
    } catch (error) {
      toast.error("Failed to regenerate QR code")
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCopyData = async () => {
    if (qrData) {
      try {
        await navigator.clipboard.writeText(qrData)
        toast.success("QR code data copied to clipboard")
      } catch (error) {
        toast.error("Failed to copy QR code data")
      }
    }
  }

  const handleDownload = () => {
    if (!qrId) return
    
    // In a real implementation, this would download the actual QR code image
    // For now, we'll simulate the download
    toast.success("QR code download started")
    console.log("Downloading QR code:", qrId)
  }

  const handlePrint = () => {
    if (!qrId) return
    
    // Create a print window with the QR code
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      const fullName = getFullName(student)
      
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>QR Code - ${fullName}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 100vh;
              margin: 0;
              background: white;
            }
            .qr-container {
              text-align: center;
              padding: 20px;
              border: 2px solid #ddd;
              border-radius: 8px;
            }
            .qr-placeholder {
              width: 200px;
              height: 200px;
              border: 2px dashed #ccc;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 20px auto;
              font-size: 14px;
              color: #666;
            }
            .student-info {
              margin-top: 15px;
            }
            .student-name {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 5px;
            }
            .student-details {
              font-size: 14px;
              color: #666;
              line-height: 1.4;
            }
            @media print {
              body { margin: 0; }
              .qr-container { border: none; }
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <div class="qr-placeholder">
              QR Code<br/>
              ${qrId}
            </div>
            <div class="student-info">
              <div class="student-name">${fullName}</div>
              <div class="student-details">
                Student ID: ${student.id}<br/>
                Grade ${student.grade}${student.section ? ` - Section ${student.section}` : ''}<br/>
                ${student.course} - ${student.year}<br/>
                Tanauan School of Arts and Trade
              </div>
            </div>
          </div>
        </body>
        </html>
      `)
      
      printWindow.document.close()
      printWindow.print()
    }
  }

  const fullName = getFullName(student)

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <QrCode className="h-5 w-5" />
          Student QR Code
        </CardTitle>
        <CardDescription>
          QR code for {fullName} ({student.id})
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* QR Code Display */}
        <div className="flex flex-col items-center space-y-4">
          <div className="w-48 h-48 bg-white border-2 border-gray-200 rounded-lg flex items-center justify-center">
            {qrId ? (
              <div className="text-center">
                <QrCode className="h-24 w-24 mx-auto mb-2 text-muted-foreground" />
                <p className="text-xs text-muted-foreground">QR Code Preview</p>
                <p className="text-xs font-mono break-all px-2">{qrId}</p>
              </div>
            ) : (
              <div className="text-center text-muted-foreground">
                <QrCode className="h-12 w-12 mx-auto mb-2" />
                <p className="text-sm">No QR Code Generated</p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2 justify-center">
            {!qrId ? (
              <Button 
                onClick={handleGenerate} 
                disabled={isGenerating}
                className="min-w-[120px]"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <QrCode className="h-4 w-4 mr-2" />
                    Generate QR
                  </>
                )}
              </Button>
            ) : (
              <>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleRegenerate}
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4 mr-2" />
                  )}
                  Regenerate
                </Button>
                
                <Button variant="outline" size="sm" onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                
                <Button variant="outline" size="sm" onClick={handlePrint}>
                  <Printer className="h-4 w-4 mr-2" />
                  Print
                </Button>
                
                <Button variant="outline" size="sm" onClick={handleCopyData}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Data
                </Button>
              </>
            )}
          </div>
        </div>

        {/* QR Code Information */}
        {qrId && (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <span className="text-muted-foreground">QR Code ID:</span>
              <span className="font-mono text-xs break-all">{qrId}</span>
              
              <span className="text-muted-foreground">Student ID:</span>
              <span className="font-mono">{student.id}</span>
              
              <span className="text-muted-foreground">Student Name:</span>
              <span>{fullName}</span>
              
              <span className="text-muted-foreground">Grade & Section:</span>
              <span>Grade {student.grade}{student.section ? ` - ${student.section}` : ''}</span>
              
              <span className="text-muted-foreground">Course:</span>
              <span>{student.course}</span>
              
              <span className="text-muted-foreground">Status:</span>
              <Badge variant={student.status === 'Active' ? 'default' : 'secondary'}>
                {student.status}
              </Badge>
            </div>
          </div>
        )}

        {/* Usage Instructions */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Usage Instructions:</strong>
            <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
              <li>Use this QR code for quick attendance marking</li>
              <li>Scan with the QRSAMS mobile app or scanner</li>
              <li>Print and laminate for durability</li>
              <li>Keep the QR code secure and report if lost</li>
              <li>QR codes are valid for one academic year</li>
            </ul>
          </AlertDescription>
        </Alert>

        {/* Validation Status */}
        {qrData && (
          <div className="flex items-center gap-2 text-sm">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span className="text-green-600">QR code is valid and ready to use</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
