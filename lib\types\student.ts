// Student Management Types - Updated for Prisma compatibility
// This file maintains backward compatibility while integrating with Prisma

// Re-export Prisma types for new code
export * from './prisma'

// Legacy types for backward compatibility
export interface Guardian {
  name: string
  phone: string
  email?: string
  relationship: 'Father' | 'Mother' | 'Guardian' | 'Grandparent' | 'Sibling' | 'Other'
  address?: string
}

export interface EmergencyContact {
  name: string
  phone: string
  relationship: string
  address?: string
}

export interface Address {
  street: string
  barangay: string
  city: string
  province: string
  zipCode: string
  country?: string
}

export interface AttendanceStats {
  totalDays: number
  presentDays: number
  lateDays: number
  absentDays: number
  attendanceRate: number
  lastAttendance?: string
}

// Legacy Student interface - use StudentWithComputed from prisma.ts for new code
export interface Student {
  id: string // DepEd ID format (e.g., 123456789012)
  firstName: string
  middleName?: string
  lastName: string
  email: string
  course: string
  year: string
  section?: string
  grade: '7' | '8' | '9' | '10' | '11' | '12'
  status: 'Active' | 'Inactive' | 'Transferred' | 'Graduated'
  photo?: string
  qrCode?: string
  dateOfBirth?: string
  gender?: 'Male' | 'Female'
  guardian: Guardian
  emergencyContacts: EmergencyContact[]
  address: Address
  enrollmentDate: string
  lastUpdated: string
  attendanceStats?: AttendanceStats
}

// Computed properties helper
export const getFullName = (student: Student): string => {
  const parts = [student.firstName, student.middleName, student.lastName].filter(Boolean)
  return parts.join(' ')
}

// Form types for student registration/editing
export interface StudentFormData {
  // Basic Information
  id: string
  firstName: string
  middleName?: string
  lastName: string
  email: string
  dateOfBirth?: string
  gender?: 'Male' | 'Female'
  
  // Academic Information
  course: string
  year: string
  section?: string
  grade: '7' | '8' | '9' | '10' | '11' | '12'
  
  // Guardian Information
  guardianName: string
  guardianPhone: string
  guardianEmail?: string
  guardianRelationship: 'Father' | 'Mother' | 'Guardian' | 'Grandparent' | 'Sibling' | 'Other'
  guardianAddress?: string
  
  // Emergency Contacts
  emergencyContacts: EmergencyContact[]
  
  // Address Information
  street: string
  barangay: string
  city: string
  province: string
  zipCode: string
  country?: string
  
  // Photo
  photo?: File | string
}

// Filter and search types
export interface StudentFilters {
  search?: string
  grade?: string[]
  section?: string[]
  status?: string[]
  course?: string[]
  year?: string[]
}

export interface StudentSortConfig {
  field: keyof Student | 'name'
  direction: 'asc' | 'desc'
}

// Bulk operations
export interface BulkOperation {
  type: 'export' | 'generateQR' | 'updateStatus' | 'delete'
  studentIds: string[]
  options?: Record<string, any>
}

export interface CSVImportResult {
  success: boolean
  imported: number
  failed: number
  errors: Array<{
    row: number
    field: string
    message: string
  }>
}

// QR Code types
export interface QRCodeData {
  studentId: string
  name: string
  grade: string
  section?: string
  validUntil?: string
}

export interface QRCodeBatch {
  id: string
  createdAt: string
  studentIds: string[]
  format: 'individual' | 'sheet'
  status: 'generating' | 'ready' | 'failed'
}

// Pagination
export interface PaginationConfig {
  page: number
  pageSize: number
  total: number
}

// API Response types
export interface StudentListResponse {
  students: Student[]
  pagination: PaginationConfig
  filters: StudentFilters
}

export interface StudentResponse {
  student: Student
  success: boolean
  message?: string
}

// Legacy validation schemas (for backward compatibility)
export const GRADE_LEVELS = ['7', '8', '9', '10', '11', '12'] as const
export const STUDENT_STATUSES = ['Active', 'Inactive', 'Transferred', 'Graduated'] as const
export const GUARDIAN_RELATIONSHIPS = ['Father', 'Mother', 'Guardian', 'Grandparent', 'Sibling', 'Other'] as const
export const GENDERS = ['Male', 'Female'] as const

// Updated constants that match Prisma enums
export const PRISMA_GRADE_LEVELS = ['GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12'] as const
export const PRISMA_STUDENT_STATUSES = ['ACTIVE', 'INACTIVE', 'TRANSFERRED', 'GRADUATED', 'DROPPED'] as const
export const PRISMA_GUARDIAN_RELATIONSHIPS = ['FATHER', 'MOTHER', 'GUARDIAN', 'GRANDPARENT', 'SIBLING', 'AUNT', 'UNCLE', 'OTHER'] as const
export const PRISMA_GENDERS = ['MALE', 'FEMALE'] as const

// Default values
export const DEFAULT_STUDENT: Partial<Student> = {
  status: 'Active',
  emergencyContacts: [],
  country: 'Philippines'
}

// Utility functions for converting between legacy and Prisma formats
export const convertLegacyGradeToPrisma = (grade: string): string => {
  return `GRADE_${grade}`
}

export const convertPrismaGradeToLegacy = (grade: string): string => {
  return grade.replace('GRADE_', '')
}

export const convertLegacyStatusToPrisma = (status: string): string => {
  return status.toUpperCase()
}

export const convertPrismaStatusToLegacy = (status: string): string => {
  return status.charAt(0) + status.slice(1).toLowerCase()
}

export const DEFAULT_PAGINATION: PaginationConfig = {
  page: 1,
  pageSize: 20,
  total: 0
}
