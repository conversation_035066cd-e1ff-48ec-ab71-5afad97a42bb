import { NextRequest, NextResponse } from "next/server"
import { findStudentById, findStudentByQRCode, getTodayAttendanceRecord } from "@/lib/data/mock-data"
import { StudentLookupResponse } from "@/lib/types/scanner"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const qrCode = searchParams.get('qrCode')

    if (!studentId && !qrCode) {
      return NextResponse.json({
        success: false,
        error: "Either studentId or qrCode parameter is required"
      } as StudentLookupResponse, { status: 400 })
    }

    let student
    if (qrCode) {
      student = findStudentByQRCode(qrCode)
    } else if (studentId) {
      student = findStudentById(studentId)
    }

    if (!student) {
      return NextResponse.json({
        success: false,
        error: "Student not found"
      } as StudentLookupResponse, { status: 404 })
    }

    // Get today's attendance record for the student
    const attendanceRecord = getTodayAttendanceRecord(student.id)

    return NextResponse.json({
      success: true,
      data: student,
      message: "Student found successfully"
    } as StudentLookupResponse)

  } catch (error) {
    console.error("Student lookup error:", error)
    return NextResponse.json({
      success: false,
      error: "Internal server error"
    } as StudentLookupResponse, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { qrCode, studentId } = body

    if (!qrCode && !studentId) {
      return NextResponse.json({
        success: false,
        error: "Either qrCode or studentId is required"
      } as StudentLookupResponse, { status: 400 })
    }

    let student
    if (qrCode) {
      student = findStudentByQRCode(qrCode)
    } else if (studentId) {
      student = findStudentById(studentId)
    }

    if (!student) {
      return NextResponse.json({
        success: false,
        error: "Student not found"
      } as StudentLookupResponse, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: student,
      message: "Student found successfully"
    } as StudentLookupResponse)

  } catch (error) {
    console.error("Student lookup error:", error)
    return NextResponse.json({
      success: false,
      error: "Internal server error"
    } as StudentLookupResponse, { status: 500 })
  }
}
