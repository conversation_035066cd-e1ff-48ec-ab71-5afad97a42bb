"use client"

import { OfflineQueueI<PERSON>, <PERSON>ync<PERSON>tatus, <PERSON><PERSON><PERSON><PERSON>, ScanR<PERSON>ult } from "@/lib/types/scanner"

export class OfflineManager {
  private static instance: OfflineManager
  private syncQueue: OfflineQueueItem[] = []
  private isOnline: boolean = true
  private syncInProgress: boolean = false
  private listeners: ((status: SyncStatus) => void)[] = []
  private syncInterval: NodeJS.Timeout | null = null

  private constructor() {
    this.initializeOnlineDetection()
    this.loadQueueFromStorage()
    this.startPeriodicSync()
  }

  static getInstance(): OfflineManager {
    if (!OfflineManager.instance) {
      OfflineManager.instance = new OfflineManager()
    }
    return OfflineManager.instance
  }

  // Initialize online/offline detection
  private initializeOnlineDetection() {
    if (typeof window !== 'undefined') {
      this.isOnline = navigator.onLine
      
      window.addEventListener('online', () => {
        this.isOnline = true
        this.notifyListeners()
        this.syncQueue()
      })
      
      window.addEventListener('offline', () => {
        this.isOnline = false
        this.notifyListeners()
      })
    }
  }

  // Load queue from localStorage
  private loadQueueFromStorage() {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem('qrsams_offline_queue')
        if (stored) {
          const parsed = JSON.parse(stored)
          this.syncQueue = parsed.map((item: any) => ({
            ...item,
            timestamp: new Date(item.timestamp),
            lastRetry: item.lastRetry ? new Date(item.lastRetry) : undefined
          }))
        }
      } catch (error) {
        console.error('Failed to load offline queue:', error)
        this.syncQueue = []
      }
    }
  }

  // Save queue to localStorage
  private saveQueueToStorage() {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('qrsams_offline_queue', JSON.stringify(this.syncQueue))
      } catch (error) {
        console.error('Failed to save offline queue:', error)
      }
    }
  }

  // Add item to sync queue
  addToQueue(type: 'attendance' | 'scan', data: AttendanceRecord | ScanResult) {
    const item: OfflineQueueItem = {
      id: this.generateId(),
      type,
      data,
      timestamp: new Date(),
      retryCount: 0
    }

    this.syncQueue.push(item)
    this.saveQueueToStorage()
    this.notifyListeners()

    // Try to sync immediately if online
    if (this.isOnline) {
      this.syncQueue()
    }
  }

  // Sync queue with server
  private async syncQueue() {
    if (this.syncInProgress || !this.isOnline || this.syncQueue.length === 0) {
      return
    }

    this.syncInProgress = true
    this.notifyListeners()

    const itemsToSync = [...this.syncQueue]
    const successfulItems: string[] = []

    for (const item of itemsToSync) {
      try {
        const success = await this.syncItem(item)
        if (success) {
          successfulItems.push(item.id)
        } else {
          // Increment retry count
          item.retryCount++
          item.lastRetry = new Date()
          
          // Remove items that have failed too many times
          if (item.retryCount >= 5) {
            console.warn(`Removing item ${item.id} after 5 failed attempts`)
            successfulItems.push(item.id) // Remove it from queue
          }
        }
      } catch (error) {
        console.error(`Failed to sync item ${item.id}:`, error)
        item.retryCount++
        item.lastRetry = new Date()
      }
    }

    // Remove successfully synced items
    this.syncQueue = this.syncQueue.filter(item => !successfulItems.includes(item.id))
    this.saveQueueToStorage()

    this.syncInProgress = false
    this.notifyListeners()
  }

  // Sync individual item
  private async syncItem(item: OfflineQueueItem): Promise<boolean> {
    try {
      const endpoint = item.type === 'attendance' ? '/api/attendance' : '/api/scans'
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...item.data,
          offline: true,
          originalTimestamp: item.timestamp
        })
      })

      return response.ok
    } catch (error) {
      console.error('Sync request failed:', error)
      return false
    }
  }

  // Start periodic sync
  private startPeriodicSync(intervalMinutes: number = 5) {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
    }

    this.syncInterval = setInterval(() => {
      if (this.isOnline) {
        this.syncQueue()
      }
    }, intervalMinutes * 60 * 1000)
  }

  // Update sync interval
  updateSyncInterval(intervalMinutes: number) {
    this.startPeriodicSync(intervalMinutes)
  }

  // Get current sync status
  getSyncStatus(): SyncStatus {
    return {
      isOnline: this.isOnline,
      lastSync: this.getLastSyncTime(),
      pendingItems: this.syncQueue.length,
      isSyncing: this.syncInProgress,
      syncError: this.getLastSyncError()
    }
  }

  // Get last sync time
  private getLastSyncTime(): Date | undefined {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('qrsams_last_sync')
      return stored ? new Date(stored) : undefined
    }
    return undefined
  }

  // Set last sync time
  private setLastSyncTime() {
    if (typeof window !== 'undefined') {
      localStorage.setItem('qrsams_last_sync', new Date().toISOString())
    }
  }

  // Get last sync error
  private getLastSyncError(): string | undefined {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('qrsams_sync_error') || undefined
    }
    return undefined
  }

  // Set sync error
  private setSyncError(error: string) {
    if (typeof window !== 'undefined') {
      localStorage.setItem('qrsams_sync_error', error)
    }
  }

  // Clear sync error
  private clearSyncError() {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('qrsams_sync_error')
    }
  }

  // Add status listener
  addStatusListener(listener: (status: SyncStatus) => void) {
    this.listeners.push(listener)
    // Immediately notify with current status
    listener(this.getSyncStatus())
  }

  // Remove status listener
  removeStatusListener(listener: (status: SyncStatus) => void) {
    this.listeners = this.listeners.filter(l => l !== listener)
  }

  // Notify all listeners
  private notifyListeners() {
    const status = this.getSyncStatus()
    this.listeners.forEach(listener => listener(status))
  }

  // Generate unique ID
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  // Force sync
  async forceSync(): Promise<boolean> {
    if (!this.isOnline) {
      throw new Error('Cannot sync while offline')
    }

    await this.syncQueue()
    return this.syncQueue.length === 0
  }

  // Clear queue (for testing/reset)
  clearQueue() {
    this.syncQueue = []
    this.saveQueueToStorage()
    this.notifyListeners()
  }

  // Get queue items (for debugging)
  getQueueItems(): OfflineQueueItem[] {
    return [...this.syncQueue]
  }

  // Cleanup
  destroy() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
    }
    this.listeners = []
  }
}
