"use client"

import * as React from "react"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export interface DateRange {
  from: Date | undefined
  to: Date | undefined
}

interface DateRangePickerProps {
  value?: DateRange
  onChange?: (range: DateRange) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function DateRangePicker({
  value,
  onChange,
  placeholder = "Select date range",
  className,
  disabled = false,
}: DateRangePickerProps) {
  const [range, setRange] = React.useState<DateRange>(
    value || { from: undefined, to: undefined }
  )

  React.useEffect(() => {
    if (value) {
      setRange(value)
    }
  }, [value])

  const handleFromChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const date = event.target.value ? new Date(event.target.value) : undefined
    const newRange = { ...range, from: date }
    setRange(newRange)
    onChange?.(newRange)
  }

  const handleToChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const date = event.target.value ? new Date(event.target.value) : undefined
    const newRange = { ...range, to: date }
    setRange(newRange)
    onChange?.(newRange)
  }

  const formatDate = (date: Date | undefined) => {
    return date ? format(date, "yyyy-MM-dd") : ""
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <div className="flex items-center space-x-2">
        <div className="grid gap-1">
          <Label htmlFor="from-date" className="text-xs">From</Label>
          <Input
            id="from-date"
            type="date"
            value={formatDate(range.from)}
            onChange={handleFromChange}
            disabled={disabled}
            className="w-full"
          />
        </div>
        <div className="grid gap-1">
          <Label htmlFor="to-date" className="text-xs">To</Label>
          <Input
            id="to-date"
            type="date"
            value={formatDate(range.to)}
            onChange={handleToChange}
            disabled={disabled}
            className="w-full"
          />
        </div>
      </div>
      {range.from && range.to && (
        <div className="flex items-center text-sm text-muted-foreground">
          <CalendarIcon className="mr-2 h-4 w-4" />
          {format(range.from, "MMM dd, yyyy")} - {format(range.to, "MMM dd, yyyy")}
        </div>
      )}
    </div>
  )
}

// Quick date range presets
export const dateRangePresets = [
  {
    label: "Today",
    value: () => {
      const today = new Date()
      return { from: today, to: today }
    }
  },
  {
    label: "Yesterday",
    value: () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      return { from: yesterday, to: yesterday }
    }
  },
  {
    label: "This Week",
    value: () => {
      const today = new Date()
      const firstDay = new Date(today.setDate(today.getDate() - today.getDay()))
      const lastDay = new Date(today.setDate(today.getDate() - today.getDay() + 6))
      return { from: firstDay, to: lastDay }
    }
  },
  {
    label: "Last Week",
    value: () => {
      const today = new Date()
      const firstDay = new Date(today.setDate(today.getDate() - today.getDay() - 7))
      const lastDay = new Date(today.setDate(today.getDate() - today.getDay() - 1))
      return { from: firstDay, to: lastDay }
    }
  },
  {
    label: "This Month",
    value: () => {
      const today = new Date()
      const firstDay = new Date(today.getFullYear(), today.getMonth(), 1)
      const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0)
      return { from: firstDay, to: lastDay }
    }
  },
  {
    label: "Last Month",
    value: () => {
      const today = new Date()
      const firstDay = new Date(today.getFullYear(), today.getMonth() - 1, 1)
      const lastDay = new Date(today.getFullYear(), today.getMonth(), 0)
      return { from: firstDay, to: lastDay }
    }
  },
  {
    label: "This Quarter",
    value: () => {
      const today = new Date()
      const quarter = Math.floor(today.getMonth() / 3)
      const firstDay = new Date(today.getFullYear(), quarter * 3, 1)
      const lastDay = new Date(today.getFullYear(), quarter * 3 + 3, 0)
      return { from: firstDay, to: lastDay }
    }
  },
  {
    label: "This School Year",
    value: () => {
      const today = new Date()
      const year = today.getFullYear()
      const month = today.getMonth() + 1
      
      // School year starts in June
      if (month >= 6) {
        return {
          from: new Date(year, 5, 1), // June 1
          to: new Date(year + 1, 4, 31) // May 31 next year
        }
      } else {
        return {
          from: new Date(year - 1, 5, 1), // June 1 previous year
          to: new Date(year, 4, 31) // May 31 current year
        }
      }
    }
  }
]
