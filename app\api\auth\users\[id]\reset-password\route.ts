import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { prisma } from '@/lib/prisma'
import { hashPassword, generateTemporaryPassword } from '@/lib/auth/password'
import { logAuditEntry, hasPermission, getClientIP, getUserAgent } from '@/lib/auth/security'
import { ApiResponse } from '@/lib/auth/types'

// POST /api/auth/users/[id]/reset-password - Reset user password (admin only)
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'users.reset_password')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      } as ApiResponse, { status: 404 })
    }

    // Generate new temporary password
    const temporaryPassword = generateTemporaryPassword()
    const passwordHash = await hashPassword(temporaryPassword)

    // Update user password
    await prisma.user.update({
      where: { id: params.id },
      data: {
        passwordHash,
        passwordChangedAt: new Date(),
        mustChangePassword: true,
        failedLoginAttempts: 0, // Reset failed attempts
        lockedUntil: null // Unlock account if locked
      }
    })

    // Log audit entry
    await logAuditEntry({
      userId: session.user.id,
      action: 'RESET_PASSWORD',
      entityType: 'User',
      entityId: params.id,
      newValues: {
        mustChangePassword: true,
        failedLoginAttempts: 0,
        lockedUntil: null
      },
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request)
    })

    return NextResponse.json({
      success: true,
      message: 'Password reset successfully',
      data: {
        temporaryPassword: temporaryPassword
      }
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Reset password error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// POST /api/auth/users/[id]/unlock - Unlock user account (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'users.unlock')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      } as ApiResponse, { status: 404 })
    }

    // Unlock user account
    await prisma.user.update({
      where: { id: params.id },
      data: {
        failedLoginAttempts: 0,
        lockedUntil: null
      }
    })

    // Log audit entry
    await logAuditEntry({
      userId: session.user.id,
      action: 'UNLOCK_ACCOUNT',
      entityType: 'User',
      entityId: params.id,
      newValues: {
        failedLoginAttempts: 0,
        lockedUntil: null
      },
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request)
    })

    return NextResponse.json({
      success: true,
      message: 'Account unlocked successfully'
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Unlock account error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
