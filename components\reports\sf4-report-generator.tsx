"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { SF4Report, ReportConfig, PrincipalReview } from "@/lib/types/reports"
import { schoolInfo } from "@/lib/data/reports-mock-data"
import { generateSF4Report } from "@/lib/utils/report-utils"
import { mockStudents, mockAttendanceRecords } from "@/lib/data/mock-data"
import { 
  TrendingUp, 
  Calendar, 
  Users, 
  School, 
  Clock,
  CheckCircle,
  AlertCircle,
  Download,
  Eye,
  Printer,
  UserPlus,
  UserMinus,
  UserX,
  BarChart3
} from "lucide-react"
import { format } from "date-fns"

interface SF4ReportGeneratorProps {
  config: ReportConfig
  onGenerate: (report: SF4Report) => void
  onPreview: (report: SF4Report) => void
  className?: string
}

export function SF4ReportGenerator({ 
  config, 
  onGenerate, 
  onPreview, 
  className 
}: SF4ReportGeneratorProps) {
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth())
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [principalReview, setPrincipalReview] = useState<Partial<PrincipalReview>>({
    reviewedBy: schoolInfo.principalName,
    approved: false,
    remarks: ""
  })
  const [reportSettings, setReportSettings] = useState({
    includeSignatures: true,
    includeStatistics: true,
    includeChronicAbsentees: true,
    includePerfectAttendance: true,
    showMovementDetails: true
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedReport, setGeneratedReport] = useState<SF4Report | null>(null)

  const months = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ]

  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 5 }, (_, i) => currentYear - 2 + i)

  // Mock enrollment movement data
  const mockMovementData = {
    newAdmissions: [
      { studentId: "STU101", name: "Maria Santos", dateOfAction: "2025-01-15", reason: "New enrollment" },
      { studentId: "STU102", name: "Juan Dela Cruz", dateOfAction: "2025-01-20", reason: "Transfer from private school" }
    ],
    transfersOut: [
      { studentId: "STU050", name: "Ana Reyes", dateOfAction: "2025-01-10", reason: "Family relocation", destination: "Manila Science High School" }
    ],
    dropouts: [
      { studentId: "STU075", name: "Jose Rodriguez", dateOfAction: "2025-01-25", reason: "Financial difficulties" }
    ]
  }

  const handleGenerateReport = async () => {
    setIsGenerating(true)
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Update config with selected month/year
      const updatedConfig = {
        ...config,
        dateRange: {
          startDate: `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-01`,
          endDate: `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-${new Date(selectedYear, selectedMonth + 1, 0).getDate()}`
        }
      }
      
      // Generate the SF4 report
      const report = generateSF4Report(
        updatedConfig,
        mockStudents,
        mockAttendanceRecords as any[]
      )
      
      // Add principal review
      const enhancedReport: SF4Report = {
        ...report,
        month: months[selectedMonth],
        year: selectedYear.toString(),
        principalReview: {
          ...principalReview,
          reviewDate: new Date().toISOString().split('T')[0]
        } as PrincipalReview
      }
      
      setGeneratedReport(enhancedReport)
      onGenerate(enhancedReport)
    } catch (error) {
      console.error("Error generating SF4 report:", error)
    } finally {
      setIsGenerating(false)
    }
  }

  const handlePreviewReport = () => {
    if (generatedReport) {
      onPreview(generatedReport)
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* SF4 Report Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <CardTitle>SF4 Monthly Learner's Movement</CardTitle>
                <CardDescription>
                  Official DepEd School Form 4 - Monthly Report on Learner's Movement
                </CardDescription>
              </div>
            </div>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              DepEd Official
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* School Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <School className="h-5 w-5" />
            School Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">School Name</Label>
              <p className="text-sm text-muted-foreground">{schoolInfo.schoolName}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">School ID</Label>
              <p className="text-sm text-muted-foreground">{schoolInfo.schoolId}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Division</Label>
              <p className="text-sm text-muted-foreground">{schoolInfo.division}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Region</Label>
              <p className="text-sm text-muted-foreground">{schoolInfo.region}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Period */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Report Period
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="month-select">Month</Label>
              <Select 
                value={selectedMonth.toString()} 
                onValueChange={(value) => setSelectedMonth(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select month" />
                </SelectTrigger>
                <SelectContent>
                  {months.map((month, index) => (
                    <SelectItem key={index} value={index.toString()}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="year-select">Year</Label>
              <Select 
                value={selectedYear.toString()} 
                onValueChange={(value) => setSelectedYear(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select year" />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2 text-blue-800">
              <Calendar className="h-4 w-4" />
              <span className="font-medium">
                Report Period: {months[selectedMonth]} {selectedYear}
              </span>
            </div>
            <p className="text-sm text-blue-600 mt-1">
              This report will cover all learner movements for the selected month
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Learner Movement Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Learner Movement Summary
          </CardTitle>
          <CardDescription>
            Preview of enrollment changes for {months[selectedMonth]} {selectedYear}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Movement Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <UserPlus className="h-6 w-6 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-green-600">{mockMovementData.newAdmissions.length}</div>
              <div className="text-sm text-muted-foreground">New Admissions</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <UserMinus className="h-6 w-6 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-blue-600">{mockMovementData.transfersOut.length}</div>
              <div className="text-sm text-muted-foreground">Transfers Out</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <UserX className="h-6 w-6 text-red-600" />
              </div>
              <div className="text-2xl font-bold text-red-600">{mockMovementData.dropouts.length}</div>
              <div className="text-sm text-muted-foreground">Dropouts</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-purple-600">
                {mockStudents.filter(s => s.status === 'Active').length}
              </div>
              <div className="text-sm text-muted-foreground">Current Enrollment</div>
            </div>
          </div>

          {/* Movement Details */}
          {reportSettings.showMovementDetails && (
            <div className="space-y-4">
              <Separator />
              <h4 className="font-medium">Movement Details</h4>
              
              {/* New Admissions */}
              {mockMovementData.newAdmissions.length > 0 && (
                <div>
                  <h5 className="text-sm font-medium text-green-600 mb-2">New Admissions</h5>
                  <div className="border rounded-lg overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow className="bg-green-50">
                          <TableHead>Student Name</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Reason</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockMovementData.newAdmissions.map((student) => (
                          <TableRow key={student.studentId}>
                            <TableCell>{student.name}</TableCell>
                            <TableCell>{format(new Date(student.dateOfAction), "MMM dd, yyyy")}</TableCell>
                            <TableCell>{student.reason}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}

              {/* Transfers Out */}
              {mockMovementData.transfersOut.length > 0 && (
                <div>
                  <h5 className="text-sm font-medium text-blue-600 mb-2">Transfers Out</h5>
                  <div className="border rounded-lg overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow className="bg-blue-50">
                          <TableHead>Student Name</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Destination</TableHead>
                          <TableHead>Reason</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockMovementData.transfersOut.map((student) => (
                          <TableRow key={student.studentId}>
                            <TableCell>{student.name}</TableCell>
                            <TableCell>{format(new Date(student.dateOfAction), "MMM dd, yyyy")}</TableCell>
                            <TableCell>{student.destination}</TableCell>
                            <TableCell>{student.reason}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}

              {/* Dropouts */}
              {mockMovementData.dropouts.length > 0 && (
                <div>
                  <h5 className="text-sm font-medium text-red-600 mb-2">Dropouts</h5>
                  <div className="border rounded-lg overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow className="bg-red-50">
                          <TableHead>Student Name</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Reason</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockMovementData.dropouts.map((student) => (
                          <TableRow key={student.studentId}>
                            <TableCell>{student.name}</TableCell>
                            <TableCell>{format(new Date(student.dateOfAction), "MMM dd, yyyy")}</TableCell>
                            <TableCell>{student.reason}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Principal Review */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Principal Review
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="principal-name">Principal Name</Label>
              <Input
                id="principal-name"
                value={principalReview.reviewedBy || ""}
                onChange={(e) => setPrincipalReview({...principalReview, reviewedBy: e.target.value})}
                placeholder="Principal's full name"
              />
            </div>
            <div className="space-y-2">
              <Label>Review Status</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="approved"
                  checked={principalReview.approved || false}
                  onCheckedChange={(checked) =>
                    setPrincipalReview({...principalReview, approved: checked as boolean})
                  }
                />
                <Label htmlFor="approved" className="text-sm">
                  Report approved by principal
                </Label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="principal-remarks">Principal's Remarks</Label>
            <Textarea
              id="principal-remarks"
              value={principalReview.remarks || ""}
              onChange={(e) => setPrincipalReview({...principalReview, remarks: e.target.value})}
              placeholder="Optional remarks or comments from the principal"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Report Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Report Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-signatures"
                checked={reportSettings.includeSignatures}
                onCheckedChange={(checked) =>
                  setReportSettings({...reportSettings, includeSignatures: checked as boolean})
                }
              />
              <Label htmlFor="include-signatures" className="text-sm">
                Include signature fields
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-statistics"
                checked={reportSettings.includeStatistics}
                onCheckedChange={(checked) =>
                  setReportSettings({...reportSettings, includeStatistics: checked as boolean})
                }
              />
              <Label htmlFor="include-statistics" className="text-sm">
                Include attendance statistics
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-chronic"
                checked={reportSettings.includeChronicAbsentees}
                onCheckedChange={(checked) =>
                  setReportSettings({...reportSettings, includeChronicAbsentees: checked as boolean})
                }
              />
              <Label htmlFor="include-chronic" className="text-sm">
                Include chronic absentees list
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-perfect"
                checked={reportSettings.includePerfectAttendance}
                onCheckedChange={(checked) =>
                  setReportSettings({...reportSettings, includePerfectAttendance: checked as boolean})
                }
              />
              <Label htmlFor="include-perfect" className="text-sm">
                Include perfect attendance list
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="show-movement"
                checked={reportSettings.showMovementDetails}
                onCheckedChange={(checked) =>
                  setReportSettings({...reportSettings, showMovementDetails: checked as boolean})
                }
              />
              <Label htmlFor="show-movement" className="text-sm">
                Show detailed movement records
              </Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generation Actions */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h3 className="text-lg font-medium">Generate SF4 Report</h3>
              <p className="text-sm text-muted-foreground">
                Create official DepEd SF4 Monthly Learner's Movement Report for {months[selectedMonth]} {selectedYear}
              </p>
            </div>
            <div className="flex items-center gap-2">
              {generatedReport && (
                <>
                  <Button variant="outline" onClick={handlePreviewReport}>
                    <Eye className="mr-2 h-4 w-4" />
                    Preview
                  </Button>
                  <Button variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                  <Button variant="outline">
                    <Printer className="mr-2 h-4 w-4" />
                    Print
                  </Button>
                </>
              )}
              <Button
                onClick={handleGenerateReport}
                disabled={isGenerating}
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <TrendingUp className="mr-2 h-4 w-4" />
                    Generate SF4
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
