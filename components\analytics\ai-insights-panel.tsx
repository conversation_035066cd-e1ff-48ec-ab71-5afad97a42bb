"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Brain, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown, 
  Eye, 
  Lightbulb,
  Target,
  Zap,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  ThumbsUp,
  ThumbsDown,
  MessageSquare
} from "lucide-react"
import { AIInsight } from "@/lib/types/analytics"

interface AIInsightsPanelProps {
  insights: AIInsight[]
  onInsightAction?: (insightId: string, action: string) => void
  onGenerateInsights?: () => void
  onFeedback?: (insightId: string, feedback: 'positive' | 'negative', comment?: string) => void
}

export function AIInsightsPanel({ 
  insights, 
  onInsightAction, 
  onGenerateInsights,
  onFeedback 
}: AIInsightsPanelProps) {
  const [selectedSeverity, setSelectedSeverity] = useState("all")
  const [selectedType, setSelectedType] = useState("all")
  const [isGenerating, setIsGenerating] = useState(false)
  const [expandedInsight, setExpandedInsight] = useState<string | null>(null)

  // Filter insights based on selected criteria
  const filteredInsights = insights.filter(insight => {
    if (selectedSeverity !== "all" && insight.severity !== selectedSeverity) return false
    if (selectedType !== "all" && insight.type !== selectedType) return false
    return true
  })

  // Group insights by type
  const insightsByType = filteredInsights.reduce((acc, insight) => {
    if (!acc[insight.type]) acc[insight.type] = []
    acc[insight.type].push(insight)
    return acc
  }, {} as Record<string, AIInsight[]>)

  // Calculate insight statistics
  const stats = {
    total: insights.length,
    critical: insights.filter(i => i.severity === 'critical').length,
    warning: insights.filter(i => i.severity === 'warning').length,
    info: insights.filter(i => i.severity === 'info').length,
    actionRequired: insights.filter(i => i.actionRequired).length,
    avgConfidence: insights.reduce((sum, i) => sum + i.confidence, 0) / insights.length
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'pattern': return <TrendingUp className="h-4 w-4" />
      case 'anomaly': return <AlertTriangle className="h-4 w-4" />
      case 'prediction': return <Brain className="h-4 w-4" />
      case 'recommendation': return <Lightbulb className="h-4 w-4" />
      default: return <Eye className="h-4 w-4" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200'
      case 'warning': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'info': return 'text-blue-600 bg-blue-50 border-blue-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const handleGenerateInsights = async () => {
    setIsGenerating(true)
    await onGenerateInsights?.()
    setIsGenerating(false)
  }

  const handleInsightAction = (insightId: string, action: string) => {
    onInsightAction?.(insightId, action)
  }

  const handleFeedback = (insightId: string, feedback: 'positive' | 'negative') => {
    onFeedback?.(insightId, feedback)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold flex items-center gap-2">
            <Brain className="h-6 w-6" />
            AI Insights Panel
          </h3>
          <p className="text-muted-foreground">
            Automated pattern detection and intelligent recommendations
          </p>
        </div>
        <Button 
          onClick={handleGenerateInsights} 
          disabled={isGenerating}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${isGenerating ? 'animate-spin' : ''}`} />
          Generate New Insights
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Insights</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.critical}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Warnings</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.warning}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Action Required</CardTitle>
            <Target className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.actionRequired}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Confidence</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(stats.avgConfidence * 100)}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <Select value={selectedSeverity} onValueChange={setSelectedSeverity}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="All Severities" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Severities</SelectItem>
            <SelectItem value="critical">Critical</SelectItem>
            <SelectItem value="warning">Warning</SelectItem>
            <SelectItem value="info">Info</SelectItem>
          </SelectContent>
        </Select>

        <Select value={selectedType} onValueChange={setSelectedType}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="All Types" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="pattern">Patterns</SelectItem>
            <SelectItem value="anomaly">Anomalies</SelectItem>
            <SelectItem value="prediction">Predictions</SelectItem>
            <SelectItem value="recommendation">Recommendations</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Insights Tabs */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Insights</TabsTrigger>
          <TabsTrigger value="patterns">Patterns</TabsTrigger>
          <TabsTrigger value="anomalies">Anomalies</TabsTrigger>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {filteredInsights.length === 0 ? (
            <Card>
              <CardContent className="flex items-center justify-center py-12">
                <div className="text-center">
                  <Brain className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">No insights match your current filters</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredInsights.map((insight) => (
                <Card key={insight.id} className={`${getSeverityColor(insight.severity)}`}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        {getInsightIcon(insight.type)}
                        <div>
                          <CardTitle className="text-lg">{insight.title}</CardTitle>
                          <CardDescription className="mt-1">
                            {insight.description}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={
                          insight.severity === 'critical' ? 'destructive' :
                          insight.severity === 'warning' ? 'secondary' : 'default'
                        }>
                          {insight.severity}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {Math.round(insight.confidence * 100)}% confidence
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent>
                    <div className="space-y-4">
                      {/* Confidence Bar */}
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Confidence Level</span>
                          <span>{Math.round(insight.confidence * 100)}%</span>
                        </div>
                        <Progress value={insight.confidence * 100} className="h-2" />
                      </div>

                      {/* Affected Items */}
                      {(insight.affectedStudents || insight.affectedGrades) && (
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Affected:</p>
                          <div className="flex flex-wrap gap-2">
                            {insight.affectedGrades?.map((grade) => (
                              <Badge key={grade} variant="outline">Grade {grade}</Badge>
                            ))}
                            {insight.affectedStudents?.slice(0, 3).map((studentId) => (
                              <Badge key={studentId} variant="outline">Student {studentId}</Badge>
                            ))}
                            {insight.affectedStudents && insight.affectedStudents.length > 3 && (
                              <Badge variant="outline">+{insight.affectedStudents.length - 3} more</Badge>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Suggested Actions */}
                      {insight.actionRequired && insight.suggestedActions.length > 0 && (
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Suggested Actions:</p>
                          <ul className="text-sm space-y-1">
                            {insight.suggestedActions.map((action, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-current rounded-full mt-2 flex-shrink-0" />
                                <span>{action}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex items-center justify-between pt-4 border-t">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleFeedback(insight.id, 'positive')}
                          >
                            <ThumbsUp className="h-4 w-4 mr-1" />
                            Helpful
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleFeedback(insight.id, 'negative')}
                          >
                            <ThumbsDown className="h-4 w-4 mr-1" />
                            Not Helpful
                          </Button>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-muted-foreground">
                            {formatDate(insight.createdAt)}
                          </span>
                          {insight.actionRequired && (
                            <Button
                              size="sm"
                              onClick={() => handleInsightAction(insight.id, 'implement')}
                            >
                              Take Action
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleInsightAction(insight.id, 'dismiss')}
                          >
                            Dismiss
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Individual type tabs */}
        {Object.entries(insightsByType).map(([type, typeInsights]) => (
          <TabsContent key={type} value={`${type}s`} className="space-y-4">
            <div className="space-y-4">
              {typeInsights.map((insight) => (
                <Card key={insight.id} className={`${getSeverityColor(insight.severity)}`}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        {getInsightIcon(insight.type)}
                        <div>
                          <CardTitle className="text-lg">{insight.title}</CardTitle>
                          <CardDescription className="mt-1">
                            {insight.description}
                          </CardDescription>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {Math.round(insight.confidence * 100)}% confidence
                      </Badge>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
