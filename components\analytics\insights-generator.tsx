"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { 
  Brain, 
  Zap, 
  TrendingUp, 
  AlertTriangle, 
  Eye, 
  Lightbulb,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
  Settings,
  Play,
  Pause,
  BarChart3
} from "lucide-react"
import { AIInsight, PredictiveModel } from "@/lib/types/analytics"

interface InsightsGeneratorProps {
  onInsightsGenerated?: (insights: AIInsight[]) => void
  isGenerating?: boolean
  models?: PredictiveModel[]
}

export function InsightsGenerator({ 
  onInsightsGenerated, 
  isGenerating = false,
  models = []
}: InsightsGeneratorProps) {
  const [generationProgress, setGenerationProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState("")
  const [autoGenerate, setAutoGenerate] = useState(false)
  const [generationHistory, setGenerationHistory] = useState<Array<{
    timestamp: string
    insightsCount: number
    duration: number
    status: 'success' | 'error'
  }>>([])

  // Simulation of AI insight generation process
  const generateInsights = async () => {
    const steps = [
      "Analyzing attendance patterns...",
      "Detecting anomalies...",
      "Running predictive models...",
      "Identifying risk factors...",
      "Generating recommendations...",
      "Validating insights..."
    ]

    setGenerationProgress(0)
    const startTime = Date.now()

    for (let i = 0; i < steps.length; i++) {
      setCurrentStep(steps[i])
      setGenerationProgress((i + 1) / steps.length * 100)
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    // Generate mock insights
    const newInsights: AIInsight[] = [
      {
        id: `INS_${Date.now()}_1`,
        type: 'pattern',
        severity: 'info',
        title: 'Weekly Attendance Pattern Identified',
        description: 'Students show 12% lower attendance on Fridays compared to other weekdays',
        confidence: 0.89,
        affectedGrades: ['11', '12'],
        actionRequired: false,
        suggestedActions: [
          'Schedule engaging activities on Fridays',
          'Monitor Friday attendance trends',
          'Survey students about Friday challenges'
        ],
        createdAt: new Date().toISOString()
      },
      {
        id: `INS_${Date.now()}_2`,
        type: 'anomaly',
        severity: 'warning',
        title: 'Unusual Absence Spike Detected',
        description: 'Grade 10 students showing 25% increase in absences over the past week',
        confidence: 0.92,
        affectedGrades: ['10'],
        actionRequired: true,
        suggestedActions: [
          'Investigate potential causes',
          'Contact grade 10 teachers',
          'Review recent schedule changes'
        ],
        createdAt: new Date().toISOString()
      },
      {
        id: `INS_${Date.now()}_3`,
        type: 'prediction',
        severity: 'critical',
        title: 'Dropout Risk Prediction Alert',
        description: 'AI model predicts 2 students have >80% probability of dropping out within 2 weeks',
        confidence: 0.94,
        affectedStudents: ['STU015', 'STU027'],
        actionRequired: true,
        suggestedActions: [
          'Immediate counseling intervention',
          'Parent engagement meetings',
          'Academic support programs'
        ],
        createdAt: new Date().toISOString()
      }
    ]

    const duration = Date.now() - startTime
    setGenerationHistory(prev => [...prev, {
      timestamp: new Date().toISOString(),
      insightsCount: newInsights.length,
      duration,
      status: 'success'
    }].slice(-10)) // Keep last 10 generations

    setCurrentStep("Insights generated successfully!")
    onInsightsGenerated?.(newInsights)

    // Reset after a delay
    setTimeout(() => {
      setGenerationProgress(0)
      setCurrentStep("")
    }, 2000)
  }

  // Auto-generation effect
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (autoGenerate && !isGenerating) {
      interval = setInterval(() => {
        generateInsights()
      }, 300000) // Every 5 minutes
    }
    return () => clearInterval(interval)
  }, [autoGenerate, isGenerating])

  const formatDuration = (ms: number) => {
    return `${(ms / 1000).toFixed(1)}s`
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Insights Generator
          </h3>
          <p className="text-muted-foreground">
            Automated pattern detection and insight generation
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={autoGenerate ? "default" : "outline"}
            size="sm"
            onClick={() => setAutoGenerate(!autoGenerate)}
          >
            {autoGenerate ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
            Auto Generate
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      {/* Generation Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Generation Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {isGenerating || generationProgress > 0 ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Progress</span>
                <span className="text-sm text-muted-foreground">
                  {Math.round(generationProgress)}%
                </span>
              </div>
              <Progress value={generationProgress} className="h-2" />
              {currentStep && (
                <p className="text-sm text-muted-foreground flex items-center gap-2">
                  <RefreshCw className="h-3 w-3 animate-spin" />
                  {currentStep}
                </p>
              )}
            </div>
          ) : (
            <div className="text-center py-4">
              <Button onClick={generateInsights} disabled={isGenerating}>
                <Brain className="h-4 w-4 mr-2" />
                Generate New Insights
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Model Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            AI Models Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Dropout Prediction</span>
                <Badge variant="default">Active</Badge>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Accuracy</span>
                  <span>94.2%</span>
                </div>
                <Progress value={94.2} className="h-1" />
              </div>
              <p className="text-xs text-muted-foreground">
                Last trained: 2 days ago
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Pattern Detection</span>
                <Badge variant="default">Active</Badge>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Accuracy</span>
                  <span>87.8%</span>
                </div>
                <Progress value={87.8} className="h-1" />
              </div>
              <p className="text-xs text-muted-foreground">
                Last trained: 1 day ago
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Risk Assessment</span>
                <Badge variant="default">Active</Badge>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Accuracy</span>
                  <span>91.5%</span>
                </div>
                <Progress value={91.5} className="h-1" />
              </div>
              <p className="text-xs text-muted-foreground">
                Last trained: 3 hours ago
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Anomaly Detection</span>
                <Badge variant="secondary">Training</Badge>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Progress</span>
                  <span>73%</span>
                </div>
                <Progress value={73} className="h-1" />
              </div>
              <p className="text-xs text-muted-foreground">
                ETA: 2 hours
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generation History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Generation History
          </CardTitle>
        </CardHeader>
        <CardContent>
          {generationHistory.length === 0 ? (
            <p className="text-center text-muted-foreground py-4">
              No generation history yet
            </p>
          ) : (
            <div className="space-y-3">
              {generationHistory.map((entry, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    {entry.status === 'success' ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <div>
                      <p className="text-sm font-medium">
                        {entry.insightsCount} insights generated
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatTimestamp(entry.timestamp)} • {formatDuration(entry.duration)}
                      </p>
                    </div>
                  </div>
                  <Badge variant={entry.status === 'success' ? 'default' : 'destructive'}>
                    {entry.status}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Insight Categories */}
      <Tabs defaultValue="patterns" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="patterns">Patterns</TabsTrigger>
          <TabsTrigger value="anomalies">Anomalies</TabsTrigger>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="patterns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Pattern Detection
              </CardTitle>
              <CardDescription>
                Identifies recurring patterns in attendance data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Weekly patterns</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Seasonal trends</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Time-of-day patterns</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Subject-specific patterns</span>
                  <Badge variant="secondary">Disabled</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="anomalies" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Anomaly Detection
              </CardTitle>
              <CardDescription>
                Detects unusual deviations from normal patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Statistical anomalies</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Sudden changes</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Outlier detection</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="predictions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Predictive Analytics
              </CardTitle>
              <CardDescription>
                Forecasts future attendance and risk levels
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Dropout prediction</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Attendance forecasting</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Risk assessment</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5" />
                Recommendation Engine
              </CardTitle>
              <CardDescription>
                Generates actionable recommendations based on insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Intervention suggestions</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Resource allocation</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Policy recommendations</span>
                  <Badge variant="secondary">Disabled</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
