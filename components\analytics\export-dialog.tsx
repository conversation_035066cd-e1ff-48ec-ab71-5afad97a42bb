"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, Dialog<PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { 
  Download, 
  FileText, 
  FileSpreadsheet, 
  File, 
  Image,
  Calendar,
  Filter,
  Settings,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import { AnalyticsFilter, ExportConfig } from "@/lib/types/analytics"

interface ExportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onExport: (config: ExportConfig) => void
  filters?: AnalyticsFilter
}

export function ExportDialog({ open, onOpenChange, onExport, filters }: ExportDialogProps) {
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    format: 'pdf',
    includeCharts: true,
    includeRawData: false,
    includeInsights: true,
    dateRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end: new Date().toISOString().split('T')[0]
    },
    filters: filters || {
      dateRange: {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end: new Date().toISOString().split('T')[0]
      },
      grades: [],
      sections: [],
      courses: [],
      subjects: [],
      riskLevels: []
    }
  })

  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)
  const [exportStatus, setExportStatus] = useState<'idle' | 'preparing' | 'generating' | 'complete' | 'error'>('idle')

  const formatOptions = [
    { value: 'pdf', label: 'PDF Report', icon: FileText, description: 'Comprehensive report with charts and analysis' },
    { value: 'excel', label: 'Excel Workbook', icon: FileSpreadsheet, description: 'Detailed data with multiple sheets' },
    { value: 'csv', label: 'CSV Data', icon: File, description: 'Raw data for further analysis' },
    { value: 'json', label: 'JSON Data', icon: File, description: 'Structured data for developers' }
  ]

  const handleExport = async () => {
    setIsExporting(true)
    setExportStatus('preparing')
    setExportProgress(0)

    try {
      // Simulate export process
      const steps = [
        'Preparing data...',
        'Generating charts...',
        'Compiling report...',
        'Finalizing export...'
      ]

      for (let i = 0; i < steps.length; i++) {
        setExportProgress((i + 1) / steps.length * 100)
        await new Promise(resolve => setTimeout(resolve, 1000))
      }

      setExportStatus('complete')
      onExport(exportConfig)
      
      // Reset after delay
      setTimeout(() => {
        setIsExporting(false)
        setExportStatus('idle')
        setExportProgress(0)
        onOpenChange(false)
      }, 2000)

    } catch (error) {
      setExportStatus('error')
      setIsExporting(false)
    }
  }

  const updateConfig = (updates: Partial<ExportConfig>) => {
    setExportConfig(prev => ({ ...prev, ...updates }))
  }

  const getFormatIcon = (format: string) => {
    const option = formatOptions.find(opt => opt.value === format)
    return option ? <option.icon className="h-4 w-4" /> : <File className="h-4 w-4" />
  }

  const estimateFileSize = () => {
    let size = 0.5 // Base size in MB
    
    if (exportConfig.includeCharts) size += 2
    if (exportConfig.includeRawData) size += 5
    if (exportConfig.includeInsights) size += 1
    
    if (exportConfig.format === 'excel') size *= 1.5
    if (exportConfig.format === 'pdf') size *= 0.8
    
    return `~${size.toFixed(1)} MB`
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Analytics Report
          </DialogTitle>
          <DialogDescription>
            Configure and export your analytics data in various formats
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Export Format</Label>
            <div className="grid gap-3 md:grid-cols-2">
              {formatOptions.map((option) => (
                <div
                  key={option.value}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    exportConfig.format === option.value 
                      ? 'border-primary bg-primary/5' 
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => updateConfig({ format: option.value as any })}
                >
                  <div className="flex items-center gap-3">
                    <option.icon className="h-5 w-5" />
                    <div>
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-muted-foreground">{option.description}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Content Options */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Include Content</Label>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-charts"
                  checked={exportConfig.includeCharts}
                  onCheckedChange={(checked) => updateConfig({ includeCharts: !!checked })}
                />
                <Label htmlFor="include-charts" className="flex items-center gap-2">
                  <Image className="h-4 w-4" />
                  Charts and Visualizations
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-insights"
                  checked={exportConfig.includeInsights}
                  onCheckedChange={(checked) => updateConfig({ includeInsights: !!checked })}
                />
                <Label htmlFor="include-insights" className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" />
                  AI Insights and Recommendations
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-raw-data"
                  checked={exportConfig.includeRawData}
                  onCheckedChange={(checked) => updateConfig({ includeRawData: !!checked })}
                />
                <Label htmlFor="include-raw-data" className="flex items-center gap-2">
                  <File className="h-4 w-4" />
                  Raw Data Tables
                </Label>
              </div>
            </div>
          </div>

          {/* Date Range */}
          <div className="space-y-3">
            <Label className="text-base font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Date Range
            </Label>
            <div className="grid gap-3 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="start-date" className="text-sm">Start Date</Label>
                <Input
                  id="start-date"
                  type="date"
                  value={exportConfig.dateRange.start}
                  onChange={(e) => updateConfig({
                    dateRange: { ...exportConfig.dateRange, start: e.target.value }
                  })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end-date" className="text-sm">End Date</Label>
                <Input
                  id="end-date"
                  type="date"
                  value={exportConfig.dateRange.end}
                  onChange={(e) => updateConfig({
                    dateRange: { ...exportConfig.dateRange, end: e.target.value }
                  })}
                />
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="space-y-3">
            <Label className="text-base font-medium flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filters
            </Label>
            <div className="grid gap-3 md:grid-cols-2">
              <div className="space-y-2">
                <Label className="text-sm">Grades</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All grades" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Grades</SelectItem>
                    <SelectItem value="7">Grade 7</SelectItem>
                    <SelectItem value="8">Grade 8</SelectItem>
                    <SelectItem value="9">Grade 9</SelectItem>
                    <SelectItem value="10">Grade 10</SelectItem>
                    <SelectItem value="11">Grade 11</SelectItem>
                    <SelectItem value="12">Grade 12</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label className="text-sm">Risk Levels</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="All risk levels" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Risk Levels</SelectItem>
                    <SelectItem value="low">Low Risk</SelectItem>
                    <SelectItem value="medium">Medium Risk</SelectItem>
                    <SelectItem value="high">High Risk</SelectItem>
                    <SelectItem value="critical">Critical Risk</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Export Progress */}
          {isExporting && (
            <div className="space-y-3 p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Export Progress</span>
                <span className="text-sm text-muted-foreground">
                  {Math.round(exportProgress)}%
                </span>
              </div>
              <Progress value={exportProgress} className="h-2" />
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                {exportStatus === 'complete' ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <div className="h-4 w-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                )}
                <span>
                  {exportStatus === 'preparing' && 'Preparing data...'}
                  {exportStatus === 'generating' && 'Generating report...'}
                  {exportStatus === 'complete' && 'Export complete!'}
                  {exportStatus === 'error' && 'Export failed'}
                </span>
              </div>
            </div>
          )}

          {/* Export Summary */}
          <div className="p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Export Summary</span>
              <Badge variant="outline">{estimateFileSize()}</Badge>
            </div>
            <div className="space-y-1 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                {getFormatIcon(exportConfig.format)}
                <span>{formatOptions.find(opt => opt.value === exportConfig.format)?.label}</span>
              </div>
              <div>
                Date range: {exportConfig.dateRange.start} to {exportConfig.dateRange.end}
              </div>
              <div className="flex gap-4">
                {exportConfig.includeCharts && <span>• Charts</span>}
                {exportConfig.includeInsights && <span>• Insights</span>}
                {exportConfig.includeRawData && <span>• Raw Data</span>}
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isExporting}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
