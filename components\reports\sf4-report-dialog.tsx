"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { SF4ReportGenerator } from "./sf4-report-generator"
import { SF4ReportPreview } from "./sf4-report-preview"
import { ReportConfig, SF4Report, ExportFormat } from "@/lib/types/reports"
import { ArrowLeft, TrendingUp, Eye } from "lucide-react"
import { toast } from "sonner"

interface SF4ReportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  config: ReportConfig
}

export function SF4ReportDialog({ open, onOpenChange, config }: SF4ReportDialogProps) {
  const [activeTab, setActiveTab] = useState("generate")
  const [generatedReport, setGeneratedReport] = useState<SF4Report | null>(null)

  const handleGenerateReport = (report: SF4Report) => {
    setGeneratedReport(report)
    setActiveTab("preview")
    toast.success("SF4 report generated successfully", {
      description: "You can now preview, download, or print the report"
    })
  }

  const handlePreviewReport = (report: SF4Report) => {
    setGeneratedReport(report)
    setActiveTab("preview")
  }

  const handleDownload = (format: ExportFormat) => {
    toast.success(`Downloading SF4 report in ${format} format`, {
      description: "The download will start shortly"
    })
  }

  const handlePrint = () => {
    window.print()
    toast.success("Print dialog opened", {
      description: "Please select your printer and print settings"
    })
  }

  const handleBackToGenerate = () => {
    setActiveTab("generate")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            SF4 Monthly Learner's Movement Generator
          </DialogTitle>
          <DialogDescription>
            Generate official DepEd School Form 4 - Monthly Report on Learner's Movement
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Generate Report
            </TabsTrigger>
            <TabsTrigger 
              value="preview" 
              disabled={!generatedReport}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              Preview Report
            </TabsTrigger>
          </TabsList>

          <TabsContent value="generate" className="mt-6">
            <SF4ReportGenerator
              config={config}
              onGenerate={handleGenerateReport}
              onPreview={handlePreviewReport}
            />
          </TabsContent>

          <TabsContent value="preview" className="mt-6">
            {generatedReport ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Button 
                    variant="outline" 
                    onClick={handleBackToGenerate}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Back to Generator
                  </Button>
                </div>
                
                <SF4ReportPreview
                  report={generatedReport}
                  onDownload={handleDownload}
                  onPrint={handlePrint}
                />
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No report generated yet</p>
                <p className="text-sm">Generate a report first to see the preview</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
