import { PrismaClient } from '../lib/generated/prisma'

const prisma = new PrismaClient()

// Philippine-specific data for realistic seeding
const PHILIPPINE_FIRST_NAMES = {
  male: [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
  ],
  female: [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
  ]
}

const PHILIPPINE_LAST_NAMES = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>',
  '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Lee', 'Wong', 'Chan', 'Chua'
]

const MIDDLE_NAMES = [
  'dela Cruz', 'delos Santos', 'del Rosario', 'de la Torre', 'San Jose',
  'Santa Maria', 'Villanueva', 'Fernandez', 'Rodriguez', 'Gonzales'
]

const BARANGAYS_TANAUAN = [
  'Aslum', 'Bato', 'Bislig', 'Cabarasan Guti', 'Cabarasan Ilaya', 'Calogcog',
  'Camaligam', 'Caridad', 'Hilapnitan', 'Kiling', 'Lim-ao', 'Linao', 'Lumbang',
  'Marasbaras', 'Pago', 'Poblacion', 'Salvacion', 'San Agustin', 'San Isidro',
  'San Pablo', 'San Roque', 'Santa Cruz', 'Santo Niño', 'Solano', 'Tigbao',
  'Tunga', 'Utap'
]

const SUBJECTS_BY_GRADE = {
  'GRADE_7': [
    { code: 'ENG7', name: 'English 7', credits: 1 },
    { code: 'FIL7', name: 'Filipino 7', credits: 1 },
    { code: 'MATH7', name: 'Mathematics 7', credits: 1 },
    { code: 'SCI7', name: 'Science 7', credits: 1 },
    { code: 'AP7', name: 'Araling Panlipunan 7', credits: 1 },
    { code: 'TLE7', name: 'Technology and Livelihood Education 7', credits: 1 },
    { code: 'MAPEH7', name: 'Music, Arts, Physical Education, and Health 7', credits: 1 },
    { code: 'ESP7', name: 'Edukasyon sa Pagpapakatao 7', credits: 1 }
  ],
  'GRADE_8': [
    { code: 'ENG8', name: 'English 8', credits: 1 },
    { code: 'FIL8', name: 'Filipino 8', credits: 1 },
    { code: 'MATH8', name: 'Mathematics 8', credits: 1 },
    { code: 'SCI8', name: 'Science 8', credits: 1 },
    { code: 'AP8', name: 'Araling Panlipunan 8', credits: 1 },
    { code: 'TLE8', name: 'Technology and Livelihood Education 8', credits: 1 },
    { code: 'MAPEH8', name: 'Music, Arts, Physical Education, and Health 8', credits: 1 },
    { code: 'ESP8', name: 'Edukasyon sa Pagpapakatao 8', credits: 1 }
  ],
  'GRADE_9': [
    { code: 'ENG9', name: 'English 9', credits: 1 },
    { code: 'FIL9', name: 'Filipino 9', credits: 1 },
    { code: 'MATH9', name: 'Mathematics 9', credits: 1 },
    { code: 'SCI9', name: 'Science 9', credits: 1 },
    { code: 'AP9', name: 'Araling Panlipunan 9', credits: 1 },
    { code: 'TLE9', name: 'Technology and Livelihood Education 9', credits: 1 },
    { code: 'MAPEH9', name: 'Music, Arts, Physical Education, and Health 9', credits: 1 },
    { code: 'ESP9', name: 'Edukasyon sa Pagpapakatao 9', credits: 1 }
  ],
  'GRADE_10': [
    { code: 'ENG10', name: 'English 10', credits: 1 },
    { code: 'FIL10', name: 'Filipino 10', credits: 1 },
    { code: 'MATH10', name: 'Mathematics 10', credits: 1 },
    { code: 'SCI10', name: 'Science 10', credits: 1 },
    { code: 'AP10', name: 'Araling Panlipunan 10', credits: 1 },
    { code: 'TLE10', name: 'Technology and Livelihood Education 10', credits: 1 },
    { code: 'MAPEH10', name: 'Music, Arts, Physical Education, and Health 10', credits: 1 },
    { code: 'ESP10', name: 'Edukasyon sa Pagpapakatao 10', credits: 1 }
  ],
  'GRADE_11': [
    { code: 'ORAL11', name: 'Oral Communication 11', credits: 1 },
    { code: 'READ11', name: 'Reading and Writing 11', credits: 1 },
    { code: 'KOMFIL11', name: 'Komunikasyon at Pananaliksik 11', credits: 1 },
    { code: 'GENMATH11', name: 'General Mathematics 11', credits: 1 },
    { code: 'EARTHSCI11', name: 'Earth and Life Science 11', credits: 1 },
    { code: 'PERSDEV11', name: 'Personal Development 11', credits: 1 },
    { code: 'PE11', name: 'Physical Education and Health 11', credits: 1 },
    { code: 'EMPTECH11', name: 'Empowerment Technologies 11', credits: 1 }
  ],
  'GRADE_12': [
    { code: 'RESEARCH12', name: 'Research 12', credits: 1 },
    { code: 'INQUIRE12', name: 'Inquiries, Investigations and Immersion 12', credits: 1 },
    { code: 'CONTEMP12', name: 'Contemporary Philippine Arts 12', credits: 1 },
    { code: 'MEDIA12', name: 'Media and Information Literacy 12', credits: 1 },
    { code: 'PHILO12', name: 'Introduction to Philosophy 12', credits: 1 },
    { code: 'PE12', name: 'Physical Education and Health 12', credits: 1 },
    { code: 'PRAC12', name: 'Practical Research 12', credits: 1 },
    { code: 'WORK12', name: 'Work Immersion 12', credits: 1 }
  ]
}

const TRACKS_STRANDS = [
  'STEM', 'HUMSS', 'ABM', 'GAS', 'TVL-ICT', 'TVL-HE', 'TVL-IA', 'TVL-AGRI',
  'ARTS AND DESIGN', 'SPORTS'
]

const SECTIONS = ['A', 'B', 'C', 'D', 'E', 'F']

// Utility functions
function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)]
}

function generateStudentId(): string {
  // DepEd format: 12-digit number
  return Math.floor(100000000000 + Math.random() * 900000000000).toString()
}

function generateEmployeeId(): string {
  // Employee ID format: EMP-YYYY-NNNN
  const year = new Date().getFullYear()
  const number = Math.floor(1000 + Math.random() * 9000)
  return `EMP-${year}-${number}`
}

function generatePhoneNumber(): string {
  // Philippine mobile format: 09XXXXXXXXX
  const prefix = '09'
  const number = Math.floor(100000000 + Math.random() * 900000000)
  return prefix + number.toString()
}

function generateBirthDate(gradeLevel: string): Date {
  const currentYear = new Date().getFullYear()
  let baseAge: number
  
  switch (gradeLevel) {
    case 'GRADE_7': baseAge = 12; break
    case 'GRADE_8': baseAge = 13; break
    case 'GRADE_9': baseAge = 14; break
    case 'GRADE_10': baseAge = 15; break
    case 'GRADE_11': baseAge = 16; break
    case 'GRADE_12': baseAge = 17; break
    default: baseAge = 15
  }
  
  const birthYear = currentYear - baseAge - Math.floor(Math.random() * 2)
  const birthMonth = Math.floor(Math.random() * 12)
  const birthDay = Math.floor(Math.random() * 28) + 1
  
  return new Date(birthYear, birthMonth, birthDay)
}

async function main() {
  console.log('🌱 Starting database seeding...')
  
  // Clear existing data
  console.log('🧹 Clearing existing data...')
  await prisma.attendance.deleteMany()
  await prisma.attendancePattern.deleteMany()
  await prisma.studentRiskAssessment.deleteMany()
  await prisma.sMSLog.deleteMany()
  await prisma.notificationQueue.deleteMany()
  await prisma.auditLog.deleteMany()
  await prisma.systemSettings.deleteMany()
  await prisma.subject.deleteMany()
  await prisma.student.deleteMany()
  await prisma.teacher.deleteMany()
  
  console.log('✅ Existing data cleared')
  
  // Create system settings
  console.log('⚙️ Creating system settings...')
  await prisma.systemSettings.createMany({
    data: [
      {
        key: 'school_name',
        value: 'Tanauan National High School',
        description: 'Official school name',
        category: 'school'
      },
      {
        key: 'school_address',
        value: 'Poblacion, Tanauan, Leyte',
        description: 'School address',
        category: 'school'
      },
      {
        key: 'school_year',
        value: '2024-2025',
        description: 'Current school year',
        category: 'academic'
      },
      {
        key: 'attendance_cutoff_time',
        value: '07:30',
        description: 'Time after which students are marked late',
        category: 'attendance'
      },
      {
        key: 'sms_provider',
        value: 'semaphore',
        description: 'SMS service provider',
        category: 'communication'
      }
    ]
  })
  
  console.log('✅ System settings created')
  
  // Create teachers
  console.log('👨‍🏫 Creating teachers...')
  const teachers = []
  
  // Create principal
  teachers.push({
    employeeId: generateEmployeeId(),
    firstName: 'Maria',
    lastName: 'Rodriguez',
    middleName: 'Santos',
    email: '<EMAIL>',
    phoneNumber: generatePhoneNumber(),
    role: 'PRINCIPAL',
    subjects: null,
    gradeLevels: null,
    sections: null,
    status: 'ACTIVE',
    hireDate: new Date('2015-06-01')
  })
  
  // Create guidance counselor
  teachers.push({
    employeeId: generateEmployeeId(),
    firstName: 'Ana',
    lastName: 'Mendoza',
    middleName: 'Cruz',
    email: '<EMAIL>',
    phoneNumber: generatePhoneNumber(),
    role: 'GUIDANCE_COUNSELOR',
    subjects: null,
    gradeLevels: JSON.stringify(['GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12']),
    sections: null,
    status: 'ACTIVE',
    hireDate: new Date('2018-06-01')
  })
  
  // Create regular teachers
  const teacherNames = [
    { firstName: 'Juan', lastName: 'Dela Cruz', middleName: 'Santos' },
    { firstName: 'Carmen', lastName: 'Reyes', middleName: 'Garcia' },
    { firstName: 'Roberto', lastName: 'Torres', middleName: 'Mendoza' },
    { firstName: 'Elena', lastName: 'Bautista', middleName: 'Cruz' },
    { firstName: 'Miguel', lastName: 'Ocampo', middleName: 'Ramos' },
    { firstName: 'Rosa', lastName: 'Marquez', middleName: 'Santos' },
    { firstName: 'Carlos', lastName: 'Aquino', middleName: 'Torres' },
    { firstName: 'Patricia', lastName: 'Mercado', middleName: 'Garcia' }
  ]
  
  for (const teacherName of teacherNames) {
    const gradeLevel = getRandomElement(['GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12'])
    const subjects = SUBJECTS_BY_GRADE[gradeLevel as keyof typeof SUBJECTS_BY_GRADE]
    const assignedSubjects = subjects.slice(0, Math.floor(Math.random() * 3) + 1)
    
    teachers.push({
      employeeId: generateEmployeeId(),
      firstName: teacherName.firstName,
      lastName: teacherName.lastName,
      middleName: teacherName.middleName,
      email: `${teacherName.firstName.toLowerCase()}.${teacherName.lastName.toLowerCase()}@tanauan-nhs.edu.ph`,
      phoneNumber: generatePhoneNumber(),
      role: 'TEACHER',
      subjects: JSON.stringify(assignedSubjects.map(s => s.code)),
      gradeLevels: JSON.stringify([gradeLevel]),
      sections: JSON.stringify([getRandomElement(SECTIONS)]),
      status: 'ACTIVE',
      hireDate: new Date(2020 + Math.floor(Math.random() * 4), 5, 1)
    })
  }
  
  const createdTeachers = await prisma.teacher.createManyAndReturn({ data: teachers })
  console.log(`✅ Created ${createdTeachers.length} teachers`)
  
  // Create subjects
  console.log('📚 Creating subjects...')
  const subjects = []
  
  for (const [gradeLevel, gradeSubjects] of Object.entries(SUBJECTS_BY_GRADE)) {
    for (const subject of gradeSubjects) {
      const teacher = createdTeachers.find(t => 
        t.role === 'TEACHER' && 
        t.gradeLevels?.includes(gradeLevel)
      )
      
      subjects.push({
        subjectCode: subject.code,
        subjectName: subject.name,
        gradeLevel: gradeLevel as any,
        credits: subject.credits,
        room: `Room ${Math.floor(Math.random() * 20) + 101}`,
        teacherId: teacher?.id || null,
        schedule: JSON.stringify([
          {
            day: getRandomElement(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']),
            startTime: `${7 + Math.floor(Math.random() * 8)}:00`,
            endTime: `${8 + Math.floor(Math.random() * 8)}:00`
          }
        ])
      })
    }
  }
  
  const createdSubjects = await prisma.subject.createManyAndReturn({ data: subjects })
  console.log(`✅ Created ${createdSubjects.length} subjects`)

  // Create students
  console.log('👨‍🎓 Creating students...')
  const students = []

  // Generate students for each grade level
  const gradeLevels = ['GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12']
  const studentsPerGrade = 50 // Total 300 students

  for (const gradeLevel of gradeLevels) {
    for (let i = 0; i < studentsPerGrade; i++) {
      const gender = getRandomElement(['MALE', 'FEMALE'])
      const firstName = getRandomElement(PHILIPPINE_FIRST_NAMES[gender.toLowerCase() as keyof typeof PHILIPPINE_FIRST_NAMES])
      const lastName = getRandomElement(PHILIPPINE_LAST_NAMES)
      const middleName = Math.random() > 0.3 ? getRandomElement(MIDDLE_NAMES) : null
      const section = getRandomElement(SECTIONS)
      const course = gradeLevel.includes('11') || gradeLevel.includes('12')
        ? getRandomElement(TRACKS_STRANDS)
        : 'Junior High School'

      const guardianRelationship = getRandomElement(['FATHER', 'MOTHER', 'GUARDIAN', 'GRANDPARENT'])
      const guardianGender = guardianRelationship === 'FATHER' ? 'male' : 'female'
      const guardianFirstName = getRandomElement(PHILIPPINE_FIRST_NAMES[guardianGender])

      students.push({
        studentId: generateStudentId(),
        firstName,
        lastName,
        middleName,
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@student.tanauan-nhs.edu.ph`,
        dateOfBirth: generateBirthDate(gradeLevel),
        gender: gender as any,
        gradeLevel: gradeLevel as any,
        section,
        course,
        year: '2024-2025',
        status: 'ACTIVE',
        enrollmentDate: new Date('2024-06-01'),
        guardianName: `${guardianFirstName} ${lastName}`,
        guardianPhone: generatePhoneNumber(),
        guardianEmail: Math.random() > 0.5 ? `${guardianFirstName.toLowerCase()}.${lastName.toLowerCase()}@gmail.com` : null,
        guardianRelationship: guardianRelationship as any,
        emergencyContactName: `${getRandomElement(PHILIPPINE_FIRST_NAMES.female)} ${getRandomElement(PHILIPPINE_LAST_NAMES)}`,
        emergencyContactPhone: generatePhoneNumber(),
        emergencyContactRelationship: getRandomElement(['Aunt', 'Uncle', 'Neighbor', 'Family Friend']),
        address: `${Math.floor(Math.random() * 999) + 1} ${getRandomElement(['Main St.', 'Rizal Ave.', 'Bonifacio St.', 'Luna St.', 'Mabini St.'])}`,
        barangay: getRandomElement(BARANGAYS_TANAUAN),
        municipality: 'Tanauan',
        province: 'Leyte',
        zipCode: '6502',
        photoUrl: null,
        qrCodeData: null
      })
    }
  }

  const createdStudents = await prisma.student.createManyAndReturn({ data: students })
  console.log(`✅ Created ${createdStudents.length} students`)

  // Create sample attendance records
  console.log('📋 Creating sample attendance records...')
  const attendanceRecords = []
  const today = new Date()
  const daysBack = 30 // Create attendance for last 30 days

  for (let dayOffset = 0; dayOffset < daysBack; dayOffset++) {
    const attendanceDate = new Date(today)
    attendanceDate.setDate(today.getDate() - dayOffset)

    // Skip weekends
    if (attendanceDate.getDay() === 0 || attendanceDate.getDay() === 6) continue

    for (const student of createdStudents.slice(0, 100)) { // Sample for first 100 students
      const attendanceChance = Math.random()

      if (attendanceChance > 0.1) { // 90% attendance rate
        const isLate = Math.random() < 0.15 // 15% chance of being late
        const timeIn = new Date(attendanceDate)
        timeIn.setHours(isLate ? 8 : 7, Math.floor(Math.random() * 60), 0, 0)

        const timeOut = new Date(attendanceDate)
        timeOut.setHours(15 + Math.floor(Math.random() * 2), Math.floor(Math.random() * 60), 0, 0)

        attendanceRecords.push({
          studentId: student.id,
          date: attendanceDate,
          timeIn,
          timeOut,
          attendanceType: 'GATE',
          subjectId: null,
          status: isLate ? 'LATE' : 'PRESENT',
          scannedBy: getRandomElement(createdTeachers.filter(t => t.role === 'TEACHER')).id,
          notes: isLate ? 'Late arrival' : null,
          isManualEntry: false,
          location: 'Main Gate',
          deviceId: 'SCANNER_001'
        })
      }
    }
  }

  await prisma.attendance.createMany({ data: attendanceRecords })
  console.log(`✅ Created ${attendanceRecords.length} attendance records`)

  // Create sample attendance patterns
  console.log('📊 Creating attendance patterns...')
  const attendancePatterns = []

  for (const student of createdStudents.slice(0, 50)) { // Sample for first 50 students
    const studentAttendance = attendanceRecords.filter(a => a.studentId === student.id)
    const totalDays = daysBack * 5 / 7 // Approximate school days
    const presentDays = studentAttendance.length
    const weeklyRate = presentDays / (totalDays / 7)
    const monthlyRate = presentDays / totalDays

    const riskScore = Math.max(0, Math.min(100, (1 - monthlyRate) * 100 + Math.random() * 20))
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW'

    if (riskScore > 75) riskLevel = 'CRITICAL'
    else if (riskScore > 50) riskLevel = 'HIGH'
    else if (riskScore > 25) riskLevel = 'MEDIUM'

    attendancePatterns.push({
      studentId: student.id,
      weeklyRate: Math.min(1, weeklyRate),
      monthlyRate: Math.min(1, monthlyRate),
      consecutiveAbsences: Math.floor(Math.random() * 5),
      latePattern: JSON.stringify({
        monday: Math.random() * 0.3,
        tuesday: Math.random() * 0.3,
        wednesday: Math.random() * 0.3,
        thursday: Math.random() * 0.3,
        friday: Math.random() * 0.3
      }),
      riskScore,
      riskLevel: riskLevel as any,
      predictions: JSON.stringify({
        nextWeekAttendance: Math.random() * 0.4 + 0.6,
        dropoutRisk: riskScore / 100,
        interventionNeeded: riskScore > 50
      }),
      insights: JSON.stringify([
        riskScore > 50 ? 'Student shows declining attendance pattern' : 'Student maintains good attendance',
        monthlyRate < 0.8 ? 'Requires parent consultation' : 'No immediate action needed'
      ]),
      lastAnalyzed: new Date(),
      dataPoints: studentAttendance.length
    })
  }

  await prisma.attendancePattern.createMany({ data: attendancePatterns })
  console.log(`✅ Created ${attendancePatterns.length} attendance patterns`)

  // Create sample SMS logs
  console.log('📱 Creating SMS logs...')
  const smsLogs = []

  for (let i = 0; i < 20; i++) {
    const student = getRandomElement(createdStudents)
    const messageTypes = ['ATTENDANCE_ALERT', 'LATE_ARRIVAL', 'GENERAL', 'PARENT_MEETING']
    const messageType = getRandomElement(messageTypes)

    let message = ''
    switch (messageType) {
      case 'ATTENDANCE_ALERT':
        message = `Dear Parent/Guardian, your child ${student.firstName} ${student.lastName} was absent today. Please contact the school if this is excused. - Tanauan NHS`
        break
      case 'LATE_ARRIVAL':
        message = `Dear Parent/Guardian, your child ${student.firstName} ${student.lastName} arrived late today at school. - Tanauan NHS`
        break
      case 'GENERAL':
        message = `Reminder: Parent-Teacher Conference on Friday, 2:00 PM. Your attendance is important. - Tanauan NHS`
        break
      case 'PARENT_MEETING':
        message = `Dear Parent/Guardian, you are invited to discuss ${student.firstName}'s academic progress. Please contact us to schedule. - Tanauan NHS`
        break
    }

    const sentDate = new Date(today.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000)

    smsLogs.push({
      recipientPhone: student.guardianPhone,
      message,
      status: getRandomElement(['SENT', 'DELIVERED', 'FAILED']) as any,
      sentAt: sentDate,
      deliveredAt: Math.random() > 0.1 ? new Date(sentDate.getTime() + Math.random() * 60000) : null,
      errorMessage: Math.random() > 0.9 ? 'Network timeout' : null,
      relatedStudentId: student.id,
      messageType: messageType as any,
      provider: 'semaphore',
      messageId: `MSG_${Math.random().toString(36).substr(2, 9)}`,
      cost: 1.00
    })
  }

  await prisma.sMSLog.createMany({ data: smsLogs })
  console.log(`✅ Created ${smsLogs.length} SMS logs`)

  console.log('🌱 Seeding completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
