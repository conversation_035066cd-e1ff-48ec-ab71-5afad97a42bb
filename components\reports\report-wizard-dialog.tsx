"use client"

import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ReportWizard } from "./report-wizard"
import { ReportConfig } from "@/lib/types/reports"
import { Wand2 } from "lucide-react"
import { toast } from "sonner"

interface ReportWizardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onComplete?: (config: ReportConfig) => void
}

export function ReportWizardDialog({ 
  open, 
  onOpenChange, 
  onComplete 
}: ReportWizardDialogProps) {
  const handleComplete = (config: ReportConfig) => {
    onComplete?.(config)
    onOpenChange(false)
    toast.success("Report wizard completed", {
      description: "Your report configuration has been saved and generation started"
    })
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5" />
            Report Generation Wizard
          </DialogTitle>
          <DialogDescription>
            Follow the step-by-step guide to create your perfect report
          </DialogDescription>
        </DialogHeader>

        <ReportWizard
          onComplete={handleComplete}
          onCancel={handleCancel}
        />
      </DialogContent>
    </Dialog>
  )
}
