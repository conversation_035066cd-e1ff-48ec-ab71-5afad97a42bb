"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Users, Clock, Calendar, TrendingUp, TrendingDown } from "lucide-react"
import { cn } from "@/lib/utils"

interface AttendanceStatsProps {
  totalStudents: number
  presentToday: number
  lateToday: number
  absentToday: number
  attendanceRate: number
  className?: string
}

export function AttendanceStats({
  totalStudents,
  presentToday,
  lateToday,
  absentToday,
  attendanceRate,
  className
}: AttendanceStatsProps) {
  const stats = [
    {
      title: "Total Students",
      value: totalStudents.toLocaleString(),
      icon: Users,
      description: "Enrolled students",
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      change: "+2.5%",
      trend: "up"
    },
    {
      title: "Present Today",
      value: presentToday.toLocaleString(),
      icon: Calendar,
      description: `${attendanceRate}% attendance rate`,
      color: "text-green-600",
      bgColor: "bg-green-50",
      change: "+1.2%",
      trend: "up"
    },
    {
      title: "Late Arrivals",
      value: lateToday.toString(),
      icon: Clock,
      description: "Students arrived late",
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
      change: "-0.8%",
      trend: "down"
    },
    {
      title: "Absent Today",
      value: absentToday.toString(),
      icon: Users,
      description: `${((absentToday / totalStudents) * 100).toFixed(1)}% of total`,
      color: "text-red-600",
      bgColor: "bg-red-50",
      change: "+0.3%",
      trend: "up"
    }
  ]

  return (
    <div className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-4", className)}>
      {stats.map((stat, index) => {
        const Icon = stat.icon
        const TrendIcon = stat.trend === "up" ? TrendingUp : TrendingDown
        
        return (
          <Card key={index} className="relative overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <div className={cn("p-2 rounded-lg", stat.bgColor)}>
                <Icon className={cn("h-4 w-4", stat.color)} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {stat.description}
                  </p>
                </div>
                <div className="flex items-center space-x-1">
                  <TrendIcon 
                    className={cn(
                      "h-3 w-3",
                      stat.trend === "up" ? "text-green-500" : "text-red-500"
                    )} 
                  />
                  <span 
                    className={cn(
                      "text-xs font-medium",
                      stat.trend === "up" ? "text-green-500" : "text-red-500"
                    )}
                  >
                    {stat.change}
                  </span>
                </div>
              </div>
              
              {/* Progress bar for attendance rate */}
              {index === 1 && (
                <div className="mt-3">
                  <Progress value={attendanceRate} className="h-2" />
                </div>
              )}
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}

// Progress Ring Component
interface ProgressRingProps {
  value: number
  max: number
  size?: number
  strokeWidth?: number
  className?: string
  children?: React.ReactNode
}

export function ProgressRing({
  value,
  max,
  size = 120,
  strokeWidth = 8,
  className,
  children
}: ProgressRingProps) {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const percentage = (value / max) * 100
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (percentage / 100) * circumference

  return (
    <div className={cn("relative inline-flex items-center justify-center", className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-muted-foreground/20"
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="text-primary transition-all duration-300 ease-in-out"
          strokeLinecap="round"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        {children || (
          <div className="text-center">
            <div className="text-2xl font-bold">{percentage.toFixed(0)}%</div>
            <div className="text-xs text-muted-foreground">
              {value}/{max}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Real-time Counter Component
interface RealTimeCounterProps {
  value: number
  label: string
  trend?: "up" | "down" | "stable"
  className?: string
}

export function RealTimeCounter({
  value,
  label,
  trend = "stable",
  className
}: RealTimeCounterProps) {
  return (
    <div className={cn("text-center", className)}>
      <div className="text-3xl font-bold tabular-nums">
        {value.toLocaleString()}
      </div>
      <div className="text-sm text-muted-foreground">{label}</div>
      {trend !== "stable" && (
        <div className="flex items-center justify-center mt-1">
          {trend === "up" ? (
            <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
          ) : (
            <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
          )}
          <Badge 
            variant={trend === "up" ? "default" : "destructive"}
            className="text-xs"
          >
            {trend === "up" ? "↑" : "↓"}
          </Badge>
        </div>
      )}
    </div>
  )
}
