"use client"

import { useEffect, useRef, useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ScanNotification } from "@/lib/types/scanner"
import { CheckCircle, XCircle, AlertTriangle, Info, X, Volume2, VolumeX } from "lucide-react"
import { cn } from "@/lib/utils"

interface FeedbackSystemProps {
  notifications: ScanNotification[]
  onDismissNotification: (id: string) => void
  audioEnabled: boolean
  onToggleAudio: () => void
}

export function FeedbackSystem({
  notifications,
  onDismissNotification,
  audioEnabled,
  onToggleAudio
}: FeedbackSystemProps) {
  const audioContextRef = useRef<AudioContext | null>(null)
  const [isAudioInitialized, setIsAudioInitialized] = useState(false)

  // Initialize audio context
  useEffect(() => {
    const initAudio = () => {
      if (!audioContextRef.current && audioEnabled) {
        try {
          audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
          setIsAudioInitialized(true)
        } catch (error) {
          console.error("Failed to initialize audio context:", error)
        }
      }
    }

    // Initialize on user interaction
    const handleUserInteraction = () => {
      initAudio()
      document.removeEventListener('click', handleUserInteraction)
      document.removeEventListener('touchstart', handleUserInteraction)
    }

    document.addEventListener('click', handleUserInteraction)
    document.addEventListener('touchstart', handleUserInteraction)

    return () => {
      document.removeEventListener('click', handleUserInteraction)
      document.removeEventListener('touchstart', handleUserInteraction)
    }
  }, [audioEnabled])

  // Play sound notification
  const playSound = (type: 'success' | 'error' | 'warning' | 'info') => {
    if (!audioEnabled || !audioContextRef.current || !isAudioInitialized) return

    try {
      const ctx = audioContextRef.current
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Different frequencies for different notification types
      const frequencies = {
        success: [523.25, 659.25, 783.99], // C5, E5, G5 (major chord)
        error: [220, 185, 165], // A3, F#3, E3 (descending)
        warning: [440, 554.37], // A4, C#5
        info: [523.25] // C5
      }

      const freq = frequencies[type]
      const duration = type === 'success' ? 0.3 : type === 'error' ? 0.5 : 0.2

      freq.forEach((f, index) => {
        const osc = ctx.createOscillator()
        const gain = ctx.createGain()
        
        osc.connect(gain)
        gain.connect(ctx.destination)
        
        osc.frequency.setValueAtTime(f, ctx.currentTime + index * 0.1)
        osc.type = type === 'error' ? 'sawtooth' : 'sine'
        
        gain.gain.setValueAtTime(0, ctx.currentTime + index * 0.1)
        gain.gain.linearRampToValueAtTime(0.1, ctx.currentTime + index * 0.1 + 0.05)
        gain.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + index * 0.1 + duration)
        
        osc.start(ctx.currentTime + index * 0.1)
        osc.stop(ctx.currentTime + index * 0.1 + duration)
      })

      // Vibration for mobile devices
      if ('vibrator' in navigator || 'vibrate' in navigator) {
        const vibrationPatterns = {
          success: [100],
          error: [200, 100, 200],
          warning: [150],
          info: [50]
        }
        navigator.vibrate?.(vibrationPatterns[type])
      }
    } catch (error) {
      console.error("Failed to play sound:", error)
    }
  }

  // Play sound when new notifications arrive
  useEffect(() => {
    const latestNotification = notifications[notifications.length - 1]
    if (latestNotification && audioEnabled) {
      playSound(latestNotification.type)
    }
  }, [notifications, audioEnabled])

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      case 'info':
        return <Info className="h-5 w-5 text-blue-600" />
      default:
        return <Info className="h-5 w-5 text-gray-600" />
    }
  }

  const getNotificationStyle = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950'
      case 'error':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950'
      case 'info':
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950'
      default:
        return 'border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-950'
    }
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {/* Audio Control */}
      <div className="flex justify-end">
        <Button
          variant="outline"
          size="sm"
          onClick={onToggleAudio}
          className="bg-background/80 backdrop-blur-sm"
        >
          {audioEnabled ? (
            <Volume2 className="h-4 w-4" />
          ) : (
            <VolumeX className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Notifications */}
      {notifications.map((notification) => (
        <NotificationCard
          key={notification.id}
          notification={notification}
          onDismiss={() => onDismissNotification(notification.id)}
          icon={getNotificationIcon(notification.type)}
          className={getNotificationStyle(notification.type)}
        />
      ))}
    </div>
  )
}

interface NotificationCardProps {
  notification: ScanNotification
  onDismiss: () => void
  icon: React.ReactNode
  className: string
}

function NotificationCard({ notification, onDismiss, icon, className }: NotificationCardProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isExiting, setIsExiting] = useState(false)

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => setIsVisible(true), 10)
    
    // Auto dismiss
    if (notification.duration) {
      const dismissTimer = setTimeout(() => {
        handleDismiss()
      }, notification.duration)
      
      return () => {
        clearTimeout(timer)
        clearTimeout(dismissTimer)
      }
    }
    
    return () => clearTimeout(timer)
  }, [notification.duration])

  const handleDismiss = () => {
    setIsExiting(true)
    setTimeout(() => {
      onDismiss()
    }, 200)
  }

  return (
    <Card 
      className={cn(
        "border transition-all duration-200 transform",
        className,
        isVisible && !isExiting ? "translate-x-0 opacity-100" : "translate-x-full opacity-0",
        isExiting && "translate-x-full opacity-0"
      )}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 mt-0.5">
            {icon}
          </div>
          
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-semibold leading-tight">
              {notification.title}
            </h4>
            <p className="text-sm text-muted-foreground mt-1">
              {notification.message}
            </p>
            
            {notification.action && (
              <Button
                variant="link"
                size="sm"
                onClick={notification.action.handler}
                className="p-0 h-auto mt-2 text-xs"
              >
                {notification.action.label}
              </Button>
            )}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="flex-shrink-0 h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
        
        <div className="flex justify-between items-center mt-2 text-xs text-muted-foreground">
          <span>{notification.timestamp.toLocaleTimeString()}</span>
        </div>
      </CardContent>
    </Card>
  )
}
