"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { <PERSON>lider } from "@/components/ui/slider"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { 
  ScannerSettings, 
  CameraDevice, 
  Subject, 
  TimePeriod, 
  ScanMode 
} from "@/lib/types/scanner"
import { 
  Settings, 
  Camera, 
  Volume2, 
  VolumeX, 
  Vibrate, 
  Clock, 
  BookOpen, 
  Timer,
  Wifi,
  WifiOff,
  RotateCcw
} from "lucide-react"
import { cn } from "@/lib/utils"

interface SettingsPanelProps {
  settings: ScannerSettings
  onUpdateSettings: (settings: <PERSON><PERSON><ScannerSettings>) => void
  availableCameras: <PERSON><PERSON><PERSON><PERSON>[]
  subjects: Subject[]
  periods: TimePeriod[]
  selectedSubject?: string
  selectedPeriod?: string
  onSubjectChange: (subject: string) => void
  onPeriodChange: (period: string) => void
  scanMode: ScanMode
  onScanModeChange: (mode: ScanMode) => void
  isOnline: boolean
  className?: string
}

export function SettingsPanel({
  settings,
  onUpdateSettings,
  availableCameras,
  subjects,
  periods,
  selectedSubject,
  selectedPeriod,
  onSubjectChange,
  onPeriodChange,
  scanMode,
  onScanModeChange,
  isOnline,
  className
}: SettingsPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const handleSettingChange = (key: keyof ScannerSettings, value: any) => {
    onUpdateSettings({ [key]: value })
  }

  const resetToDefaults = () => {
    const defaultSettings: ScannerSettings = {
      audioEnabled: true,
      vibrationEnabled: true,
      scanDelay: 1000,
      autoAdvance: false,
      offlineMode: false,
      syncInterval: 5
    }
    onUpdateSettings(defaultSettings)
  }

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Scanner Settings
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? 'Collapse' : 'Expand'}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Scan Mode Selection */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Scan Mode</Label>
          <div className="grid grid-cols-3 gap-2">
            {(['gate', 'subject', 'batch'] as ScanMode[]).map((mode) => (
              <Button
                key={mode}
                variant={scanMode === mode ? 'default' : 'outline'}
                size="sm"
                onClick={() => onScanModeChange(mode)}
                className="capitalize"
              >
                {mode}
              </Button>
            ))}
          </div>
        </div>

        {/* Subject and Period Selection (for subject mode) */}
        {scanMode === 'subject' && (
          <div className="space-y-4">
            <Separator />
            
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Subject
              </Label>
              <Select value={selectedSubject} onValueChange={onSubjectChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id}>
                      <div className="flex flex-col">
                        <span>{subject.name}</span>
                        <span className="text-xs text-muted-foreground">{subject.code}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Timer className="h-4 w-4" />
                Time Period
              </Label>
              <Select value={selectedPeriod} onValueChange={onPeriodChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  {periods.map((period) => (
                    <SelectItem key={period.id} value={period.id}>
                      <div className="flex flex-col">
                        <span>{period.name}</span>
                        <span className="text-xs text-muted-foreground">
                          {period.startTime} - {period.endTime}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {isExpanded && (
          <>
            <Separator />

            {/* Camera Settings */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Camera className="h-4 w-4" />
                Camera Settings
              </h4>
              
              <div className="space-y-2">
                <Label>Camera Device</Label>
                <Select 
                  value={settings.cameraDeviceId} 
                  onValueChange={(value) => handleSettingChange('cameraDeviceId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select camera" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableCameras.map((camera) => (
                      <SelectItem key={camera.deviceId} value={camera.deviceId}>
                        {camera.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Scan Delay (ms)</Label>
                <div className="px-3">
                  <Slider
                    value={[settings.scanDelay]}
                    onValueChange={([value]) => handleSettingChange('scanDelay', value)}
                    max={5000}
                    min={500}
                    step={250}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>500ms</span>
                    <span>{settings.scanDelay}ms</span>
                    <span>5000ms</span>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Audio & Feedback Settings */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Audio & Feedback</h4>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {settings.audioEnabled ? (
                    <Volume2 className="h-4 w-4" />
                  ) : (
                    <VolumeX className="h-4 w-4" />
                  )}
                  <Label>Sound Notifications</Label>
                </div>
                <Switch
                  checked={settings.audioEnabled}
                  onCheckedChange={(checked) => handleSettingChange('audioEnabled', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Vibrate className="h-4 w-4" />
                  <Label>Vibration</Label>
                </div>
                <Switch
                  checked={settings.vibrationEnabled}
                  onCheckedChange={(checked) => handleSettingChange('vibrationEnabled', checked)}
                />
              </div>
            </div>

            <Separator />

            {/* Scanning Behavior */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Scanning Behavior</h4>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <Label>Auto Advance (Batch Mode)</Label>
                </div>
                <Switch
                  checked={settings.autoAdvance}
                  onCheckedChange={(checked) => handleSettingChange('autoAdvance', checked)}
                />
              </div>
            </div>

            <Separator />

            {/* Offline & Sync Settings */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium flex items-center gap-2">
                {isOnline ? (
                  <Wifi className="h-4 w-4 text-green-600" />
                ) : (
                  <WifiOff className="h-4 w-4 text-red-600" />
                )}
                Offline & Sync
              </h4>
              
              <div className="flex items-center justify-between">
                <Label>Offline Mode</Label>
                <Switch
                  checked={settings.offlineMode}
                  onCheckedChange={(checked) => handleSettingChange('offlineMode', checked)}
                />
              </div>

              <div className="space-y-2">
                <Label>Sync Interval (minutes)</Label>
                <div className="px-3">
                  <Slider
                    value={[settings.syncInterval]}
                    onValueChange={([value]) => handleSettingChange('syncInterval', value)}
                    max={60}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>1 min</span>
                    <span>{settings.syncInterval} min</span>
                    <span>60 min</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Badge variant={isOnline ? 'default' : 'destructive'} className="text-xs">
                  {isOnline ? 'Online' : 'Offline'}
                </Badge>
                {!isOnline && (
                  <span className="text-xs text-muted-foreground">
                    Data will sync when connection is restored
                  </span>
                )}
              </div>
            </div>

            <Separator />

            {/* Reset Settings */}
            <div className="flex justify-center">
              <Button variant="outline" onClick={resetToDefaults} size="sm">
                <RotateCcw className="mr-2 h-4 w-4" />
                Reset to Defaults
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
